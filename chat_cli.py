#!/usr/bin/env python3
"""
Interactive CLI Chat Program for HuggingFace and Local Models
Supports both base models and LoRA adapters
"""

import argparse
import os
import sys
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM, BitsAndBytesConfig
from peft import PeftModel
import warnings

warnings.filterwarnings("ignore")


class ChatBot:
    def __init__(
        self,
        model_path,
        system_prompt="You are a helpful assistant",
        use_4bit=False,
        max_length=2048,
    ):
        self.model_path = model_path
        self.system_prompt = system_prompt
        self.max_length = max_length
        self.conversation_history = []

        print(f"🤖 Loading model from: {model_path}")
        print(f"💭 System prompt: {system_prompt}")
        print(f"📏 Max length: {max_length}")

        # Load tokenizer and model
        self._load_model(use_4bit)

        # Initialize conversation with system prompt
        self._add_system_message()

        print("✅ Model loaded successfully!")
        print("💬 Type 'quit', 'exit', or 'q' to end the conversation")
        print("🔄 Type 'reset' to clear conversation history")
        print("📋 Type 'history' to see conversation history")
        print("=" * 60)

    def _load_model(self, use_4bit):
        """Load tokenizer and model (handles both base models and LoRA adapters)"""

        # Check if this is a LoRA adapter directory
        is_lora = os.path.exists(os.path.join(self.model_path, "adapter_config.json"))

        if is_lora:
            print("🔧 Detected LoRA adapter")
            self._load_lora_model(use_4bit)
        else:
            print("🔧 Loading base model")
            self._load_base_model(use_4bit)

    def _load_base_model(self, use_4bit):
        """Load a base model from HuggingFace or local path"""

        # Load tokenizer
        self.tokenizer = AutoTokenizer.from_pretrained(self.model_path)
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token

        # Configure quantization if requested
        quantization_config = None
        if use_4bit:
            quantization_config = BitsAndBytesConfig(
                load_in_4bit=True,
                bnb_4bit_compute_dtype=torch.bfloat16,
                bnb_4bit_use_double_quant=True,
                bnb_4bit_quant_type="nf4",
            )
            print("⚡ Using 4-bit quantization")

        # Load model
        self.model = AutoModelForCausalLM.from_pretrained(
            self.model_path,
            quantization_config=quantization_config,
            torch_dtype=torch.bfloat16 if not use_4bit else None,
            device_map="auto",
            trust_remote_code=True,
        )

    def _load_lora_model(self, use_4bit):
        """Load a LoRA adapter with its base model"""

        # Load tokenizer from adapter directory
        self.tokenizer = AutoTokenizer.from_pretrained(self.model_path)
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token

        # Load adapter config to get base model name
        import json

        adapter_config_path = os.path.join(self.model_path, "adapter_config.json")
        with open(adapter_config_path, "r") as f:
            adapter_config = json.load(f)

        base_model_name = adapter_config.get(
            "base_model_name_or_path", "meta-llama/Meta-Llama-3.1-8B"
        )
        print(f"📦 Base model: {base_model_name}")

        # Configure quantization if requested
        quantization_config = None
        if use_4bit:
            quantization_config = BitsAndBytesConfig(
                load_in_4bit=True,
                bnb_4bit_compute_dtype=torch.bfloat16,
                bnb_4bit_use_double_quant=True,
                bnb_4bit_quant_type="nf4",
            )
            print("⚡ Using 4-bit quantization")

        # Load base model
        base_model = AutoModelForCausalLM.from_pretrained(
            base_model_name,
            quantization_config=quantization_config,
            torch_dtype=torch.bfloat16 if not use_4bit else None,
            device_map="auto",
            trust_remote_code=True,
        )

        # Resize embeddings if tokenizer was extended
        if len(self.tokenizer) != base_model.config.vocab_size:
            print(
                f"🔧 Resizing embeddings: {base_model.config.vocab_size} → {len(self.tokenizer)}"
            )
            base_model.resize_token_embeddings(len(self.tokenizer))

        # Load LoRA adapter
        self.model = PeftModel.from_pretrained(base_model, self.model_path)
        print("🎯 LoRA adapter loaded")

    def _add_system_message(self):
        """Add system message to conversation history"""
        self.conversation_history = [{"role": "system", "content": self.system_prompt}]

    def _format_conversation(self):
        """Format conversation history for the model with translation token detection"""
        if hasattr(self.tokenizer, "apply_chat_template"):
            # Use the model's enhanced chat template if available
            return self.tokenizer.apply_chat_template(
                self.conversation_history, tokenize=False, add_generation_prompt=True
            )
        else:
            # Fallback to manual formatting with translation detection
            formatted = ""
            for message in self.conversation_history:
                if message["role"] == "system":
                    formatted += f"<|start_header_id|>system<|end_header_id|>\n\n{message['content']}<|eot_id|>"
                elif message["role"] == "user":
                    formatted += f"<|start_header_id|>user<|end_header_id|>\n\n{message['content']}<|eot_id|>"
                elif message["role"] == "assistant":
                    formatted += f"<|start_header_id|>assistant<|end_header_id|>\n\n{message['content']}<|eot_id|>"

            # Add generation prompt with English translation token (always)
            formatted += (
                "<|start_header_id|>assistant<|end_header_id|>\n\n<en_translation>"
            )

            return formatted

    def generate_response(self, user_input):
        """Generate response from the model"""

        # Add user message to history
        self.conversation_history.append({"role": "user", "content": user_input})

        # Format conversation
        prompt = self._format_conversation()

        # Tokenize
        inputs = self.tokenizer(
            prompt, return_tensors="pt", truncation=True, max_length=self.max_length
        )
        inputs = {k: v.to(self.model.device) for k, v in inputs.items()}

        # Generate response
        with torch.no_grad():
            # Check if this is a translation request to set appropriate EOS tokens
            eos_token_ids = [self.tokenizer.eos_token_id]

            # Add directional translation closing tokens as EOS
            if hasattr(self.tokenizer, "convert_tokens_to_ids"):
                try:
                    en_end_id = self.tokenizer.convert_tokens_to_ids(
                        "</en_translation>"
                    )
                    if en_end_id != self.tokenizer.unk_token_id:
                        eos_token_ids.append(en_end_id)

                    it_end_id = self.tokenizer.convert_tokens_to_ids(
                        "</it_translation>"
                    )
                    if it_end_id != self.tokenizer.unk_token_id:
                        eos_token_ids.append(it_end_id)
                except:
                    pass

            outputs = self.model.generate(
                **inputs,
                max_new_tokens=256,
                temperature=0.7,
                do_sample=True,
                top_p=0.9,
                repetition_penalty=1.1,
                pad_token_id=self.tokenizer.eos_token_id,
                eos_token_id=eos_token_ids,
            )

        # Extract only the new response (after the prompt)
        input_length = inputs["input_ids"].shape[1]
        new_tokens = outputs[0][input_length:]
        response = self.tokenizer.decode(new_tokens, skip_special_tokens=True).strip()

        # Clean up response - remove any repetitive patterns
        if response:
            # Split by lines and take first meaningful response
            lines = response.split("\n")
            clean_lines = []
            for line in lines:
                line = line.strip()
                if line and not line.startswith("👤") and not line.startswith("🤖"):
                    clean_lines.append(line)
                    if len(clean_lines) >= 3:  # Limit to first few lines
                        break
            response = "\n".join(clean_lines) if clean_lines else response

        # Add assistant response to history
        self.conversation_history.append({"role": "assistant", "content": response})

        return response

    def reset_conversation(self):
        """Reset conversation history"""
        self._add_system_message()
        print("🔄 Conversation history reset")

    def show_history(self):
        """Show conversation history"""
        print("\n📋 Conversation History:")
        print("-" * 40)
        for i, message in enumerate(self.conversation_history):
            role = message["role"].upper()
            content = message["content"]
            print(f"{i+1}. {role}: {content}")
        print("-" * 40)

    def chat(self):
        """Main chat loop"""
        while True:
            try:
                user_input = input("\n👤 You: ").strip()

                if user_input.lower() in ["quit", "exit", "q"]:
                    print("👋 Goodbye!")
                    break
                elif user_input.lower() == "reset":
                    self.reset_conversation()
                    continue
                elif user_input.lower() == "history":
                    self.show_history()
                    continue
                elif not user_input:
                    continue

                print("🤖 Assistant: ", end="", flush=True)
                response = self.generate_response(user_input)
                print(response)

            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")
                continue


def main():
    parser = argparse.ArgumentParser(
        description="Interactive CLI Chat with HuggingFace/Local Models"
    )
    parser.add_argument(
        "model_path", help="Path to model (HuggingFace model name or local path)"
    )
    parser.add_argument(
        "--system-prompt",
        default="You are a helpful assistant",
        help="System prompt for the assistant",
    )
    parser.add_argument("--4bit", action="store_true", help="Use 4-bit quantization")
    parser.add_argument(
        "--max-length", type=int, default=2048, help="Maximum sequence length"
    )

    args = parser.parse_args()

    print("🚀 Interactive Chat CLI")
    print("=" * 60)

    try:
        chatbot = ChatBot(
            model_path=args.model_path,
            system_prompt=args.system_prompt,
            use_4bit=getattr(args, "4bit", False),
            max_length=args.max_length,
        )
        chatbot.chat()

    except Exception as e:
        print(f"❌ Failed to load model: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
