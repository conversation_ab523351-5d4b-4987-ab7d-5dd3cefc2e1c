# Core ML/DL Frameworks
torch>=2.1.0
torchvision>=0.16.0
torchaudio>=2.1.0

# Transformers and Model Libraries
transformers>=4.36.0
tokenizers>=0.15.0
accelerate>=0.25.0

# Fine-tuning and LoRA
peft>=0.7.0
trl>=0.7.0
bitsandbytes>=0.41.0

# Dataset Handling
datasets>=2.16.0
evaluate>=0.4.0

# Experiment Tracking and Logging
wandb>=0.16.0
tensorboard>=2.15.0

# Optimization and Training
deepspeed>=0.12.0
flash-attn>=2.4.0
xformers>=0.0.23

# Data Processing and Utilities
numpy>=1.24.0
pandas>=2.1.0
scikit-learn>=1.3.0
scipy>=1.11.0

# Visualization and Analysis
matplotlib>=3.7.0
seaborn>=0.12.0
plotly>=5.17.0

# Development and Debugging
jupyter>=1.0.0
ipykernel>=6.26.0
tqdm>=4.66.0
rich>=13.7.0

# File Handling and Serialization
safetensors>=0.4.0
protobuf>=4.25.0
h5py>=3.10.0

# Distributed Training Support
mpi4py>=3.1.0
nccl>=2.18.0

# Text Processing
sentencepiece>=0.1.99
tiktoken>=0.5.0

# Additional Utilities for Long Sequences
ninja>=1.11.0
packaging>=23.2.0
psutil>=5.9.0

# Optional: For advanced memory optimization
# apex  # Uncomment if you want NVIDIA's mixed precision training
# fairscale>=0.4.0  # Uncomment for additional distributed training features

# Optional: For custom CUDA operations (if needed)
# triton>=2.1.0  # Uncomment if using custom CUDA kernels

# Development Quality Assurance
black>=23.11.0
isort>=5.12.0
flake8>=6.1.0
mypy>=1.7.0

