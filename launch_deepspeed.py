#!/usr/bin/env python3

import subprocess
import sys
import os

def main():
    """Launch DeepSpeed training"""
    
    # Set environment variables for optimal performance
    env = os.environ.copy()
    env.update({
        "CUDA_DEVICE_ORDER": "PCI_BUS_ID",
        "WANDB_PROJECT": "llama-translation-it-deepspeed",
    })
    
    # DeepSpeed command
    cmd = [
        "deepspeed",
        "--num_gpus=4",
        "--master_port=12355",
        "train_deepspeed.py"  # Your main training script
    ]
    
    print(f"🚀 Launching DeepSpeed with command: {' '.join(cmd)}")
    
    # Run DeepSpeed
    result = subprocess.run(cmd, env=env)
    
    if result.returncode == 0:
        print("✅ DeepSpeed training completed successfully!")
    else:
        print("❌ DeepSpeed training failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()

