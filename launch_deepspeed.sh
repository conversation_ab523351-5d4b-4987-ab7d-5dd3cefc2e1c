#!/bin/bash

# DeepSpeed launcher script for 32k token sequence training
# This script provides multiple ways to launch DeepSpeed training

echo "🚀 DeepSpeed Launcher for 32k Token Sequence Training"
echo "=================================================="

# Set environment variables for optimal performance
export CUDA_VISIBLE_DEVICES=0,1,2,3
export NCCL_DEBUG=INFO
export NCCL_P2P_DISABLE=1
export NCCL_IB_DISABLE=1
export NCCL_SOCKET_TIMEOUT=3600
export TORCH_NCCL_BLOCKING_WAIT=1
export TOKENIZERS_PARALLELISM=false

# Check available GPUs
NUM_GPUS=$(nvidia-smi --list-gpus | wc -l)
echo "Available GPUs: $NUM_GPUS"

# Method 1: Direct Python execution (uses mp.spawn internally)
echo ""
echo "Method 1: Direct Python execution"
echo "Command: python train.py"
echo ""

# Method 2: DeepSpeed launcher with automatic configuration
echo "Method 2: DeepSpeed launcher (recommended)"
echo "Command: deepspeed --num_gpus=$NUM_GPUS train.py"
echo ""

# Method 3: DeepSpeed launcher with specific config file
echo "Method 3: DeepSpeed launcher with ZeRO Stage 2"
echo "Command: deepspeed --num_gpus=$NUM_GPUS --deepspeed_config=deepspeed_config_zero2.json train.py"
echo ""

echo "Method 4: DeepSpeed launcher with ZeRO Stage 3 (maximum memory efficiency)"
echo "Command: deepspeed --num_gpus=$NUM_GPUS --deepspeed_config=deepspeed_config_zero3.json train.py"
echo ""

# Default execution - choose the best method based on available memory
echo "🎯 Executing recommended configuration..."

# Check GPU memory to decide on ZeRO stage
GPU_MEMORY=$(nvidia-smi --query-gpu=memory.total --format=csv,noheader,nounits | head -1)
echo "GPU Memory per device: ${GPU_MEMORY}MB"

if [ "$GPU_MEMORY" -lt 20000 ]; then
    echo "Using ZeRO Stage 3 for memory efficiency (GPU memory < 20GB)"
    deepspeed --num_gpus=$NUM_GPUS --deepspeed_config=deepspeed_config_zero3.json train.py
elif [ "$GPU_MEMORY" -lt 40000 ]; then
    echo "Using ZeRO Stage 2 for balanced performance (GPU memory < 40GB)"
    deepspeed --num_gpus=$NUM_GPUS --deepspeed_config=deepspeed_config_zero2.json train.py
else
    echo "Using direct execution for high-memory GPUs (GPU memory >= 40GB)"
    python train.py
fi
