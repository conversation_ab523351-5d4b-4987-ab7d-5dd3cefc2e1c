{"fp16": {"enabled": false}, "bf16": {"enabled": true}, "zero_optimization": {"stage": 3, "overlap_comm": true, "contiguous_gradients": true, "sub_group_size": 1000000000.0, "reduce_bucket_size": "auto", "stage3_prefetch_bucket_size": "auto", "stage3_param_persistence_threshold": "auto", "stage3_max_live_parameters": 1000000000.0, "stage3_max_reuse_distance": 1000000000.0, "stage3_gather_16bit_weights_on_model_save": true, "offload_optimizer": {"device": "cpu", "pin_memory": true, "pipeline_read": false, "pipeline_write": false, "fast_init": false}, "offload_param": {"device": "cpu", "pin_memory": true, "pipeline_read": false, "pipeline_write": false}}, "sequence_parallel_size": 4, "attention_type": "flash_attention_2", "activation_checkpointing": {"partition_activations": true, "cpu_checkpointing": true, "contiguous_memory_optimization": false, "number_checkpoints": 8, "synchronize_checkpoint_boundary": true, "profile": false}, "optimizer": {"type": "AdamW", "params": {"lr": 0.0001, "betas": [0.9, 0.95], "eps": 1e-08, "weight_decay": 0.01}}, "scheduler": {"type": "WarmupLR", "params": {"warmup_min_lr": 0, "warmup_max_lr": 0.0001, "warmup_num_steps": 100}}, "train_batch_size": "auto", "train_micro_batch_size_per_gpu": "auto", "gradient_accumulation_steps": "auto", "gradient_clipping": 1.0, "prescale_gradients": false, "wall_clock_breakdown": false, "memory_breakdown": false, "checkpoint": {"use_node_local_storage": true, "tag_validation": "Ignore"}, "tensorboard": {"enabled": true, "output_path": "./deepspeed-32k-output/tensorboard", "job_name": "deepspeed_32k_training"}}