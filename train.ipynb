{"cells": [{"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["import os\n", "import torch\n", "import torch.distributed as dist\n", "import torch.multiprocessing as mp\n", "from torch.nn.parallel import DistributedDataParallel as DDP\n", "from transformers import (\n", "    AutoTokenizer, \n", "    AutoModelForCausalLM,\n", "    TrainingArguments,\n", "    Trainer,\n", "    BitsAndBytesConfig\n", ")\n", "from peft import LoraConfig, get_peft_model, prepare_model_for_kbit_training\n", "from datasets import load_dataset\n", "import transformers\n", "import os\n", "\n", "# Set environment variables for distributed training\n", "os.environ['MASTER_ADDR'] = 'localhost'\n", "os.environ['MASTER_PORT'] = '12355'\n", "\n", "def setup(rank, world_size):\n", "    \"\"\"Initialize the process group for distributed training\"\"\"\n", "    dist.init_process_group(\"nccl\", rank=rank, world_size=world_size)\n", "    torch.cuda.set_device(rank)\n", "\n", "def cleanup():\n", "    \"\"\"Clean up the process group\"\"\"\n", "    dist.destroy_process_group()\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["def load_model_and_tokenizer(model_name=\"meta-llama/Meta-Llama-3.1-8B\", use_4bit=True):\n", "    \"\"\"Load model and tokenizer with quantization for memory efficiency\"\"\"\n", "    \n", "    # Configure quantization for memory efficiency[1]\n", "    if use_4bit:\n", "        bnb_config = BitsAndBytesConfig(\n", "            load_in_4bit=True,\n", "            bnb_4bit_use_double_quant=True,\n", "            bnb_4bit_quant_type=\"nf4\",\n", "            bnb_4bit_compute_dtype=torch.bfloat16\n", "        )\n", "    else:\n", "        bnb_config = None\n", "    \n", "    # Load tokenizer\n", "    tokenizer = AutoTokenizer.from_pretrained(model_name)\n", "    tokenizer.pad_token = tokenizer.eos_token\n", "    tokenizer.padding_side = \"right\"\n", "    \n", "    # Load model\n", "    model = AutoModelForCausalLM.from_pretrained(\n", "        model_name,\n", "        quantization_config=bnb_config,\n", "        torch_dtype=torch.bfloat16,\n", "        device_map=None,  # We'll handle device mapping manually for DDP\n", "        trust_remote_code=True\n", "    )\n", "    \n", "    return model, tokenizer\n", "\n", "def setup_lora(model, rank=0):\n", "    \"\"\"Setup LoRA configuration for efficient fine-tuning\"\"\"[1]\n", "    \n", "    # Prepare model for k-bit training if quantized\n", "    if hasattr(model, 'is_loaded_in_4bit') and model.is_loaded_in_4bit:\n", "        model = prepare_model_for_kbit_training(model)\n", "    \n", "    # LoRA configuration\n", "    lora_config = LoraConfig(\n", "        r=64,  # Rank\n", "        lora_alpha=16,\n", "        target_modules=[\n", "            \"q_proj\", \"k_proj\", \"v_proj\", \"o_proj\",\n", "            \"gate_proj\", \"up_proj\", \"down_proj\"\n", "        ],\n", "        lora_dropout=0.1,\n", "        bias=\"none\",\n", "        task_type=\"CAUSAL_LM\"\n", "    )\n", "    \n", "    model = get_peft_model(model, lora_config)\n", "    \n", "    if rank == 0:\n", "        model.print_trainable_parameters()\n", "    \n", "    return model\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def format_reasoning_chat_template(examples, tokenizer):\n", "    \"\"\"Format data specifically for reasoning models with <think> tokens\"\"\"[6]\n", "    \n", "    formatted_texts = []\n", "    \n", "    for i in range(len(examples['instruction'])):\n", "        # Create conversation format\n", "        conversation = [\n", "            {\"role\": \"user\", \"content\": examples['instruction'][i]}\n", "        ]\n", "        \n", "        # Apply chat template - critical for reasoning models[6]\n", "        formatted_chat = tokenizer.apply_chat_template(\n", "            conversation,\n", "            tokenize=False,\n", "            add_generation_prompt=True\n", "        )\n", "        \n", "        # Add the reasoning response with <think> tokens\n", "        # For reasoning models, responses should start with <think>[6]\n", "        if 'output' in examples:\n", "            response = f\"<think>\\n{examples.get('reasoning', 'Let me think about this...')}\\n</think>\\n{examples['output'][i]}\"\n", "        else:\n", "            response = f\"<think>\\nLet me think about this step by step.\\n</think>\\nI'll help you with that.\"\n", "        \n", "        full_text = formatted_chat + response + tokenizer.eos_token\n", "        formatted_texts.append(full_text)\n", "    \n", "    return {\"text\": formatted_texts}\n", "\n", "def prepare_dataset(dataset_name, tokenizer, max_length=4096):\n", "    \"\"\"Prepare and tokenize dataset for training\"\"\"\n", "    \n", "    # Load your reasoning dataset\n", "    dataset = load_dataset(dataset_name)\n", "    \n", "    # Format with chat template\n", "    dataset = dataset.map(\n", "        lambda x: format_reasoning_chat_template(x, tokenizer),\n", "        batched=True,\n", "        remove_columns=dataset['train'].column_names\n", "    )\n", "    \n", "    # Tokenize\n", "    def tokenize_function(examples):\n", "        tokenized = tokenizer(\n", "            examples[\"text\"],\n", "            truncation=True,\n", "            padding=False,\n", "            max_length=max_length,\n", "            return_tensors=None\n", "        )\n", "        tokenized[\"labels\"] = tokenized[\"input_ids\"].copy()\n", "        return tokenized\n", "    \n", "    dataset = dataset.map(tokenize_function, batched=True)\n", "    \n", "    return dataset\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def train_model(rank, world_size, dataset_name, model_name=\"meta-llama/Meta-Llama-3.1-8B\"):\n", "    \"\"\"Main training function for each GPU process\"\"\"[4]\n", "    \n", "    # Setup distributed training\n", "    setup(rank, world_size)\n", "    \n", "    print(f\"Starting training on GPU {rank}/{world_size}\")\n", "    \n", "    # Load model and tokenizer\n", "    model, tokenizer = load_model_and_tokenizer(model_name)\n", "    \n", "    # Move model to current GPU\n", "    model = model.to(rank)\n", "    \n", "    # Setup LoRA\n", "    model = setup_lora(model, rank)\n", "    \n", "    # Wrap with DDP[4]\n", "    model = DDP(model, device_ids=[rank], find_unused_parameters=True)\n", "    \n", "    # Prepare dataset\n", "    dataset = prepare_dataset(dataset_name, tokenizer)\n", "    \n", "    # Training arguments optimized for multi-GPU[1]\n", "    training_args = TrainingArguments(\n", "        output_dir=\"./llama-reasoning-finetuned\",\n", "        num_train_epochs=3,\n", "        per_device_train_batch_size=1,  # Small batch size for long sequences\n", "        gradient_accumulation_steps=4,  # Effective batch size = 1 * 4 * num_gpus\n", "        warmup_ratio=0.1,\n", "        learning_rate=2e-4,\n", "        fp16=False,\n", "        bf16=True,\n", "        logging_steps=10,\n", "        save_strategy=\"steps\",\n", "        save_steps=500,\n", "        evaluation_strategy=\"no\",\n", "        dataloader_num_workers=4,\n", "        remove_unused_columns=False,\n", "        ddp_find_unused_parameters=True,\n", "        gradient_checkpointing=True,  # Save memory\n", "        dataloader_pin_memory=False,\n", "        local_rank=rank,\n", "        deepspeed=None,  # Can be added for even better memory efficiency\n", "    )\n", "    \n", "    # Data collator\n", "    from transformers import DataCollatorForLanguageModeling\n", "    data_collator = DataCollatorForLanguageModeling(\n", "        tokenizer=tokenizer,\n", "        mlm=False,\n", "    )\n", "    \n", "    # Initialize trainer\n", "    trainer = Trainer(\n", "        model=model,\n", "        args=training_args,\n", "        train_dataset=dataset[\"train\"],\n", "        data_collator=data_collator,\n", "        tokenizer=tokenizer,\n", "    )\n", "    \n", "    # Start training\n", "    if rank == 0:\n", "        print(\"Starting training...\")\n", "    \n", "    trainer.train()\n", "    \n", "    # Save model (only on rank 0)\n", "    if rank == 0:\n", "        trainer.save_model()\n", "        print(\"Training completed and model saved!\")\n", "    \n", "    cleanup()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def launch_training():\n", "    \"\"\"Launch distributed training across multiple GPUs\"\"\"\n", "    \n", "    # Get number of available GPUs[3]\n", "    if 'CUDA_VISIBLE_DEVICES' in os.environ:\n", "        visible_devices = os.environ['CUDA_VISIBLE_DEVICES'].split(',')\n", "        world_size = len(visible_devices)\n", "        print(f\"Using GPUs: {visible_devices}\")\n", "    else:\n", "        world_size = torch.cuda.device_count()\n", "        print(f\"Using all {world_size} available GPUs\")\n", "    \n", "    if world_size < 2:\n", "        print(\"Warning: Multi-GPU training requires at least 2 GPUs\")\n", "        return\n", "    \n", "    # Your dataset name - replace with your reasoning dataset\n", "    dataset_name = \"your_reasoning_dataset\"  # Replace with actual dataset\n", "    \n", "    # Spawn processes for distributed training[4]\n", "    mp.spawn(\n", "        train_model,\n", "        args=(world_size, dataset_name),\n", "        nprocs=world_size,\n", "        join=True\n", "    )\n", "\n", "# Launch training\n", "if __name__ == \"__main__\":\n", "    launch_training()\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}