import os
import torch
import torch.distributed as dist
import torch.multiprocessing as mp
from torch.nn.parallel import DistributedDataParallel as DDP
from transformers import (
    AutoTokenizer,
    AutoModelForCausalLM,
    TrainingArguments,
    Trainer,
    BitsAndBytesConfig,
    DataCollatorForLanguageModeling,
)
from peft import LoraConfig, get_peft_model, prepare_model_for_kbit_training
from datasets import load_dataset
import transformers
import wandb

# Set environment variables for distributed training
os.environ["MASTER_ADDR"] = "localhost"
os.environ["MASTER_PORT"] = "12355"


def setup(rank, world_size):
    """Initialize the process group for distributed training"""
    dist.init_process_group("nccl", rank=rank, world_size=world_size)
    torch.cuda.set_device(rank)


def cleanup():
    """Clean up the process group"""
    dist.destroy_process_group()


def load_model_and_tokenizer(model_name="meta-llama/Meta-Llama-3.1-8B", use_4bit=True):
    """Load model and tokenizer with custom chat template"""

    # Configure quantization
    if use_4bit:
        bnb_config = BitsAndBytesConfig(
            load_in_4bit=True,
            bnb_4bit_use_double_quant=True,
            bnb_4bit_quant_type="nf4",
            bnb_4bit_compute_dtype=torch.bfloat16,
        )
    else:
        bnb_config = None

    # Load tokenizer
    tokenizer = AutoTokenizer.from_pretrained(model_name)
    tokenizer.pad_token = tokenizer.eos_token
    tokenizer.padding_side = "right"

    # Set up custom chat template with special tokens
    tokenizer = setup_custom_chat_template(tokenizer)

    # Load model
    model = AutoModelForCausalLM.from_pretrained(
        model_name,
        quantization_config=bnb_config,
        torch_dtype=torch.bfloat16,
        device_map=None,
        trust_remote_code=True,
    )

    # Resize token embeddings to account for new special tokens
    model.resize_token_embeddings(len(tokenizer))

    return model, tokenizer


def setup_lora(model, rank=0):
    """Setup LoRA configuration for efficient fine-tuning"""[1]

    # Prepare model for k-bit training if quantized
    if hasattr(model, "is_loaded_in_4bit") and model.is_loaded_in_4bit:
        model = prepare_model_for_kbit_training(model)

    # LoRA configuration
    lora_config = LoraConfig(
        r=64,  # Rank
        lora_alpha=16,
        target_modules=[
            "q_proj",
            "k_proj",
            "v_proj",
            "o_proj",
            "gate_proj",
            "up_proj",
            "down_proj",
        ],
        lora_dropout=0.1,
        bias="none",
        task_type="CAUSAL_LM",
    )

    model = get_peft_model(model, lora_config)

    if rank == 0:
        model.print_trainable_parameters()

    return model


def setup_custom_chat_template(tokenizer):
    """Set up custom chat template with translation and think tokens as content delimiters"""

    # Add special tokens if they don't exist[5]
    special_tokens = ["<translation>", "</translation>", "<think>", "</think>"]
    tokenizer.add_special_tokens({"additional_special_tokens": special_tokens})

    # Standard Llama chat template (tokens added manually in content)
    custom_template = """
{%- for message in messages %}
    {%- if message['role'] == 'system' %}
        {{- '<|start_header_id|>system<|end_header_id|>\n\n' + message['content'] + '<|eot_id|>' }}
    {%- elif message['role'] == 'user' %}
        {{- '<|start_header_id|>user<|end_header_id|>\n\n' + message['content'] + '<|eot_id|>' }}
    {%- elif message['role'] == 'assistant' %}
        {{- '<|start_header_id|>assistant<|end_header_id|>\n\n' + message['content'] + '<|eot_id|>' }}
    {%- endif %}
{%- endfor %}
{%- if add_generation_prompt %}
    {{- '<|start_header_id|>assistant<|end_header_id|>\n\n' }}
{%- endif %}
""".strip()

    tokenizer.chat_template = custom_template
    return tokenizer


def format_reasoning_chat_template(examples, tokenizer):
    """Format data specifically for reasoning models with translation and think tokens"""

    formatted_texts = []

    for i in range(len(examples["question"])):
        # Create conversation format
        conversation = [{"role": "user", "content": examples["question"][i]}]

        # Apply chat template - now it will work because we set the template
        formatted_chat = tokenizer.apply_chat_template(
            conversation, tokenize=False, add_generation_prompt=True
        )

        # Build response with translation and reasoning tokens
        if all(
            key in examples for key in ["question", "solution", "translation", "answer"]
        ):
            translation_content = examples["translation"][i]
            reasoning_content = examples["solution"][i]
            output_content = examples["answer"][i]

            # Format response with your special tokens
            response = f"<translation>{translation_content}</translation>\n\n<think>{reasoning_content}</think>\n\n{output_content}"
        else:
            continue

        # Combine formatted chat + response + eos
        full_text = formatted_chat + response + tokenizer.eos_token
        formatted_texts.append(full_text)

    return {"text": formatted_texts}


def prepare_dataset(dataset_name, tokenizer, max_length=10000):
    """Prepare and tokenize dataset with consistent handling"""

    # Load dataset
    dataset = load_dataset(dataset_name)

    # Format with chat template and filter out invalid samples
    def format_and_filter(examples):
        formatted = format_reasoning_chat_template(examples, tokenizer)

        # Filter out empty texts (from skipped samples)
        filtered_texts = [text for text in formatted["text"] if text.strip()]

        return {"text": filtered_texts}

    dataset = dataset.map(
        format_and_filter,
        batched=True,
        remove_columns=dataset["train"].column_names,
    )

    # Enhanced tokenization function
    def tokenize_function(examples):
        # Tokenize with consistent parameters
        tokenized = tokenizer(
            examples["text"],
            truncation=True,
            padding=True,
            max_length=max_length,
            return_tensors=None,
            add_special_tokens=False,  # Already in chat template
        )

        # Create labels (copy of input_ids for causal LM)
        tokenized["labels"] = [input_ids.copy() for input_ids in tokenized["input_ids"]]

        return tokenized

    # Apply tokenization
    dataset = dataset.map(
        tokenize_function,
        batched=True,
        remove_columns=["text"],
    )

    # Filter out sequences that are too long after tokenization
    def filter_length(example):
        return (
            len(example["input_ids"]) <= max_length
            and len(example["labels"]) <= max_length
        )

    dataset = dataset.filter(filter_length)

    return dataset


def train_model(
    rank, world_size, dataset_name, model_name="meta-llama/Meta-Llama-3.1-8B"
):
    """Main training function with checkpoint saving and wandb monitoring"""

    # Setup distributed training
    setup(rank, world_size)

    # Initialize wandb (only on rank 0 to avoid duplicate runs)[1]
    if rank == 0:
        wandb.init(
            project="llama-translation-it",
            name="limo",
            tags=["reasoning", "think-tokens", "lora", "multi-gpu"],
            config={
                "model_name": model_name,
                "dataset_name": dataset_name,
                "world_size": world_size,
                "method": "LoRA",
                "reasoning_tokens": True,
            },
        )

    print(f"Starting training on GPU {rank}/{world_size}")

    # Load model and tokenizer
    model, tokenizer = load_model_and_tokenizer(model_name)
    model = model.to(rank)
    model = setup_lora(model, rank)
    model.gradient_checkpointing_enable()
    model = DDP(model, device_ids=[rank], find_unused_parameters=True)

    # Prepare dataset
    dataset = prepare_dataset(dataset_name, tokenizer)

    # Enhanced training arguments with checkpointing and wandb[1][2]
    training_args = TrainingArguments(
        output_dir="./llama-reasoning-finetuned",
        num_train_epochs=3,
        per_device_train_batch_size=1,
        gradient_accumulation_steps=4,
        warmup_ratio=0.1,
        learning_rate=2e-4,
        fp16=False,
        bf16=True,
        # Wandb integration[1]
        report_to="wandb" if rank == 0 else "none",  # Enable wandb logging
        run_name="1" if rank == 0 else None,  # Optional run name
        logging_steps=10,  # Log metrics every 10 steps
        # Checkpoint saving configuration[2]
        save_strategy="steps",  # Save at regular intervals
        save_steps=500,  # Save every 500 steps
        save_total_limit=3,  # Keep only last 3 checkpoints
        # Evaluation configuration
        eval_strategy="steps",  # Evaluate at regular intervals
        eval_steps=500,  # Evaluate every 500 steps
        # Best model saving[1]
        load_best_model_at_end=True,
        metric_for_best_model="eval_loss",
        greater_is_better=False,
        # Other settings
        dataloader_num_workers=0,
        remove_unused_columns=False,
        ddp_find_unused_parameters=True,
        gradient_checkpointing=False,
        dataloader_pin_memory=False,
        local_rank=rank,
        deepspeed=None,
    )

    data_collator = DataCollatorForLanguageModeling(
        tokenizer=tokenizer,
        mlm=False,  # Not masked language modeling
        pad_to_multiple_of=8,  # Pad to multiple of 8 for efficiency
        return_tensors="pt",  # Return PyTorch tensors
    )

    # Initialize trainer
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=dataset["train"],
        eval_dataset=dataset["test"],
        data_collator=data_collator,
        tokenizer=tokenizer,
    )

    # Start training
    if rank == 0:
        print("Starting training with wandb monitoring and checkpoint saving...")

    trainer.train()

    # Save final model (only on rank 0)
    if rank == 0:
        trainer.save_model()
        print("Training completed and model saved!")
        wandb.finish()

    cleanup()


def launch_training():
    """Launch distributed training across multiple GPUs"""

    # Get number of available GPUs[3]
    if "CUDA_VISIBLE_DEVICES" in os.environ:
        visible_devices = os.environ["CUDA_VISIBLE_DEVICES"].split(",")
        world_size = len(visible_devices)
        print(f"Using GPUs: {visible_devices}")
    else:
        world_size = torch.cuda.device_count()
        print(f"Using all {world_size} available GPUs")

    if world_size < 2:
        print("Warning: Multi-GPU training requires at least 2 GPUs")
        return

    # Your dataset name - replace with your reasoning dataset
    dataset_name = "junkim100/limo_crosslingual_ko_en"

    # Spawn processes for distributed training[4]
    mp.spawn(train_model, args=(world_size, dataset_name), nprocs=world_size, join=True)


# Launch training
if __name__ == "__main__":
    launch_training()
