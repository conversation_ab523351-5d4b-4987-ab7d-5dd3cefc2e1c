import os
import gc
import traceback
import torch
import torch.distributed as dist
import torch.multiprocessing as mp
import deepspeed
from transformers import (
    AutoTokenizer,
    AutoModelForCausalLM,
    TrainingArguments,
    Trainer,
    BitsAndBytesConfig,
)
from peft import LoraConfig, get_peft_model, prepare_model_for_kbit_training
from datasets import load_dataset
import wandb
from typing import Dict
import time
import json

# Set environment variables for distributed training
os.environ["MASTER_ADDR"] = "localhost"
os.environ["MASTER_PORT"] = "12355"

# Memory optimization for 32k sequences
os.environ["PYTORCH_CUDA_ALLOC_CONF"] = "expandable_segments:True"
os.environ["TOKENIZERS_PARALLELISM"] = "false"  # Prevent tokenizer warnings
os.environ["TORCH_NCCL_BLOCKING_WAIT"] = "1"  # Use new NCCL environment variable


def get_gpu_memory_info(device_id: int) -> Dict[str, float]:
    """Get detailed GPU memory information for a specific device"""
    if torch.cuda.is_available() and device_id < torch.cuda.device_count():
        torch.cuda.set_device(device_id)
        total = torch.cuda.get_device_properties(device_id).total_memory / 1024**3  # GB
        allocated = torch.cuda.memory_allocated(device_id) / 1024**3  # GB
        cached = torch.cuda.memory_reserved(device_id) / 1024**3  # GB
        free = total - allocated
        usage_percent = (allocated / total) * 100

        return {
            "total": total,
            "allocated": allocated,
            "cached": cached,
            "free": free,
            "usage_percent": usage_percent,
        }
    return {"total": 0, "allocated": 0, "cached": 0, "free": 0, "usage_percent": 0}


def log_gpu_memory_usage(rank: int, world_size: int, stage: str = ""):
    """Log GPU memory usage across all devices"""
    if rank == 0:  # Only log from rank 0 to avoid spam
        print(f"\n=== GPU Memory Usage {stage} ===")
        for i in range(world_size):
            memory_info = get_gpu_memory_info(i)
            print(
                f"GPU {i}: {memory_info['allocated']:.2f}GB/{memory_info['total']:.2f}GB "
                f"({memory_info['usage_percent']:.1f}%) - Free: {memory_info['free']:.2f}GB"
            )
        print("=" * 50)


def setup(rank, world_size):
    """Fixed setup function with proper device assignment"""

    print(f"Process {rank}: Initializing distributed training...")

    # CRITICAL: Set device BEFORE any CUDA operations or process group init
    torch.cuda.set_device(rank)
    device = torch.device(f"cuda:{rank}")

    # Check if already initialized by DeepSpeed launcher
    if not dist.is_initialized():
        # Initialize process group AFTER setting device
        dist.init_process_group("nccl", rank=rank, world_size=world_size)
    else:
        print(f"Process {rank}: Distributed training already initialized by DeepSpeed")

    # Verify correct device assignment
    current_device = torch.cuda.current_device()
    print(f"✅ Process {rank}: Successfully assigned to GPU {current_device}")

    dist.barrier()

    if rank == 0:
        print(
            f"✅ Distributed training initialized successfully with {world_size} processes"
        )

    return device


def cleanup():
    """Clean up the process group with error handling"""
    try:
        if dist.is_initialized():
            dist.destroy_process_group()
    except Exception as e:
        print(f"Warning: Error during distributed cleanup: {e}")


def load_model_and_tokenizer(
    model_name="meta-llama/Meta-Llama-3.1-8B",
    use_4bit=False,  # Disable quantization for DeepSpeed compatibility
    target_device=None,
    rank=0,
    max_length=4096,
):
    """Load model and tokenizer optimized for DeepSpeed and long sequences"""

    print(f"Process {rank}: Loading model and tokenizer for max_length={max_length}")
    print(f"Process {rank}: DeepSpeed mode - quantization disabled for compatibility")

    # Note: DeepSpeed handles memory optimization, so we disable quantization
    # DeepSpeed's ZeRO stages provide better memory efficiency than quantization
    bnb_config = None

    # Load tokenizer with optimizations for long sequences
    tokenizer = AutoTokenizer.from_pretrained(
        model_name,
        model_max_length=max_length,  # Set maximum length
        padding_side="right",
        truncation_side="right",
        use_fast=True,  # Use fast tokenizer for better performance
    )

    # Set padding token
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token

    # Set up custom chat template with special tokens
    tokenizer = setup_custom_chat_template(tokenizer)

    print(f"Process {rank}: Tokenizer loaded with vocab size: {len(tokenizer)}")

    # Load model with DeepSpeed optimizations - CRITICAL: Keep on CPU for DeepSpeed
    print(f"Process {rank}: Loading model from {model_name}")
    model = AutoModelForCausalLM.from_pretrained(
        model_name,
        torch_dtype=torch.bfloat16,
        device_map=None,  # CRITICAL: Must be None for DeepSpeed
        trust_remote_code=True,
        low_cpu_mem_usage=True,  # Reduce CPU memory usage
        use_cache=False,  # Disable KV cache for training to save memory
    )

    # Resize token embeddings to account for new special tokens
    original_vocab_size = model.config.vocab_size
    new_vocab_size = len(tokenizer)
    if new_vocab_size != original_vocab_size:
        print(
            f"Process {rank}: Resizing token embeddings from {original_vocab_size} to {new_vocab_size}"
        )
        model.resize_token_embeddings(new_vocab_size)

    # CRITICAL: Keep model on CPU - DeepSpeed will handle device placement during initialization
    print(
        f"Process {rank}: Model loaded on CPU, DeepSpeed will handle device placement"
    )

    return model, tokenizer


def setup_lora(model, rank=0, max_length=4096):
    """Setup LoRA configuration optimized for DeepSpeed and long sequences"""

    print(f"Process {rank}: Setting up LoRA for max_length={max_length}")

    # Determine target modules based on model architecture
    model_type = model.config.model_type.lower()

    if "llama" in model_type:
        # Llama/Llama2/Llama3 target modules
        target_modules = [
            "q_proj",
            "k_proj",
            "v_proj",
            "o_proj",
            "gate_proj",
            "up_proj",
            "down_proj",
        ]
    elif "gpt" in model_type or "dialogpt" in model_type:
        # GPT-style models
        target_modules = ["c_attn", "c_proj", "c_fc"]
    elif "mistral" in model_type:
        # Mistral models
        target_modules = [
            "q_proj",
            "k_proj",
            "v_proj",
            "o_proj",
            "gate_proj",
            "up_proj",
            "down_proj",
        ]
    else:
        # Default: try to find attention and MLP layers
        target_modules = []
        for name, module in model.named_modules():
            if any(
                keyword in name.lower()
                for keyword in ["attn", "attention", "proj", "fc", "linear"]
            ):
                module_name = name.split(".")[-1]
                if module_name not in target_modules:
                    target_modules.append(module_name)

        if not target_modules:
            # Fallback to common patterns
            target_modules = ["q_proj", "v_proj"]

    print(f"Process {rank}: Using LoRA target modules: {target_modules}")

    # LoRA configuration optimized for 8k sequences - minimal rank for memory efficiency
    lora_config = LoraConfig(
        r=16,  # Minimal rank for 8k sequences to save memory
        lora_alpha=32,  # Scaling factor (2x rank)
        target_modules=target_modules,
        lora_dropout=0.05,  # Reduced dropout for stability with long sequences
        bias="none",
        task_type="CAUSAL_LM",
        inference_mode=False,  # Ensure training mode
    )

    model = get_peft_model(model, lora_config)

    # Note: DeepSpeed will handle gradient checkpointing through its config
    # We don't enable it manually here to avoid conflicts
    print(
        f"Process {rank}: LoRA setup complete, DeepSpeed will handle gradient checkpointing"
    )

    if rank == 0:
        model.print_trainable_parameters()

    return model


def setup_custom_chat_template(tokenizer):
    """Set up custom chat template with translation tokens as content delimiters"""

    # Add special tokens if they don't exist[5]
    special_tokens = ["<translation>", "</translation>"]
    tokenizer.add_special_tokens({"additional_special_tokens": special_tokens})

    # Standard Llama chat template (tokens added manually in content)
    custom_template = """
{%- for message in messages %}
    {%- if message['role'] == 'system' %}
        {{- '<|start_header_id|>system<|end_header_id|>\n\n' + message['content'] + '<|eot_id|>' }}
    {%- elif message['role'] == 'user' %}
        {{- '<|start_header_id|>user<|end_header_id|>\n\n' + message['content'] + '<|eot_id|>' }}
    {%- elif message['role'] == 'assistant' %}
        {{- '<|start_header_id|>assistant<|end_header_id|>\n\n' + message['content'] + '<|eot_id|>' }}
    {%- endif %}
{%- endfor %}
{%- if add_generation_prompt %}
    {{- '<|start_header_id|>assistant<|end_header_id|>\n\n' }}
{%- endif %}
""".strip()

    tokenizer.chat_template = custom_template
    return tokenizer


def format_reasoning_chat_template(examples, tokenizer, target_language):
    """Format data specifically for reasoning models with translation tokens"""

    formatted_texts = []

    input_column = f"{target_language}_input"
    output_column = f"{target_language}_output"

    for i in range(len(examples[input_column])):
        # Create conversation format
        conversation = [{"role": "user", "content": examples[input_column][i]}]

        # Apply chat template - now it will work because we set the template
        formatted_chat = tokenizer.apply_chat_template(
            conversation, tokenize=False, add_generation_prompt=True
        )

        # Build response with translation and reasoning tokens
        if all(
            key in examples
            for key in [input_column, output_column, "en_input", "en_output"]
        ):
            translation_input = examples["en_input"][i]
            output_content = examples["en_output"][i]
            translation_output = examples[output_column][i]

            # Format response with your special tokens
            response = f"<translation>{translation_input}</translation>\n\n{output_content}\n\n<translation>{translation_output}</translation>"
        else:
            continue

        # Combine formatted chat + response + eos
        full_text = formatted_chat + response + tokenizer.eos_token
        formatted_texts.append(full_text)

    return {"text": formatted_texts}


def prepare_dataset(
    dataset_name, tokenizer, max_length=4096, rank=0, target_language="it"
):
    """Prepare and tokenize dataset optimized for extremely long sequences"""

    print(
        f"Process {rank}: Preparing dataset {dataset_name} with max_length={max_length}"
    )

    # Load dataset
    dataset = load_dataset(dataset_name)

    print(
        f"Process {rank}: Dataset loaded - Train: {len(dataset['train'])}, Val: {len(dataset['val'])}"
    )

    # Format with chat template and filter out invalid samples
    def format_and_filter(examples):
        formatted = format_reasoning_chat_template(examples, tokenizer, target_language)

        # Filter out empty texts (from skipped samples)
        filtered_texts = [text for text in formatted["text"] if text.strip()]

        return {"text": filtered_texts}

    dataset = dataset.map(
        format_and_filter,
        batched=True,
        remove_columns=dataset["train"].column_names,
        desc="Formatting dataset",
    )

    # Enhanced tokenization function optimized for long sequences
    def tokenize_function(examples):
        # Tokenize with memory-efficient parameters
        tokenized = tokenizer(
            examples["text"],
            truncation=True,
            padding=False,  # Don't pad here - will pad in data collator
            max_length=max_length,
            return_tensors=None,
            add_special_tokens=False,  # Already in chat template
            return_attention_mask=True,
            return_length=True,
        )

        # Create labels (copy of input_ids for causal LM)
        tokenized["labels"] = [input_ids.copy() for input_ids in tokenized["input_ids"]]

        return tokenized

    # Apply tokenization with progress tracking
    dataset = dataset.map(
        tokenize_function,
        batched=True,
        remove_columns=["text"],
        desc="Tokenizing dataset",
        num_proc=1,  # Single process to avoid memory issues
    )

    # Filter sequences and log statistics
    def filter_length(example):
        length = len(example["input_ids"])
        return length <= max_length and length > 50  # Minimum length filter

    original_train_size = len(dataset["train"])
    original_val_size = len(dataset["val"])

    dataset = dataset.filter(filter_length)

    filtered_train_size = len(dataset["train"])
    filtered_val_size = len(dataset["val"])

    if rank == 0:
        print(f"Dataset filtering results:")
        print(
            f"  Train: {original_train_size} -> {filtered_train_size} "
            f"({filtered_train_size/original_train_size*100:.1f}% retained)"
        )
        print(
            f"  Val: {original_val_size} -> {filtered_val_size} "
            f"({filtered_val_size/original_val_size*100:.1f}% retained)"
        )

        # Log sequence length statistics
        train_lengths = [len(example["input_ids"]) for example in dataset["train"]]
        if train_lengths:
            print(
                f"  Sequence lengths - Min: {min(train_lengths)}, Max: {max(train_lengths)}, "
                f"Mean: {sum(train_lengths)/len(train_lengths):.1f}"
            )

    return dataset


class LongSequenceDataCollator:
    """Custom data collator optimized for extremely long sequences and memory efficiency"""

    def __init__(self, tokenizer, max_length=4096, device=None):
        self.tokenizer = tokenizer
        self.max_length = max_length
        self.device = device

    def __call__(self, features):
        # Extract input_ids and labels
        input_ids = [torch.tensor(f["input_ids"], dtype=torch.long) for f in features]
        labels = [torch.tensor(f["labels"], dtype=torch.long) for f in features]

        # Get actual lengths for efficient padding
        lengths = [len(ids) for ids in input_ids]
        max_batch_length = min(max(lengths), self.max_length)

        # Pad sequences to the maximum length in this batch (not global max)
        padded_input_ids = []
        padded_labels = []
        attention_masks = []

        for ids, lbls in zip(input_ids, labels):
            current_length = len(ids)

            if current_length > max_batch_length:
                # Truncate if necessary
                ids = ids[:max_batch_length]
                lbls = lbls[:max_batch_length]
                current_length = max_batch_length

            # Calculate padding needed
            padding_length = max_batch_length - current_length

            # Pad input_ids and labels
            if padding_length > 0:
                pad_token_id = self.tokenizer.pad_token_id
                ids = torch.cat(
                    [ids, torch.full((padding_length,), pad_token_id, dtype=torch.long)]
                )
                lbls = torch.cat(
                    [lbls, torch.full((padding_length,), -100, dtype=torch.long)]
                )  # -100 for ignored tokens

            # Create attention mask (1 for real tokens, 0 for padding)
            attention_mask = torch.cat(
                [
                    torch.ones(current_length, dtype=torch.long),
                    torch.zeros(padding_length, dtype=torch.long),
                ]
            )

            padded_input_ids.append(ids)
            padded_labels.append(lbls)
            attention_masks.append(attention_mask)

        # Stack into tensors
        batch = {
            "input_ids": torch.stack(padded_input_ids),
            "attention_mask": torch.stack(attention_masks),
            "labels": torch.stack(padded_labels),
        }

        # Move to device if specified
        if self.device is not None:
            batch = {
                k: v.to(self.device) if isinstance(v, torch.Tensor) else v
                for k, v in batch.items()
            }

        return batch


def train_model(
    rank,
    world_size,
    dataset_name,
    model_name="meta-llama/Meta-Llama-3.1-8B",
    target_language="it",
):
    """Main training function with DeepSpeed integration"""

    max_length = 4096  # Reduced to 4k for memory efficiency with DeepSpeed

    try:
        # Setup distributed training
        device = setup(rank, world_size)

        print(
            f"Process {rank}: Starting DeepSpeed training on {device} with max_length={max_length} (4k sequences)"
        )

        # Log initial GPU memory
        log_gpu_memory_usage(rank, world_size, "Before Model Loading")

        # Initialize wandb (only on rank 0)
        if rank == 0:
            wandb.init(
                project="llama-translation",
                name=f"{target_language}-{world_size}gpu-deepspeed",
                tags=[
                    "lora",
                    "deepspeed",
                    "multi-gpu",
                    "4k-sequences",
                ],
                config={
                    "model_name": model_name,
                    "dataset_name": dataset_name,
                    "world_size": world_size,
                    "max_length": max_length,
                    "method": "LoRA + DeepSpeed ZeRO-3",
                    "reasoning_tokens": True,
                    "deepspeed_stage": 3,
                },
            )

        # Load model and tokenizer for DeepSpeed
        model, tokenizer = load_model_and_tokenizer(
            model_name=model_name,
            use_4bit=False,  # Disabled for DeepSpeed compatibility
            target_device=None,  # DeepSpeed handles device placement
            rank=rank,
            max_length=max_length,
        )

        # Synchronization barrier after model loading
        dist.barrier()
        print(f"Process {rank}: Passed model loading barrier")

        # Log GPU memory after model loading
        log_gpu_memory_usage(rank, world_size, "After Model Loading")

        # Setup LoRA with DeepSpeed optimizations
        model = setup_lora(model, rank, max_length)

        print(f"Process {rank}: LoRA model prepared for DeepSpeed initialization")

        # Synchronization barrier after LoRA setup
        dist.barrier()
        print(f"Process {rank}: Passed LoRA setup barrier")

        # Prepare dataset with long sequence support
        dataset = prepare_dataset(
            dataset_name, tokenizer, max_length, rank, target_language
        )

        # Synchronization barrier after dataset preparation
        dist.barrier()
        print(f"Process {rank}: Dataset prepared and synchronized")

        # Enhanced training arguments optimized for DeepSpeed and long sequences
        training_args = TrainingArguments(
            output_dir="./llama-reasoning-finetuned-32k-deepspeed",
            num_train_epochs=2,  # Reduced epochs for 32k sequences
            per_device_train_batch_size=1,  # Must be 1 for 32k sequences
            gradient_accumulation_steps=8,  # Increased for effective batch size
            warmup_ratio=0.05,  # Reduced warmup for stability
            learning_rate=1e-4,  # Reduced learning rate for long sequences
            weight_decay=0.01,
            max_grad_norm=0.5,  # Gradient clipping for stability
            # Precision settings
            fp16=False,
            bf16=True,
            tf32=True,  # Enable TF32 for better performance
            # Memory optimizations - let DeepSpeed handle these
            gradient_checkpointing=False,  # DeepSpeed handles this
            dataloader_num_workers=0,  # Avoid multiprocessing issues
            dataloader_pin_memory=False,  # Reduce memory pressure
            remove_unused_columns=False,
            # DeepSpeed integration - use ZeRO Stage 3 for better memory efficiency
            deepspeed="deepspeed_config_zero3.json",  # Use ZeRO Stage 3 for 4k sequences
            local_rank=rank,
            # Logging and monitoring
            report_to="wandb" if rank == 0 else "none",
            run_name=(
                f"{target_language}-{world_size}gpu-deepspeed" if rank == 0 else None
            ),
            logging_steps=5,  # More frequent logging
            logging_first_step=True,
            # Checkpoint saving
            save_strategy="steps",
            save_steps=100,  # More frequent saves for long training
            save_total_limit=2,  # Keep fewer checkpoints to save disk space
            # Evaluation
            eval_strategy="steps",
            eval_steps=100,
            eval_accumulation_steps=1,  # Reduce eval memory usage
            # Performance optimizations
            group_by_length=False,  # Disable for consistent memory usage
            length_column_name="length",
            # Memory management
            skip_memory_metrics=False,  # Monitor memory usage
        )

        # Use custom data collator optimized for long sequences
        data_collator = LongSequenceDataCollator(
            tokenizer=tokenizer,
            max_length=max_length,
            device=None,  # DeepSpeed will handle device placement
        )

        # Log GPU memory before trainer initialization
        log_gpu_memory_usage(rank, world_size, "Before Trainer Init")

        # Initialize trainer with DeepSpeed integration
        trainer = Trainer(
            model=model,
            args=training_args,
            train_dataset=dataset["train"],
            eval_dataset=dataset["val"],
            data_collator=data_collator,
            tokenizer=tokenizer,
        )

        # Final synchronization before training
        dist.barrier()

        # Log GPU memory after DeepSpeed initialization
        log_gpu_memory_usage(rank, world_size, "After DeepSpeed Init")

        # Start training
        if rank == 0:
            print(
                f"🚀 Starting DeepSpeed training for 4k sequences on {world_size} GPUs..."
            )
            print(f"   - Max sequence length: {max_length}")
            print(f"   - DeepSpeed ZeRO Stage: 3 (Maximum Memory Efficiency)")
            print(
                f"   - Batch size per device: {training_args.per_device_train_batch_size}"
            )
            print(
                f"   - Gradient accumulation steps: {training_args.gradient_accumulation_steps}"
            )
            print(
                f"   - Effective batch size: {training_args.per_device_train_batch_size * training_args.gradient_accumulation_steps * world_size}"
            )

        # Train with memory monitoring
        start_time = time.time()
        trainer.train()
        training_time = time.time() - start_time

        # Log final GPU memory usage
        log_gpu_memory_usage(rank, world_size, "After Training Complete")

        # Save final model (only on rank 0)
        if rank == 0:
            print(f"✅ DeepSpeed training completed in {training_time/3600:.2f} hours!")
            trainer.save_model()
            print("Model saved successfully!")

            # Log final metrics
            wandb.log(
                {
                    "training_time_hours": training_time / 3600,
                    "final_memory_usage": get_gpu_memory_info(0)["usage_percent"],
                    "deepspeed_stage": 2,
                }
            )
            wandb.finish()

    except Exception as e:
        print(f"❌ Process {rank}: Error during training: {e}")
        traceback.print_exc()
        raise e
    finally:
        try:
            cleanup()
        except Exception as cleanup_error:
            print(f"Process {rank}: Error during cleanup: {cleanup_error}")


def launch_training():
    """Launch DeepSpeed training across multiple GPUs using DeepSpeed launcher"""

    print("🚀 Please use DeepSpeed launcher to run this script:")
    print("=" * 70)
    print("Command: deepspeed --num_gpus=4 train.py")
    print("=" * 70)
    print()
    print("This script is designed to work with DeepSpeed launcher only.")
    print("Using mp.spawn with DeepSpeed causes device placement conflicts.")
    print()
    print("If you want to run without DeepSpeed launcher, please:")
    print("1. Remove DeepSpeed configuration from TrainingArguments")
    print("2. Use standard PyTorch DDP instead")
    print()

    # Validate CUDA availability
    if not torch.cuda.is_available():
        raise RuntimeError("CUDA is not available. This script requires GPU support.")

    # Get number of available GPUs
    world_size = torch.cuda.device_count()
    print(f"📍 Available GPUs: {world_size}")

    # Validate GPU memory
    print(f"\n🔍 GPU Memory Validation:")
    total_memory = 0
    for i in range(world_size):
        if torch.cuda.is_available() and i < torch.cuda.device_count():
            props = torch.cuda.get_device_properties(i)
            memory_gb = props.total_memory / 1024**3
            total_memory += memory_gb
            print(f"   GPU {i}: {props.name} - {memory_gb:.1f}GB")

    print(f"   Total GPU Memory: {total_memory:.1f}GB")

    # Estimate memory requirements for 4k sequences
    estimated_memory_per_gpu = 10  # GB estimate for 4k sequences with LoRA
    if total_memory / world_size < estimated_memory_per_gpu:
        print(
            f"⚠️  Warning: Each GPU has {total_memory/world_size:.1f}GB, but ~{estimated_memory_per_gpu}GB recommended for 4k sequences"
        )
    else:
        print(
            f"✅ Memory validation passed: {total_memory/world_size:.1f}GB per GPU >= {estimated_memory_per_gpu}GB required"
        )

    print(f"\n📊 Training Configuration:")
    print(f"   Dataset: junkim100/Lima-X-Processed")
    print(f"   Model: meta-llama/Meta-Llama-3.1-8B")
    print(f"   Max Sequence Length: 8,192 tokens")
    print(f"   Optimization: LoRA + DeepSpeed ZeRO Stage 3 + Custom Data Collator")

    print(f"\n🚀 To start training, run:")
    print(f"   deepspeed --num_gpus={world_size} train.py")

    return


# Launch training
if __name__ == "__main__":
    # Check if launched by DeepSpeed launcher
    import sys

    if "--local_rank" in sys.argv or "LOCAL_RANK" in os.environ:
        # Launched by DeepSpeed launcher - run training directly
        print("🚀 Detected DeepSpeed launcher - running training directly")

        # Get local rank from command line or environment
        local_rank = None
        for i, arg in enumerate(sys.argv):
            if arg == "--local_rank" and i + 1 < len(sys.argv):
                local_rank = int(sys.argv[i + 1])
                break

        if local_rank is None:
            local_rank = int(os.environ.get("LOCAL_RANK", 0))

        world_size = int(os.environ.get("WORLD_SIZE", 4))

        # Run training directly
        train_model(
            rank=local_rank,
            world_size=world_size,
            dataset_name="junkim100/Lima-X-Processed",
            model_name="meta-llama/Meta-Llama-3.1-8B",
            target_language="it",
        )
    else:
        # Not launched by DeepSpeed - use mp.spawn
        launch_training()
