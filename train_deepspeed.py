import os
import json
import torch
from transformers import (
    AutoTokenizer,
    AutoModelForCausalLM,
    TrainingArguments,
    Trainer,
    BitsAndBytesConfig,
)
from peft import LoraConfig, get_peft_model, prepare_model_for_kbit_training
from datasets import load_dataset
import wandb
from dataclasses import dataclass, field
from typing import List, Optional
import deepspeed

# DeepSpeed configuration optimized for 32K sequences
def create_deepspeed_config(output_dir: str, max_length: int = 32159):
    """Create DeepSpeed configuration optimized for long sequences"""
    
    # Based on search results [3] - DeepSpeed-Ulysses for extremely long sequences
    config = {
        "fp16": {
            "enabled": False
        },
        "bf16": {
            "enabled": True
        },
        "zero_optimization": {
            "stage": 3,  # ZeRO-3 for parameter sharding across GPUs
            "overlap_comm": True,
            "contiguous_gradients": True,
            "sub_group_size": 1e9,
            "reduce_bucket_size": "auto",
            "stage3_prefetch_bucket_size": "auto", 
            "stage3_param_persistence_threshold": "auto",
            "stage3_max_live_parameters": 1e9,
            "stage3_max_reuse_distance": 1e9,
            "stage3_gather_16bit_weights_on_model_save": True,
            # CPU offloading for memory efficiency with long sequences
            "offload_optimizer": {
                "device": "cpu",
                "pin_memory": True,
                "pipeline_read": False,
                "pipeline_write": False,
                "fast_init": False
            },
            "offload_param": {
                "device": "cpu",
                "pin_memory": True,
                "pipeline_read": False,
                "pipeline_write": False
            }
        },
        # DeepSpeed-Ulysses for sequence parallelism (search result [3])
        "sequence_parallel_size": 4,  # Number of GPUs for sequence parallelism
        "attention_type": "flash_attention_2",  # Efficient attention for long sequences
        
        # Activation checkpointing optimized for long sequences
        "activation_checkpointing": {
            "partition_activations": True,
            "cpu_checkpointing": True,  # Move activations to CPU for very long sequences
            "contiguous_memory_optimization": False,
            "number_checkpoints": 8,  # More checkpoints for 32K sequences
            "synchronize_checkpoint_boundary": True,
            "profile": False
        },
        
        # Optimizer configuration
        "optimizer": {
            "type": "AdamW",
            "params": {
                "lr": 1e-4,
                "betas": [0.9, 0.95],
                "eps": 1e-8,
                "weight_decay": 0.01
            }
        },
        
        # Scheduler configuration
        "scheduler": {
            "type": "WarmupLR",
            "params": {
                "warmup_min_lr": 0,
                "warmup_max_lr": 1e-4,
                "warmup_num_steps": 100
            }
        },
        
        # Batch size configuration
        "train_batch_size": "auto",  # Let DeepSpeed calculate
        "train_micro_batch_size_per_gpu": "auto",
        "gradient_accumulation_steps": "auto",
        
        # Memory and communication optimizations
        "gradient_clipping": 1.0,
        "prescale_gradients": False,
        "wall_clock_breakdown": False,
        "memory_breakdown": False,
        
        # Checkpointing
        "checkpoint": {
            "use_node_local_storage": True,
            "tag_validation": "Ignore"
        },
        
        # Logging
        "tensorboard": {
            "enabled": True,
            "output_path": f"{output_dir}/tensorboard",
            "job_name": "deepspeed_32k_training"
        }
    }
    
    return config

@dataclass
class ModelArguments:
    """Model arguments for DeepSpeed training"""
    model_name_or_path: str = "meta-llama/Meta-Llama-3.1-8B"
    use_4bit: bool = True
    model_max_length: int = 32159  # Based on search result [1]

@dataclass
class DataArguments:
    """Data arguments"""
    dataset_name: str = "junkim100/limo_crosslingual_ko_en"
    max_seq_length: int = 32159

@dataclass  
class LoraArguments:
    """LoRA arguments based on search result [1]"""
    use_lora: bool = True
    lora_r: int = 64  # Rank for LoRA
    lora_alpha: int = 16  # Alpha value for LoRA  
    lora_dropout: float = 0.05  # Dropout rate for LoRA
    lora_target_modules: List[str] = field(
        default_factory=lambda: [
            "q_proj",
            "k_proj", 
            "v_proj",
            "o_proj",
            "up_proj",
            "gate_proj",
            "down_proj",
        ]
    )
    lora_weight_path: str = ""
    lora_bias: str = "none"
    q_lora: bool = True  # Enable QLoRA based on search result [5]

def setup_custom_chat_template(tokenizer):
    """Set up custom chat template with translation and think tokens"""
    
    # Add special tokens
    special_tokens = ["<translation>", "</translation>", "<think>", "</think>"]
    tokenizer.add_special_tokens({"additional_special_tokens": special_tokens})
    
    # Standard Llama chat template
    custom_template = """
{%- for message in messages %}
    {%- if message['role'] == 'system' %}
        {{- '<|start_header_id|>system<|end_header_id|>\n\n' + message['content'] + '<|eot_id|>' }}
    {%- elif message['role'] == 'user' %}
        {{- '<|start_header_id|>user<|end_header_id|>\n\n' + message['content'] + '<|eot_id|>' }}
    {%- elif message['role'] == 'assistant' %}
        {{- '<|start_header_id|>assistant<|end_header_id|>\n\n' + message['content'] + '<|eot_id|>' }}
    {%- endif %}
{%- endfor %}
{%- if add_generation_prompt %}
    {{- '<|start_header_id|>assistant<|end_header_id|>\n\n' }}
{%- endif %}
""".strip()
    
    tokenizer.chat_template = custom_template
    return tokenizer

def format_reasoning_chat_template(examples, tokenizer):
    """Format data for reasoning models with translation and think tokens"""
    
    formatted_texts = []
    
    for i in range(len(examples["question"])):
        try:
            # Create conversation format
            conversation = [{"role": "user", "content": examples["question"][i]}]
            
            # Apply chat template
            formatted_chat = tokenizer.apply_chat_template(
                conversation, tokenize=False, add_generation_prompt=True
            )
            
            # Build response with translation and reasoning tokens
            if all(key in examples for key in ["question", "solution", "translation", "answer"]):
                translation_content = examples["translation"][i]
                reasoning_content = examples["solution"][i]
                output_content = examples["answer"][i]
                
                # Format response with special tokens
                response = f"<translation>{translation_content}</translation>\n\n<think>{reasoning_content}</think>\n\n{output_content}"
                
                # Combine formatted chat + response + eos
                full_text = formatted_chat + response + tokenizer.eos_token
                formatted_texts.append(full_text)
                
        except Exception as e:
            print(f"Error processing sample {i}: {e}")
            continue
    
    return {"text": formatted_texts}

def prepare_dataset(dataset_name: str, tokenizer, max_length: int = 32159):
    """Prepare dataset for DeepSpeed training"""
    
    print(f"Loading dataset: {dataset_name}")
    
    # Load dataset
    dataset = load_dataset(dataset_name)
    
    # Format with chat template
    def format_and_filter(examples):
        return format_reasoning_chat_template(examples, tokenizer)
    
    dataset = dataset.map(
        format_and_filter,
        batched=True,
        remove_columns=dataset["train"].column_names,
        desc="Formatting dataset"
    )
    
    # Filter out empty samples
    dataset = dataset.filter(lambda x: len(x["text"]) > 0)
    
    # Tokenization function optimized for long sequences
    def tokenize_function(examples):
        tokenized = tokenizer(
            examples["text"],
            truncation=True,
            padding=False,  # DeepSpeed will handle padding
            max_length=max_length,
            return_tensors=None,
            add_special_tokens=False,
        )
        
        # Create labels
        tokenized["labels"] = [input_ids.copy() for input_ids in tokenized["input_ids"]]
        
        return tokenized
    
    # Apply tokenization
    dataset = dataset.map(
        tokenize_function,
        batched=True,
        remove_columns=["text"],
        desc="Tokenizing dataset"
    )
    
    # Filter by length
    def filter_length(example):
        return len(example["input_ids"]) <= max_length and len(example["input_ids"]) > 50
    
    dataset = dataset.filter(filter_length)
    
    print(f"Dataset prepared: {len(dataset['train'])} train, {len(dataset['test'])} test")
    
    return dataset

def load_model_and_tokenizer(model_args: ModelArguments):
    """Load model and tokenizer with QLoRA configuration"""
    
    # Configure quantization for QLoRA (search result [5])
    if model_args.use_4bit:
        bnb_config = BitsAndBytesConfig(
            load_in_4bit=True,
            bnb_4bit_use_double_quant=True,
            bnb_4bit_quant_type="nf4",
            bnb_4bit_compute_dtype=torch.bfloat16,
        )
    else:
        bnb_config = None
    
    # Load tokenizer
    tokenizer = AutoTokenizer.from_pretrained(
        model_args.model_name_or_path,
        model_max_length=model_args.model_max_length,
        padding_side="right",
        use_fast=True,
    )
    
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    # Setup custom chat template
    tokenizer = setup_custom_chat_template(tokenizer)
    
    # Load model
    model = AutoModelForCausalLM.from_pretrained(
        model_args.model_name_or_path,
        quantization_config=bnb_config,
        torch_dtype=torch.bfloat16,
        device_map=None,  # DeepSpeed will handle device placement
        trust_remote_code=True,
        use_cache=False,  # Disable for training
    )
    
    # Resize token embeddings
    if len(tokenizer) != model.config.vocab_size:
        model.resize_token_embeddings(len(tokenizer))
        print(f"Resized embeddings from {model.config.vocab_size} to {len(tokenizer)}")
    
    return model, tokenizer

def setup_lora(model, lora_args: LoraArguments):
    """Setup LoRA with QLoRA configuration"""
    
    # Prepare model for k-bit training
    if hasattr(model, "is_loaded_in_4bit") and model.is_loaded_in_4bit:
        model = prepare_model_for_kbit_training(model)
    
    # LoRA configuration based on search result [1]
    lora_config = LoraConfig(
        r=lora_args.lora_r,
        lora_alpha=lora_args.lora_alpha,
        target_modules=lora_args.lora_target_modules,
        lora_dropout=lora_args.lora_dropout,
        bias=lora_args.lora_bias,
        task_type="CAUSAL_LM",
        inference_mode=False,
    )
    
    model = get_peft_model(model, lora_config)
    model.print_trainable_parameters()
    
    return model

class DeepSpeedDataCollator:
    """Custom data collator for DeepSpeed with long sequences"""
    
    def __init__(self, tokenizer, max_length: int = 32159):
        self.tokenizer = tokenizer
        self.max_length = max_length
    
    def __call__(self, features):
        # Extract sequences
        input_ids = [f["input_ids"] for f in features]
        labels = [f["labels"] for f in features]
        
        # Find max length in batch
        batch_max_length = min(max(len(seq) for seq in input_ids), self.max_length)
        
        # Pad sequences
        padded_input_ids = []
        padded_labels = []
        attention_masks = []
        
        for inp, lab in zip(input_ids, labels):
            # Truncate if necessary
            inp = inp[:batch_max_length]
            lab = lab[:batch_max_length]
            
            # Pad
            pad_length = batch_max_length - len(inp)
            padded_inp = inp + [self.tokenizer.pad_token_id] * pad_length
            padded_lab = lab + [-100] * pad_length
            attention_mask = [1] * len(inp) + [0] * pad_length
            
            padded_input_ids.append(padded_inp)
            padded_labels.append(padded_lab)
            attention_masks.append(attention_mask)
        
        return {
            "input_ids": torch.tensor(padded_input_ids, dtype=torch.long),
            "labels": torch.tensor(padded_labels, dtype=torch.long),
            "attention_mask": torch.tensor(attention_masks, dtype=torch.long),
        }

def main():
    """Main training function with DeepSpeed"""
    
    # Initialize arguments
    model_args = ModelArguments()
    data_args = DataArguments()
    lora_args = LoraArguments()
    
    # Create DeepSpeed config
    ds_config = create_deepspeed_config("./deepspeed-32k-output", model_args.model_max_length)
    
    # Save DeepSpeed config
    with open("ds_config.json", "w") as f:
        json.dump(ds_config, f, indent=2)
    
    # Initialize wandb
    wandb.init(
        project="llama-translation-it-deepspeed",
        name="deepspeed-32k-reasoning",
        tags=["deepspeed", "32k-sequences", "lora", "reasoning", "ulysses"],
        config={
            "model_name": model_args.model_name_or_path,
            "dataset_name": data_args.dataset_name,
            "max_length": model_args.model_max_length,
            "method": "DeepSpeed + QLoRA",
            "sequence_parallel": True,
        }
    )
    
    # Load model and tokenizer
    print("Loading model and tokenizer...")
    model, tokenizer = load_model_and_tokenizer(model_args)
    
    # Setup LoRA
    print("Setting up LoRA...")
    model = setup_lora(model, lora_args)
    
    # Prepare dataset
    print("Preparing dataset...")
    dataset = prepare_dataset(data_args.dataset_name, tokenizer, data_args.max_seq_length)
    
    # Training arguments for DeepSpeed
    training_args = TrainingArguments(
        output_dir="./deepspeed-32k-output",
        num_train_epochs=3,
        per_device_train_batch_size=1,  # Will be auto-adjusted by DeepSpeed
        gradient_accumulation_steps=8,
        learning_rate=1e-4,
        warmup_ratio=0.1,
        weight_decay=0.01,
        
        # DeepSpeed integration
        deepspeed="ds_config.json",  # Use our DeepSpeed config
        bf16=True,
        dataloader_drop_last=True,
        
        # Logging and evaluation  
        logging_steps=10,
        eval_steps=500,
        save_steps=500,
        save_total_limit=3,
        evaluation_strategy="steps",
        
        # Optimization for long sequences
        max_grad_norm=1.0,
        remove_unused_columns=False,
        
        # Wandb integration
        report_to="wandb",
        run_name="deepspeed-32k-reasoning",
    )
    
    # Data collator
    data_collator = DeepSpeedDataCollator(tokenizer, data_args.max_seq_length)
    
    # Initialize Trainer with DeepSpeed
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=dataset["train"],
        eval_dataset=dataset["test"],
        data_collator=data_collator,
        tokenizer=tokenizer,
    )
    
    # Start training
    print("🚀 Starting DeepSpeed training with 32K sequences...")
    print(f"   - Model: {model_args.model_name_or_path}")
    print(f"   - Max sequence length: {model_args.model_max_length}")
    print(f"   - DeepSpeed ZeRO Stage: 3")
    print(f"   - Sequence Parallelism: Enabled (Ulysses)")
    print(f"   - QLoRA: Enabled")
    
    trainer.train()
    
    # Save final model
    trainer.save_model()
    
    print("✅ Training completed!")
    wandb.finish()

if __name__ == "__main__":
    main()

