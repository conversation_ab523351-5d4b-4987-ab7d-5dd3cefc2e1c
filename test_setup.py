#!/usr/bin/env python3
"""
Test script to verify DeepSpeed setup and basic functionality
"""

import os
import torch
import torch.distributed as dist
from train import (
    create_deepspeed_config,
    get_gpu_memory_info,
    load_model_and_tokenizer,
    setup_lora,
    setup_custom_chat_template
)

def test_basic_setup():
    """Test basic setup without distributed training"""
    print("🧪 Testing Basic Setup")
    print("=" * 50)
    
    # Test GPU availability
    if not torch.cuda.is_available():
        print("❌ CUDA not available")
        return False
    
    num_gpus = torch.cuda.device_count()
    print(f"✅ CUDA available with {num_gpus} GPUs")
    
    # Test GPU memory info
    for i in range(min(num_gpus, 4)):  # Test first 4 GPUs
        memory_info = get_gpu_memory_info(i)
        print(f"   GPU {i}: {memory_info['total']:.1f}GB total, {memory_info['free']:.1f}GB free")
    
    # Test DeepSpeed config creation
    try:
        config = create_deepspeed_config(stage=2)
        print("✅ DeepSpeed config creation successful")
    except Exception as e:
        print(f"❌ DeepSpeed config creation failed: {e}")
        return False
    
    return True

def test_model_loading():
    """Test model and tokenizer loading"""
    print("\n🧪 Testing Model Loading")
    print("=" * 50)
    
    try:
        # Use a smaller model for testing
        model_name = "microsoft/DialoGPT-small"  # Much smaller for testing
        
        print(f"Loading model: {model_name}")
        model, tokenizer = load_model_and_tokenizer(
            model_name=model_name,
            use_4bit=False,
            target_device=None,
            rank=0,
            max_length=1024  # Smaller for testing
        )
        
        print(f"✅ Model loaded successfully")
        print(f"   Model parameters: {sum(p.numel() for p in model.parameters()):,}")
        print(f"   Tokenizer vocab size: {len(tokenizer)}")
        
        # Test LoRA setup
        model = setup_lora(model, rank=0, max_length=1024)
        print("✅ LoRA setup successful")
        
        # Clean up
        del model, tokenizer
        torch.cuda.empty_cache()
        
        return True
        
    except Exception as e:
        print(f"❌ Model loading failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_deepspeed_config_files():
    """Test DeepSpeed configuration files"""
    print("\n🧪 Testing DeepSpeed Config Files")
    print("=" * 50)
    
    config_files = [
        "deepspeed_config_zero2.json",
        "deepspeed_config_zero3.json"
    ]
    
    for config_file in config_files:
        if os.path.exists(config_file):
            print(f"✅ {config_file} exists")
            try:
                import json
                with open(config_file, 'r') as f:
                    config = json.load(f)
                print(f"   ZeRO stage: {config.get('zero_optimization', {}).get('stage', 'N/A')}")
            except Exception as e:
                print(f"❌ Error reading {config_file}: {e}")
                return False
        else:
            print(f"❌ {config_file} not found")
            return False
    
    return True

def main():
    """Run all tests"""
    print("🚀 DeepSpeed Setup Verification")
    print("=" * 60)
    
    tests = [
        ("Basic Setup", test_basic_setup),
        ("DeepSpeed Config Files", test_deepspeed_config_files),
        ("Model Loading", test_model_loading),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    print("\n📊 Test Results")
    print("=" * 60)
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} {test_name}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 All tests passed! Ready for DeepSpeed training.")
        print("\nNext steps:")
        print("1. Run: python train.py")
        print("2. Or: ./launch_deepspeed.sh")
        print("3. Or: deepspeed --num_gpus=4 train.py")
    else:
        print("❌ Some tests failed. Please check the errors above.")
    
    return all_passed

if __name__ == "__main__":
    main()
