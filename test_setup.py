#!/usr/bin/env python3
"""
Quick validation script to test the optimized training setup
Run this before the main training to validate everything is working correctly
"""

import os
import torch
import torch.distributed as dist
import torch.multiprocessing as mp
from transformers import AutoTokenizer
from datasets import load_dataset

def test_gpu_setup():
    """Test GPU availability and memory"""
    print("🔍 Testing GPU Setup...")
    
    if not torch.cuda.is_available():
        print("❌ CUDA not available")
        return False
    
    gpu_count = torch.cuda.device_count()
    print(f"✅ Found {gpu_count} GPUs")
    
    for i in range(gpu_count):
        props = torch.cuda.get_device_properties(i)
        memory_gb = props.total_memory / 1024**3
        print(f"   GPU {i}: {props.name} - {memory_gb:.1f}GB")
    
    return True

def test_distributed_setup(rank, world_size):
    """Test distributed training setup"""
    try:
        # Set device
        torch.cuda.set_device(rank)
        device = torch.device(f"cuda:{rank}")
        
        # Initialize process group
        dist.init_process_group("nccl", rank=rank, world_size=world_size)
        
        # Test communication
        test_tensor = torch.tensor([rank], dtype=torch.float32).to(device)
        dist.all_reduce(test_tensor, op=dist.ReduceOp.SUM)
        expected_sum = sum(range(world_size))
        
        success = abs(test_tensor.item() - expected_sum) < 1e-6
        
        if success and rank == 0:
            print(f"✅ Distributed setup test PASSED")
        elif not success:
            print(f"❌ Distributed setup test FAILED on rank {rank}")
        
        dist.destroy_process_group()
        return success
        
    except Exception as e:
        print(f"❌ Distributed setup error on rank {rank}: {e}")
        return False

def test_dataset_loading():
    """Test dataset loading"""
    print("🔍 Testing Dataset Loading...")
    
    try:
        dataset = load_dataset("junkim100/limo_crosslingual_ko_en")
        train_size = len(dataset["train"])
        test_size = len(dataset["test"])
        
        print(f"✅ Dataset loaded successfully")
        print(f"   Train samples: {train_size}")
        print(f"   Test samples: {test_size}")
        
        # Test a sample
        sample = dataset["train"][0]
        required_keys = ["question", "translation", "solution", "answer"]
        
        if all(key in sample for key in required_keys):
            print(f"✅ Dataset format validation passed")
            return True
        else:
            print(f"❌ Dataset missing required keys: {required_keys}")
            return False
            
    except Exception as e:
        print(f"❌ Dataset loading failed: {e}")
        return False

def test_tokenizer_setup():
    """Test tokenizer with long sequences"""
    print("🔍 Testing Tokenizer Setup...")
    
    try:
        tokenizer = AutoTokenizer.from_pretrained(
            "meta-llama/Meta-Llama-3.1-8B",
            model_max_length=32159,
            use_fast=True,
        )
        
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
        
        # Test tokenization
        test_text = "This is a test sequence for tokenization validation."
        tokens = tokenizer(test_text, return_tensors="pt")
        
        print(f"✅ Tokenizer loaded successfully")
        print(f"   Vocab size: {len(tokenizer)}")
        print(f"   Max length: {tokenizer.model_max_length}")
        print(f"   Test tokenization: {len(tokens['input_ids'][0])} tokens")
        
        return True
        
    except Exception as e:
        print(f"❌ Tokenizer setup failed: {e}")
        return False

def run_distributed_test():
    """Run distributed training test"""
    print("🔍 Testing Distributed Training Setup...")
    
    # Get world size
    if "CUDA_VISIBLE_DEVICES" in os.environ:
        visible_devices = os.environ["CUDA_VISIBLE_DEVICES"].split(",")
        world_size = len(visible_devices)
    else:
        world_size = torch.cuda.device_count()
    
    if world_size < 2:
        print("⚠️  Skipping distributed test (requires 2+ GPUs)")
        return True
    
    # Set environment variables
    os.environ["MASTER_ADDR"] = "localhost"
    os.environ["MASTER_PORT"] = "12356"  # Different port to avoid conflicts
    
    try:
        mp.spawn(test_distributed_setup, args=(world_size,), nprocs=world_size, join=True)
        return True
    except Exception as e:
        print(f"❌ Distributed test failed: {e}")
        return False

def main():
    """Run all validation tests"""
    print("🚀 Running Optimized Training Setup Validation")
    print("=" * 60)
    
    tests = [
        ("GPU Setup", test_gpu_setup),
        ("Dataset Loading", test_dataset_loading),
        ("Tokenizer Setup", test_tokenizer_setup),
        ("Distributed Training", run_distributed_test),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}:")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Validation Summary:")
    
    all_passed = True
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"   {test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 All tests passed! Ready for optimized training.")
        print("   Run: python new.py")
    else:
        print("\n⚠️  Some tests failed. Please fix issues before training.")
    
    return all_passed

if __name__ == "__main__":
    main()
