#!/usr/bin/env python3
"""
Test script to verify the enhanced chat template with automatic translation token insertion
"""

import torch
from transformers import AutoTokenizer
import json

def test_chat_template():
    """Test the enhanced chat template functionality"""
    
    print("🧪 Testing Enhanced Chat Template with Translation Token Detection")
    print("=" * 70)
    
    # Load tokenizer from the enhanced model
    model_path = "./llama-reasoning-finetuned-9k-deepspeed/checkpoint-66"
    
    print(f"📂 Loading tokenizer from: {model_path}")
    tokenizer = AutoTokenizer.from_pretrained(model_path)
    
    # Check if translation tokens exist
    translation_start = "<translation>"
    translation_end = "</translation>"
    
    if translation_start in tokenizer.get_vocab():
        start_token_id = tokenizer.convert_tokens_to_ids(translation_start)
        print(f"✅ Found {translation_start} token - ID: {start_token_id}")
    else:
        print(f"❌ Missing {translation_start} token")
        return False
        
    if translation_end in tokenizer.get_vocab():
        end_token_id = tokenizer.convert_tokens_to_ids(translation_end)
        print(f"✅ Found {translation_end} token - ID: {end_token_id}")
    else:
        print(f"❌ Missing {translation_end} token")
        return False
    
    # Test cases: translation requests vs regular requests
    test_cases = [
        {
            "name": "Translation Request - 'translate'",
            "messages": [
                {"role": "system", "content": "You are a helpful assistant"},
                {"role": "user", "content": "Translate 'Hello world' to Italian"}
            ],
            "should_have_translation_token": True
        },
        {
            "name": "Translation Request - 'italian'", 
            "messages": [
                {"role": "system", "content": "You are a helpful assistant"},
                {"role": "user", "content": "What is 'Good morning' in Italian?"}
            ],
            "should_have_translation_token": True
        },
        {
            "name": "Translation Request - 'convert to italian'",
            "messages": [
                {"role": "system", "content": "You are a helpful assistant"},
                {"role": "user", "content": "Convert to Italian: 'Thank you very much'"}
            ],
            "should_have_translation_token": True
        },
        {
            "name": "Regular Request - Math",
            "messages": [
                {"role": "system", "content": "You are a helpful assistant"},
                {"role": "user", "content": "What is 2 + 2?"}
            ],
            "should_have_translation_token": False
        },
        {
            "name": "Regular Request - General",
            "messages": [
                {"role": "system", "content": "You are a helpful assistant"},
                {"role": "user", "content": "Tell me about the weather"}
            ],
            "should_have_translation_token": False
        }
    ]
    
    print(f"\n🔍 Testing {len(test_cases)} cases...")
    print("-" * 70)
    
    all_passed = True
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['name']}")
        print(f"   User message: '{test_case['messages'][-1]['content']}'")
        
        try:
            # Apply chat template
            formatted = tokenizer.apply_chat_template(
                test_case['messages'],
                tokenize=False,
                add_generation_prompt=True
            )
            
            # Check if <translation> token is present
            has_translation_token = "<translation>" in formatted
            expected = test_case['should_have_translation_token']
            
            if has_translation_token == expected:
                status = "✅ PASS"
                print(f"   {status} - Translation token {'present' if has_translation_token else 'absent'} as expected")
            else:
                status = "❌ FAIL"
                all_passed = False
                print(f"   {status} - Expected translation token {'present' if expected else 'absent'}, but was {'present' if has_translation_token else 'absent'}")
            
            # Show the formatted output (last few lines)
            lines = formatted.strip().split('\n')
            last_lines = lines[-3:] if len(lines) >= 3 else lines
            print(f"   Generated prompt ending:")
            for line in last_lines:
                print(f"     {line}")
                
        except Exception as e:
            print(f"   ❌ ERROR: {e}")
            all_passed = False
    
    print("\n" + "=" * 70)
    if all_passed:
        print("🎉 ALL TESTS PASSED! Enhanced chat template works correctly!")
        print("✅ Translation tokens are automatically inserted for translation requests")
        print("✅ Regular requests don't get translation tokens")
    else:
        print("❌ SOME TESTS FAILED! Check the chat template logic")
    
    return all_passed

def test_tokenizer_config():
    """Test that the tokenizer config has the enhanced chat template"""
    
    print("\n🔧 Testing Tokenizer Configuration")
    print("-" * 40)
    
    model_path = "./llama-reasoning-finetuned-9k-deepspeed/checkpoint-66"
    config_path = f"{model_path}/tokenizer_config.json"
    
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        if 'chat_template' in config:
            print("✅ Chat template found in tokenizer config")
            
            # Check if it contains translation detection logic
            template = config['chat_template']
            if 'is_translation_request' in template and '<translation>' in template:
                print("✅ Enhanced template with translation detection found")
                return True
            else:
                print("⚠️  Chat template exists but doesn't have translation detection")
                return False
        else:
            print("❌ No chat template found in tokenizer config")
            return False
            
    except Exception as e:
        print(f"❌ Error reading tokenizer config: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Enhanced Chat Template Testing Suite")
    print("=" * 70)
    
    # Test tokenizer config
    config_ok = test_tokenizer_config()
    
    if config_ok:
        # Test chat template functionality
        template_ok = test_chat_template()
        
        print("\n" + "=" * 70)
        print("📋 SUMMARY")
        print("=" * 70)
        
        if template_ok:
            print("🎉 SUCCESS: Enhanced chat template is working perfectly!")
            print("🔧 Your model will now automatically use <translation> tokens")
            print("📝 Translation requests will be detected and handled properly")
            print("\n🚀 Ready to test with the CLI:")
            print("   python chat_cli.py ./llama-reasoning-finetuned-9k-deepspeed/checkpoint-66")
        else:
            print("❌ ISSUES: Enhanced chat template needs debugging")
    else:
        print("❌ CONFIGURATION ISSUE: Tokenizer config needs to be fixed first")
