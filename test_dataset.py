#!/usr/bin/env python3
"""
Quick test script to check dataset structure
"""

from datasets import load_dataset

def test_dataset():
    """Test the dataset structure"""
    
    print("🔍 Testing dataset structure...")
    
    try:
        dataset = load_dataset("junkim100/Lima-X-Processed")
        
        print(f"✅ Dataset loaded successfully!")
        print(f"Available splits: {list(dataset.keys())}")
        
        for split_name, split_data in dataset.items():
            print(f"\n📊 Split '{split_name}':")
            print(f"   Size: {len(split_data)}")
            print(f"   Columns: {split_data.column_names}")
            
            if len(split_data) > 0:
                print(f"   Sample keys: {list(split_data[0].keys())}")
                
                # Check for target language columns
                for col in split_data.column_names:
                    if 'it_' in col:
                        print(f"   Found Italian column: {col}")
                        
        return True
        
    except Exception as e:
        print(f"❌ Error loading dataset: {e}")
        return False

if __name__ == "__main__":
    test_dataset()
