{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# Cell 1: Import required libraries\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import requests\n", "import json\n", "from datasets import load_dataset\n", "import numpy as np\n", "from transformers import AutoTokenizer\n", "\n", "# Set up plotting style\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def plot_length_distribution(lengths, title=\"Text Length Distribution\"):\n", "    \"\"\"\n", "    Create visualizations for text length distribution\n", "    \"\"\"\n", "    if lengths is None:\n", "        print(\"No data to plot\")\n", "        return\n", "    \n", "    fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "    fig.suptitle(title, fontsize=16)\n", "    \n", "    # Histogram\n", "    axes[0,0].hist(lengths, bins=30, alpha=0.7, color='skyblue', edgecolor='black')\n", "    axes[0,0].set_title('Length Distribution')\n", "    axes[0,0].set_xlabel('Text Length')\n", "    axes[0,0].set_ylabel('Frequency')\n", "    \n", "    # Box plot\n", "    axes[0,1].boxplot(lengths)\n", "    axes[0,1].set_title('Length Box Plot')\n", "    axes[0,1].set_ylabel('Text Length')\n", "    \n", "    # Cumulative distribution\n", "    sorted_lengths = np.sort(lengths)\n", "    cumulative = np.arange(1, len(sorted_lengths) + 1) / len(sorted_lengths)\n", "    axes[1,0].plot(sorted_lengths, cumulative, marker='o', markersize=2)\n", "    axes[1,0].set_title('Cumulative Distribution')\n", "    axes[1,0].set_xlabel('Text Length')\n", "    axes[1,0].set_ylabel('Cumulative Probability')\n", "    \n", "    # Statistics summary\n", "    stats_text = f\"\"\"\n", "    Count: {len(lengths)}\n", "    Min: {min(lengths)}\n", "    Max: {max(lengths)}\n", "    Mean: {np.mean(lengths):.2f}\n", "    Median: {np.median(lengths):.2f}\n", "    Std: {np.std(lengths):.2f}\n", "    \"\"\"\n", "    axes[1,1].text(0.1, 0.5, stats_text, fontsize=12, verticalalignment='center')\n", "    axes[1,1].set_title('Statistics Summary')\n", "    axes[1,1].axis('off')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "def analyze_dataset_direct(dataset_name, column_name, tokenizer, split='train'):\n", "    \"\"\"\n", "    Analyze dataset column lengths by loading the dataset directly\n", "    \"\"\"\n", "    try:\n", "        # Load the dataset\n", "        dataset = load_dataset(dataset_name)\n", "\n", "        # There's an item in junnei/ko-limo that has an empty solution. Remove it.\n", "        dataset['train'] = dataset['train'].filter(lambda x: len(x['solution']) > 0)\n", "        dataset['train']\n", "        \n", "        # Remove data with length larger than 40960\n", "        dataset['train'] = dataset['train'].filter(lambda x: len(x['solution']) < 40960)\n", "        \n", "        # Get the specified split\n", "        data_split = dataset[split]\n", "        \n", "        # Check if column exists\n", "        if column_name in data_split.column_names:\n", "            # Extract the column data\n", "            column_data = data_split[column_name]\n", "            \n", "            # Calculate lengths\n", "            # lengths = [len(str(item)) for item in column_data]\n", "            \n", "            #calcluate token length\n", "            lengths = [len(tokenizer(str(item))['input_ids']) for item in column_data]\n", "            \n", "            # Calculate statistics\n", "            stats = {\n", "                'count': len(lengths),\n", "                'min_length': min(lengths),\n", "                'max_length': max(lengths),\n", "                'mean_length': np.mean(lengths),\n", "                'median_length': np.median(lengths),\n", "                'std_length': np.std(lengths)\n", "            }\n", "            \n", "            plot_length_distribution(lengths, f\"{dataset_name} {split} {column_name}\")\n", "            print(\"Dataset Statistics:\")\n", "            for key, value in stats.items():\n", "                print(f\"{key}: {value}\")\n", "            return lengths, stats\n", "        else:\n", "            print(f\"Column '{column_name}' not found. Available columns: {data_split.column_names}\")\n", "            return None, None\n", "            \n", "    except Exception as e:\n", "        print(f\"Error loading dataset: {e}\")\n", "        return None, None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tokenizer = AutoTokenizer.from_pretrained(\"meta-llama/Llama-3.1-8B\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2a34a55adb274436bf9deaec8e6b1ccc", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer_config.json:   0%|          | 0.00/50.5k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "362063ff29c249488932be4bf07bde9b", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer.json:   0%|          | 0.00/9.09M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c0e1ce569fa14f2b979e385f24d96f28", "version_major": 2, "version_minor": 0}, "text/plain": ["special_tokens_map.json:   0%|          | 0.00/73.0 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABdEAAAPZCAYAAAD+1mNdAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQABAABJREFUeJzs3XlcVdX+//H3AWRQAZUYEwGHnOdK0VBME8ckh25qjiRp2nWsvpS3HEpMU7Oc0hwaLMsy703NHHJMMkXJTPOqKWoCkgMHUZDh/P7ox7md4DgePAiv5+OxH7HXWuezPvugtvmwztoGk8lkEgAAAAAAAAAAKMDB3gkAAAAAAAAAAFBcUUQHAAAAAAAAAMAKiugAAAAAAAAAAFhBER0AAAAAAAAAACsoogMAAAAAAAAAYAVFdAAAAAAAAAAArKCIDgAAAAAAAACAFRTRAQAAAAAAAACwgiI6AAAAAAAAAABWUEQHAAAAAAAAAMAKiugAAADFxNatWzVkyBDVqVNHFStWVJkyZeTl5aWHH35YI0aM0KZNm2QymW4Y55///KcMBoMMBoO+/vrr645dtmyZDAaDgoODC/QFBweb4+QfLi4uqly5srp166Y1a9bc1HWdOHFCBoNBnTp1Mrflx9u6detNxRg4cKAMBoMGDhxo0X7y5EmL/L766qvrxuncubN5bLt27ayOy87O1qJFi9SxY0f5+/vL2dlZXl5eatasmSZOnKg//vjjpvIubsLDw2/pfb8X5H8/76b8vxsnT568q/MCAADAPiiiAwAA2Nkff/yhiIgItWnTRu+//76MRqNatmypJ598UqGhoTp37pzmzp2rxx57TE2bNr1urKysLC1fvtx8vmTJkjvOr2XLlhowYIAGDBigTp06ycnJSf/5z3/UtWtXjRkz5oav//LLLyVJPXr0uONcbuR61/v777/r22+/vWGMw4cPq27duoqOjtbGjRtVrVo19ezZUw8//LAOHz6sCRMmqFq1alq1atUt55f/S4u//zIAxUf+L2yWLVtm71QAAABQTDjZOwEAAIDS7NKlS3rkkUd05MgR1apVS/PmzVObNm0KjDt48KBmzZqlFStWXDfeV199pQsXLiggIEBJSUlas2aNUlJS5Ovre9s5PvPMMxZF35ycHI0ePVpz5szRrFmz1Lt3bz300ENWX//ll1/K0dFR3bp1u+0cbsTR0VH169fX+vXrlZycLD8/vwJjPvjgA+Xm5uqhhx7Snj17Co1z4sQJPfLII7pw4YLatWunxYsXq0qVKub+q1ev6tVXX9Vbb72lXr166csvv1RkZGRRXZbNffjhh7py5YrFNeHWbd68WdnZ2br//vvtnQoAAADuAlaiAwAA2NHzzz+vI0eOqGrVqtq1a1ehBXRJqlevnhYvXqwtW7ZcN97ixYslSSNHjlTr1q2Vk5OjDz/80KY5Ozk5afr06fLw8JCk624Z8/vvv2v37t1q1aqV7rvvPpvm8XeDBw9WTk6OPvjgg0L7ly5dKldXV/Xp08dqjH79+unChQtq3ry51qxZU6DY7ObmpunTp2vcuHHKy8vTwIEDdf78eZteR1GqUqWKatWqpbJly9o7lXtatWrVVKtWLZUpU8beqQAAAOAuoIgOAABgJ8ePH9cnn3wiSZo1a5YqVqx4w9c8/PDDVvtOnjypzZs3y8nJSf3791dUVJQk22zp8neurq6qUaOGJCklJcXquK+++komk+mubOXSt29fubi4aOnSpQX6tm3bpmPHjumJJ55QhQoVCn39tm3b9P3330uS5syZIxcXF6tzTZ48WT4+PkpLS9OcOXNuKr/g4GANGjRI0p+r4v+6l3t4eLh53F/3Ld+xY4e6du0qb29vOTg4mLcYSU9P16JFi9S9e3fVqFFD5cqVU7ly5VS/fn298sorunTpUqE5WNsT/a9bmJw4cUL9+vWTn5+fXFxcVK1aNY0fP15ZWVk3dZ1/tXLlSrVr105eXl7mPf7r1KmjIUOG6MCBAwXGX7lyRVOnTlWTJk3k7u6usmXLqm7duho/frwuXrx4S3PfaK/0v78X+fvr5/8SZtCgQRbfowkTJphfe7090W/1GvLnDQ4Olslk0sKFC9W0aVOVK1dOnp6eat++veLi4m7p2gEAAGBbFNEBAADsZM2aNcrLy1PFihXVpUuXO463ZMkSmUwmderUSX5+furRo4c8PT3166+/ateuXTbI2JLRaJSk624V8+WXX8pgMOiJJ56w+fx/V6lSJXXr1k1HjhwxF8Pz5a/QHzx4sNXXr169WpJUt27dG+497+rqqieffFKS9J///Oem8uvZs6datmwp6c+VzPn7zA8YMEAdOnQoMH7lypUKDw/Xb7/9pnbt2umxxx4zF/Z/+uknRUdHa+fOnfLz81PXrl31yCOPKCkpSVOmTNFDDz10WyvkExIS1KhRI+3YsUOtW7dWq1atlJSUpDfeeENPPfXULcWaNGmSnnzySW3btk316tVTr1691Lx5czk6Omrx4sX67rvvLMZfuHBBLVu2VExMjI4fP65HH31UnTp10rlz5/TGG2+oadOmRfogz/Lly2vAgAGqVq2aJMtnAQwYMECNGjW6YYw7vYZBgwZpxIgRqlChgrp06SI/Pz9t3LhRbdq00e7du210pQAAALhlJgAAANhFv379TJJMbdu2veNYubm5psDAQJMk0+rVq83tzz77rEmSafDgwYW+bunSpSZJpqCgoAJ9QUFBJkmmpUuXFug7dOiQydHR0STJtGfPnkJjnzt3zuTo6Ghq0aJFgT5JJkmmLVu23NT1DRgwwCTJNGDAAIv2EydOmCSZHB0dTSaTyfTtt98WuN60tDRT2bJlTcHBwaa8vDzzNf/9fQ8LCzNJMg0aNOimcvrggw9MkkwODg6m7Ozsm3pN/tx/v46/at26tfn9mTt3bqFjTp8+bdq0aZMpNzfXoj0jI8PUv39/kyTTc889ZzX239/3/PdXkumVV14x5eTkmPt+/vlnU7ly5UySTLt27bqp68zMzDS5ubmZypcvb/r1118L9J88edJ0+PBhi7Z//OMfJkmmZs2amf744w9ze3p6uqljx44mSdf9s3Sz7flu9F4U9uc+X/7fjRMnTtzxNeT/Gc7/e3jkyBFzX05Ojmnw4MEmSab27dtbzQcAAABFi5XoAAAAdvLHH39Ikry9vQvt/+mnnzRw4MACx86dOwuM3bBhg06fPi1fX1917tzZ3J6/pcvnn3+uy5cv33HOaWlp2rBhg7p3767c3FyNHz9eDz74YKFjV69erdzcXHXv3v2O571Z7dq1U5UqVfT5558rIyNDkvTpp5/qypUr5i1LrElNTZV0/ZX1f5U/Li8vTxcuXLjDzAt69NFH9dxzzxXaV7lyZbVt21YODpa382XLltX8+fPl5OSklStX3vKcTZs21eTJk+Xo6Ghuq1evnvr16ydJ2rRp003FMRqNunr1qqpWraqaNWsW6A8KClKtWrXM56dOndLKlStlMBi0cOFCeXl5mfvKly+vRYsWydXVVbt27SqST1XYgi2u4d1339UDDzxgPnd0dNQbb7wh6c/thrKzs4v2IgAAAFAoJ3snAAAAgMKdPn260IdkhoeH65FHHrFoe//99yVJ/fv3l5PT/27xHnroIdWrV08HDx7UZ599Zi6q34pBgwaZ9/LO5+joqI8//lh9+/a1+rpVq1ZJ0l0tojs4OGjAgAGaPHmyPv/8cw0aNEhLliyRg4ODBg4caNO5TCaTTeP9Xc+ePW84ZteuXdqxY4dOnTqlK1eumHNydnZWamqqLl68eFN77efr0qVLob9oqF27tqQ/HxR7M7y9vRUcHKwDBw5o7NixioqKUp06dayO3759u/Ly8tSkSRM1aNCgQP/999+viIgI/fvf/9aWLVvUokWLm7yiu+dOr8HJyanQbX38/PxUsWJFXbx4UefPn5efn1+RXQMAAAAKx0p0AAAAO7nvvvsk/W8F9N916dJFJpPJfLRt27bQcampqeZ9uQvb8zu/7XYfMPrXvaE7deokd3d35ebmatiwYfrxxx8Lfc2lS5e0efNmNWnSRCEhIbc17+3KfyDkkiVL9Msvv+jHH3/Uo48+qqCgoOu+Lv/7cb0Hpf7VuXPnJP1ZuK9UqdKdJV2I4ODg684dFhamli1b6v/+7/80b948LVu2TB988IE++OADXblyRdL/9q2/WVWqVCm03cPDQ5KUmZl507E+/PBD+fj4aObMmapbt668vLzUqVMnzZo1y/wpjHz5xfnr/VnJ36v8Zgv5d9udXoO/v7/KlClT6Otu5/0HAACA7VBEBwAAsJMmTZpIkvbt26e8vLzbjvPRRx8pOztbTk5OeuaZZ/TII49YHPmr2Xft2qVff/31luM/88wzWrZsmZYtW6a1a9fq9OnTatOmjdLT0/Xkk0+aC7Z/9fXXXys7O1s9evS47eu6XSEhIQoPD9fOnTsVExMj6foPFM2X/zDRm32AY/4vEBo2bGix+t9W3NzcrPY988wz2rlzp0JDQ7VhwwalpKTo2rVr5l+4+Pv7S7r11fJ/3x7mToSFhenkyZNauXKlRowYoeDgYH377bcaM2aMqlatqs2bN9tsrttxJ3/nioIt33sAAADYFndqAAAAdtKlSxc5ODjo4sWLWrdu3W3HWbx4sSQpJydH33//fYHjp59+KjD2Tnh6euqzzz5TpUqVlJiYqJkzZxYY8+WXX0q6u1u5/FV+0fzrr79WxYoV9cQTT9zwNd26dZMkHTp0SPHx8dcdm5mZqc8//1yS9Pjjj99htrcmIyND69atk4ODg9atW6fHHntMPj4+5lXMGRkZSk5Ovqs5WePm5qaePXvq3XffVXx8vJKTkxUdHa309HSLX2zcf//9kqTffvvNaqz8vvyxN5L/fqSnpxfan5iYeFNxblZRXAMAAACKB4roAAAAdlK9enX94x//kCSNGTNGaWlptxwjLi5Ohw4dkouLiy5evGix/ctfj/wi/UcffaScnJw7zt3b21vjx4+XJL311lu6dOmSuS8jI0MbNmxQnTp1LB4eeTf16NFDQUFB8vLy0qBBg+Tq6nrD17Rp00bNmzeXJA0fPlxZWVlWx/7rX/9SamqqPDw8NHz48JvOy9nZWZLu6HuQlpam3NxceXh4qEKFCgX6P/744yLfr/12eXt7a9q0aZL+fBDnxYsXJUmtWrWSg4ODEhISLH7pky8pKUnr16+X9Of36WbkF6oPHz5coO/AgQM6ffp0oa+73e9RUVwDAAAAigeK6AAAAHY0d+5cVa9eXUePHlWLFi20bdu2QsedPHlSZ86cKdCev7K8W7duhRZU87Vv315+fn5KSUnRmjVrbJL7c889pypVqigtLU0zZswwt69bt05Xr161y1Yu+dzc3HTy5En98ccfFrndyMcff6wKFSpo9+7d6tKlS4FC69WrV/Xiiy/qrbfeMu+77u3tfdPxK1euLOnP1e63y9fXVxUrVtSlS5f00UcfWfT98MMP5i1s7CkxMVHvv/9+oXuyf/3115KkihUrmvf6rlKlinr16iWTyaRnn31W58+fN4/PyMhQdHS0MjMz1aJFi5t+qGi7du0kSRMnTrT4hcjJkyc1YMAAq79oyP8e/fLLLzc1T76iuAYAAAAUD7bfvBEAAAA3rWLFivr+++/Vp08fbd68WeHh4apcubIaNWqkChUq6OrVqzp69Kh+/vlnmUwm1a9fXw8++KAk6fLly/rss88kSQMGDLjuPI6OjurTp49mzpypxYsXKzIy8o5zd3Fx0YQJEzR48GDNnj1bo0ePVqVKlcxbudxMEf25554zF1IL89VXX5n3974bqlWrpp07d6pbt27atGmTqlatqubNmyswMFCXLl3S999/L6PRqPLly2vJkiW3/IuC5s2bKyAgQPv371eTJk1Uv359lSlTRjVr1tQLL7xwUzEcHR316quvavTo0erfv7/mzp2rqlWr6tSpU9q1a5eefvppbd++3ebbldyKixcvasiQIXruuefUqFEj88M2jx49qv3798tgMGj69OlydHQ0v2bu3Ln69ddftXv3blWrVk1t2rSRk5OTtm3bptTUVIWEhGj58uU3ncPLL7+sL774QuvWrdMDDzyghx56SKmpqdqzZ49atmypFi1aaNeuXQVeFxkZqYkTJ+qdd97RwYMHFRgYKAcHBz3++OM33LrH1tcAAACA4oGV6AAAAHbm4+OjTZs2adOmTRo8eLDKlSun7du3a8WKFfruu+/k5OSk6Ohobdy4UQkJCapXr54k6fPPP9fly5fl5+eniIiIG87Tv39/SdI333yjs2fP2iT3/v37q06dOkpPT9f06dOVlZWltWvXqlq1amrYsOENX3/48GHt3r3b6nG9LVWKSt26dXXo0CG99957evTRR3X06FF98cUX+uGHH/TAAw/o1Vdf1W+//aZevXrdcmxnZ2d9++23evzxx3XmzBl9/PHHWrx4sdauXXtLcUaNGqXVq1erRYsWOnLkiL7++mtlZWVp7ty55gfJ2lO1atX09ttvq0uXLrp06ZLWrVuntWvXKiMjQ/3799eePXsUFRVl8RovLy/t2rVLsbGxCgkJ0YYNG7RmzRrdd999evnllxUfH6/g4OCbziEkJES7du1S9+7dlZ6erjVr1iglJUWvvPKK1q1bZ94z/e8aNGigL7/8UqGhodq9e7eWLVumxYsXa9++fTec09bXAAAAgOLBYCquGyYCAADgnvP111/r8ccf1wsvvGDe+xoAAAAA7mVs5wIAAACbcXV11WuvvaY+ffrYOxUAAAAAsAlWogMAAAAAAAAAYAV7ogMAAAAAAAAAYAVFdAAAAAAAAAAArKCIDgAAAAAAAACAFRTRAQAAAAAAAACwgiI6AAAAAAAAAABWUEQHAAAAAAAAAMAKiugAAAAAAAAAAFhBER0AAAAAAAAAACsoogMAAAAAAAAAYAVFdAAAAAAAAAAArKCIDgDFXHh4uOrVq3dX5zQYDJowYUKRz7N161YZDAZt3brV3HY3r/fkyZMyGAxatmzZXZkPAAAAuBF73P8XBwMHDlRwcLC90wCAQlFEB1AqLFu2TAaDQXv37rV3KoU6e/asJkyYoISEBJvHDg4OlsFgkMFgkIODgypUqKD69esrOjpau3fvttk8n3zyid5++22bxbOl4pwbAAAAbI/7f4P5cHV1VY0aNfTCCy/owoULNp/vRsLDwy3yqVSpkh566CEtWbJEeXl5NpljypQpWr16tU1iAUBhnOydAADgz5voiRMnKjg4WI0aNbJ5/EaNGmns2LGSpPT0dB0+fFgrV67UokWLNHr0aM2cOdNi/NWrV+XkdGv/i/jkk0908OBBjRo16qZf06pVK129elXOzs63NNetspZbUFCQrl69qjJlyhTp/AAAAMBf3c37/8zMTMXHx+vtt9/Wtm3b9OOPP9p8vhupXLmyYmNjJUmpqan68MMPFRUVpf/+97+aOnXqHcefMmWKevbsqcjIyDuOBQCFoYgOAKXA/fffr6efftqi7c0331SfPn00a9Ys1ahRQ8OGDTP3ubq6Fmk+mZmZcnZ2loODQ5HPdT35K3MAAACAkuTv9//PPPOMypcvr7feektHjx5VjRo17mo+np6eFvk8++yzqlmzpubMmaPJkyezqAVAscd2LgDwF7///rsGDx4sX19fubi4qG7dulqyZInFmPx9vD///HO98cYbqly5slxdXdW2bVsdO3asQMy5c+eqatWqcnNz08MPP6wdO3YoPDxc4eHh5ngPPfSQJGnQoEHmjzn+fZ/uQ4cOqU2bNipbtqzuv/9+TZs27Y6u1c3NTR999JEqVaqkN954QyaTydz39z3R09PTNWrUKAUHB8vFxUU+Pj567LHHtG/fPkl/fkRz7dq1SkxMNOefv59h/vu1YsUKjR8/Xvfff7/Kli0ro9FY6J7o+eLj49WiRQu5ubkpJCRECxYssOjP/4juyZMnLdr/HvN6uVnbE/27775TWFiYypUrpwoVKqhbt246fPiwxZgJEybIYDDo2LFjGjhwoCpUqCBPT08NGjRIV65cublvAgAAAOyqNN3/+/n5SVKBT5ze6N536dKlMhgMBd6XKVOmyGAwaN26dbecS9myZdW8eXNlZGQoNTXV6riMjAyNHTtWgYGBcnFxUc2aNfXWW28V+NklIyNDH3zwgfm9HDhw4C3nBADXw0p0APj/UlJS1Lx5cxkMBo0YMULe3t765ptvFBUVJaPRWGArkKlTp8rBwUHjxo1TWlqapk2bpr59+1rsMz5//nyNGDFCYWFhGj16tE6ePKnIyEhVrFhRlStXliTVrl1bkyZN0quvvqro6GiFhYVJklq0aGGOc/HiRXXo0EHdu3fXk08+qS+++EIvvfSS6tevr44dO972NZcvX15PPPGEFi9erEOHDqlu3bqFjhs6dKi++OILjRgxQnXq1NH58+e1c+dOHT58WE2aNNErr7yitLQ0nTlzRrNmzTLH/qvJkyfL2dlZ48aNU1ZW1nW3cLl48aI6deqkJ598Ur1799bnn3+uYcOGydnZWYMHD76la7yZ3P5q06ZN6tixo6pWraoJEybo6tWrevfdd9WyZUvt27evwMOOnnzySYWEhCg2Nlb79u3T+++/Lx8fH7355pu3lCcAAADurpJ8/5+dna0//vhD0p+fAt2/f79mzpypVq1aKSQkxDzuZu59Bw0apFWrVmnMmDF67LHHFBgYqJ9//lkTJ05UVFSUOnXqdFvv/2+//SZHR0dVqFCh0H6TyaTHH39cW7ZsUVRUlBo1aqRvv/1WL7zwgn7//Xfzvf1HH32kZ555Rg8//LCio6MlSdWqVbutnADAKhMAlAJLly41STLt2bPH6pioqCiTv7+/6Y8//rBof+qpp0yenp6mK1eumEwmk2nLli0mSabatWubsrKyzONmz55tkmT6+eefTSaTyZSVlWXy8vIyPfTQQ6bs7GzzuGXLlpkkmVq3bm1u27Nnj0mSaenSpQXyat26tUmS6cMPPzS3ZWVlmfz8/Ew9evS44bUHBQWZOnfubLV/1qxZJkmmf//73+Y2SabXXnvNfO7p6WkaPnz4defp3LmzKSgoqEB7/vtVtWpV83v4974tW7aY2/Kvd8aMGea2rKwsU6NGjUw+Pj6ma9eumUym/31PT5w4ccOY1nI7ceJEgfc9f57z58+b23766SeTg4ODqX///ua21157zSTJNHjwYIuYTzzxhMnLy6vAXAAAALh7Svv9v6QCR8uWLQtc683e+yYlJZkqVapkeuyxx0xZWVmmxo0bm6pUqWJKS0u7YT6tW7c21apVy5SammpKTU01HT582PTPf/7TJMnUtWtX87gBAwZY3LOvXr3aJMn0+uuvW8Tr2bOnyWAwmI4dO2ZuK1eunGnAgAE3zAUAbhfbuQCA/lzl8OWXX6pr164ymUz6448/zEdERITS0tLMW5fkGzRokMVq6vwVJL/99pskae/evTp//ryGDBli8ZHJvn37qmLFireUX/ny5S32EHR2dtbDDz9snutO5K/KTk9PtzqmQoUK2r17t86ePXvb8wwYMEBubm43NdbJyUnPPvus+dzZ2VnPPvuszp07p/j4+NvO4UaSkpKUkJCggQMHqlKlSub2Bg0a6LHHHiv0o6pDhw61OA8LC9P58+dlNBqLLE8AAADcmZJ+/9+sWTNt3LhRGzdu1Jo1a/TGG2/ol19+0eOPP66rV69KurV7Xz8/P82dO1cbN25UWFiYEhIStGTJEnl4eNxUPr/++qu8vb3l7e2t2rVr691331Xnzp0LbBHzV+vWrZOjo6P++c9/WrSPHTtWJpNJ33zzzU3NDQC2wHYuAKA/nxB/6dIlLVy4UAsXLix0zLlz5yzOq1SpYnGef2N88eJFSVJiYqIkqXr16hbjnJycCmwJciOVK1eWwWAoMN+BAwduKU5hLl++LElyd3e3OmbatGkaMGCAAgMD1bRpU3Xq1En9+/dX1apVb3qev35s9EYCAgJUrlw5i7YHHnhA0p/7mDdv3vymY92K/O9ZzZo1C/TVrl1b3377rTIyMixyu96fg5v9oQIAAAB3V0m//7/vvvvUrl0783nnzp1Vs2ZN9ezZU++//76ef/75W773feqpp/Txxx9r7dq1io6OVtu2bW/6eoKDg7Vo0SIZDAa5urqqRo0a8vHxue5rEhMTFRAQUODnlNq1a5v7AeBuoYgOAJLy8vIkSU8//bQGDBhQ6JgGDRpYnDs6OhY6zvSXh9zYSlHOdfDgQUkFb/b/6sknn1RYWJi++uorbdiwQdOnT9ebb76pVatW3fSe7De7Cv1m/f2Hiny5ubk2nedG7uafAwAAANhGabz/zy96b9++Xc8///wtv/78+fPau3evpD8fepqXlycHh5vb4KBcuXIWRX0AuNdQRAcASd7e3nJ3d1dubq7Nbu6CgoIkSceOHVObNm3M7Tk5OTp58qTFTbm1gnBRu3z5sr766isFBgaaV3RY4+/vr+eee07PPfeczp07pyZNmuiNN94wF9FteQ1nz54tsOL7v//9rySZV/Hkr/y5dOmSxWsLW5Fys7nlf8+OHDlSoO/XX3/VfffdV2CFPAAAAO49pfH+PycnR9L/Pol6q/e+w4cPV3p6umJjYxUTE6O3335bY8aMKbJ8g4KCtGnTJqWnp1usRv/1118t8pfs9/MUgNKDPdEBQH+u9OjRo4e+/PJL88rsv0pNTb3lmA8++KC8vLy0aNEi8w2rJC1fvtz8kc98+Tenfy8IF6WrV6+qX79+unDhgl555ZXrruxOS0uzaPPx8VFAQICysrLMbeXKlSsw7nbl5OTovffeM59fu3ZN7733nry9vdW0aVNJUrVq1ST9uZLmr7kW9nHcm83N399fjRo10gcffGDxvTh48KA2bNigTp063e4lAQAAoBgpjff/X3/9tSSpYcOGkm7t3veLL77QZ599pqlTp+r//u//9NRTT2n8+PHmhS5FoVOnTsrNzdWcOXMs2mfNmiWDwWDxidhy5crd1fcSQOnDSnQApcqSJUu0fv36Au0jR47U1KlTtWXLFjVr1kxDhgxRnTp1dOHCBe3bt0+bNm3ShQsXbmkuZ2dnTZgwQc8//7weffRRPfnkkzp58qSWLVumatWqWRStq1WrpgoVKmjBggVyd3dXuXLl1KxZs1vaR/x6fv/9d3388ceS/lx5cujQIa1cuVLJyckaO3asxUM8/y49PV2VK1dWz5491bBhQ5UvX16bNm3Snj17NGPGDPO4pk2b6rPPPtOYMWP00EMPqXz58uratett5RsQEKA333xTJ0+e1AMPPKDPPvtMCQkJWrhwocqUKSNJqlu3rpo3b66YmBhduHBBlSpV0ooVKyx+YLmd3KZPn66OHTsqNDRUUVFRunr1qt599115enpqwoQJt3U9AAAAsA/u//9ckPLTTz/pvffe03333WexlcvN3PueO3dOw4YNU5s2bTRixAhJ0pw5c7RlyxYNHDhQO3fuvOltXW5F165d1aZNG73yyis6efKkGjZsqA0bNujf//63Ro0aZV5UI/15v79p0ybNnDlTAQEBCgkJUbNmzWyeE4BSzAQApcDSpUtNkqwep0+fNplMJlNKSopp+PDhpsDAQFOZMmVMfn5+prZt25oWLlxojrVlyxaTJNPKlSst5jhx4oRJkmnp0qUW7e+8844pKCjI5OLiYnr44YdN33//valp06amDh06WIz797//bapTp47JycnJIk7r1q1NdevWLXBNAwYMMAUFBd3w2oOCgszXaTAYTB4eHqa6deuahgwZYtq9e3ehr5Fkeu2110wmk8mUlZVleuGFF0wNGzY0ubu7m8qVK2dq2LChad68eRavuXz5sqlPnz6mChUqmCSZc7P2fv21b8uWLea2/Ovdu3evKTQ01OTq6moKCgoyzZkzp8Drjx8/bmrXrp3JxcXF5Ovra3r55ZdNGzduLBDTWm7WvmebNm0ytWzZ0uTm5mby8PAwde3a1XTo0CGLMa+99ppJkik1NdWiPf/P2okTJwp9bwEAAFD0uP//37U6ODiYfHx8TL179zYdO3aswPgb3ft2797d5O7ubjp58mSB/CWZ3nzzzevmY+16bub60tPTTaNHjzYFBASYypQpY6pRo4Zp+vTppry8PItxv/76q6lVq1YmNzc3kyTTgAEDbjgfANwKg8nEk88A4G7Ky8uTt7e3unfvrkWLFtk7HQAAAABFiPt/ALj3sSc6ABShzMxM/f13lR9++KEuXLig8PBw+yQFAAAAoEhw/w8AJRMr0QGgCG3dulWjR49Wr1695OXlpX379mnx4sWqXbu24uPj5ezsbO8UAQAAANgI9/8AUDLxYFEAKELBwcEKDAzUO++8Y374Zf/+/TV16lRuoAEAAIAShvt/ACiZWIkOAAAAAAAAAIAV7IkOAAAAAAAAAIAVFNEBAAAAAAAAALCi1O2JnpeXp7Nnz8rd3V0Gg8He6QAAAKCEMJlMSk9PV0BAgBwcWKtia9zHAwAAwNZu9h6+1BXRz549q8DAQHunAQAAgBLq9OnTqly5sr3TKHG4jwcAAEBRudE9fKkroru7u0v6843x8PCwczYAAAAoKYxGowIDA833m7At7uMBAABgazd7D1/qiuj5H/308PDg5hsAAAA2x1YjRYP7eAAAABSVG93Ds1kjAAAAAAAAAABWUEQHAAAAAAAAAMAKiugAAAAAAAAAAFhBER0AAAAAAAAAACsoogMAAAAAAAAAYAVFdAAAAAAAAAAArKCIDgAAAAAAAACAFRTRAQAAAAAAAACwgiI6AAAAAAAAAABWUEQHAAAAAAAAAMAKJ3snAAAAAAAAgDuXm5urHTt2KCkpSf7+/goLC5Ojo6O90wKAex4r0QEAAAAAAO5xq1atUvXq1dWmTRv16dNHbdq0UfXq1bVq1Sp7pwYA9zyK6AAAAAAAAPewVatWqWfPnqpfv77i4uKUnp6uuLg41a9fXz179qSQDgB3yGAymUz2TuJuMhqN8vT0VFpamjw8POydDgAAAEoI7jOLFu8vABQuNzdX1atXV/369bV69Wo5OPxvvWReXp4iIyN18OBBHT16lK1dAOBvbvYek5XoAAAAAAAA96gdO3bo5MmTevnlly0K6JLk4OCgmJgYnThxQjt27LBThgBw7+PBoihSqampMhqNNovn4eEhb29vm8UDAAAAAOBelpSUJEmqV69eof357fnjAAC3jiI6ikxqaqoGRw9V+tVMm8V0d3PVkoULKKQDAAAAACDJ399fknTw4EE1b968QP/BgwctxgEAbh1FdBQZo9Go9KuZCu83TF7+le843vmkM9r60XwZjUaK6AAAAAAASAoLC1NwcLCmTJlS6J7osbGxCgkJUVhYmB2zBIB7G3uio8h5+VeWX1DIHR+2KMQDAACUJtu3b1fXrl0VEBAgg8Gg1atXW/QbDIZCj+nTp5vHBAcHF+ifOnWqRZwDBw4oLCxMrq6uCgwM1LRp0wrksnLlStWqVUuurq6qX7++1q1bVyTXDACljaOjo2bMmKE1a9YoMjJScXFxSk9PV1xcnCIjI7VmzRq99dZbPFQUAO4ARXQAAACghMrIyFDDhg01d+7cQvuTkpIsjiVLlshgMKhHjx4W4yZNmmQx7vnnnzf3GY1GtW/fXkFBQYqPj9f06dM1YcIELVy40Dxm165d6t27t6KiorR//35FRkYqMjLSvMUAAODOdO/eXV988YV+/vlntWjRQh4eHmrRooUOHjyoL774Qt27d7d3igBwT2M7FwAAAKCE6tixozp27Gi138/Pz+L83//+t9q0aaOqVatatLu7uxcYm2/58uW6du2alixZImdnZ9WtW1cJCQmaOXOmoqOjJUmzZ89Whw4d9MILL0iSJk+erI0bN2rOnDlasGDBnVwiAOD/6969u7p166YdO3YoKSlJ/v7+CgsLYwU6ANgAK9EBAAAAKCUlRWvXrlVUVFSBvqlTp8rLy0uNGzfW9OnTlZOTY+6Li4tTq1at5OzsbG6LiIjQkSNHdPHiRfOYdu3aWcSMiIhQXFyc1XyysrJkNBotDgDA9Tk6Oio8PFy9e/dWeHg4BXQAsBFWogMAAADQBx98IHd39wIf+f/nP/+pJk2aqFKlStq1a5diYmKUlJSkmTNnSpKSk5MVEhJi8RpfX19zX8WKFZWcnGxu++uY5ORkq/nExsZq4sSJtrg0ACg1cnNzWYkOAEWAIjoAAAAALVmyRH379pWrq6tF+5gxY8xfN2jQQM7Oznr22WcVGxsrFxeXIssnJibGYm6j0ajAwMAimw8A7nWrVq3S2LFjdfLkSXNbcHCwZsyYwZ7oAHCH2M4FAAAAKOV27NihI0eO6Jlnnrnh2GbNmiknJ8dcpPHz81NKSorFmPzz/H3UrY2xts+6JLm4uMjDw8PiAAAUbtWqVerZs6fq16+vuLg4paenKy4uTvXr11fPnj21atUqe6cIAPc0iugAAABAKbd48WI1bdpUDRs2vOHYhIQEOTg4yMfHR5IUGhqq7du3Kzs72zxm48aNqlmzpipWrGges3nzZos4GzduVGhoqA2vAgBKp9zcXI0dO1ZdunTR6tWr1bx5c5UvX17NmzfX6tWr1aVLF40bN065ubn2ThUA7lkU0QEAAIAS6vLly0pISFBCQoIk6cSJE0pISNCpU6fMY4xGo1auXFnoKvS4uDi9/fbb+umnn/Tbb79p+fLlGj16tJ5++mlzgbxPnz5ydnZWVFSUfvnlF3322WeaPXu2xVYsI0eO1Pr16zVjxgz9+uuvmjBhgvbu3asRI0YU7RsAAKXAjh07dPLkSb388stycLAs8zg4OCgmJkYnTpzQjh077JQhANz72BMdAAAAKKH27t2rNm3amM/zC9sDBgzQsmXLJEkrVqyQyWRS7969C7zexcVFK1as0IQJE5SVlaWQkBCNHj3aokDu6empDRs2aPjw4WratKnuu+8+vfrqq4qOjjaPadGihT755BONHz9eL7/8smrUqKHVq1erXr16RXTlAFB6JCUlSZLVf1Pz2/PHAQBuHUV0AAAAoIQKDw+XyWS67pjo6GiLgvdfNWnSRD/88MMN52nQoMENVzj26tVLvXr1umEsAMCt8ff3lyQdPHhQzZs3L9B/8OBBi3EAgFvHdi4AAAAAAAD3qLCwMAUHB2vKlCnKy8uz6MvLy1NsbKxCQkIUFhZmpwwB4N5HER0AAAAAAOAe5ejoqBkzZmjNmjWKjIxUXFyc0tPTFRcXp8jISK1Zs0ZvvfWWHB0d7Z0qANyz2M4FAAAAAADgHta9e3d98cUXGjt2rFq0aGFuDwkJ0RdffKHu3bvbMTsAuPdRRMc95dq1LCUmJtosnoeHh7y9vW0WDwAAAAAAe+jevbu6deumHTt2KCkpSf7+/goLC2MFOgDYAEV03DPSL13QieO/6ZXJU+Ti4mKTmO5urlqycAGFdAAAAADAPc/R0VHh4eH2TgMAShyK6LhnZF7JkEOZMmrdb5juD652x/HOJ53R1o/my2g0UkQHAAAAAAAAUCiK6LjnePkFyC8oxN5pAAAAAAAAACgFHOydAAAAAAAAAAAAxRVFdAAAAAAAAAAArGA7F5Rq165lKTEx0WbxPDw82F8dAAAAAAAAKEEooqPUSr90QSeO/6ZXJk+Ri4uLTWK6u7lqycIFFNIBAAAAAACAEoIiOkqtzCsZcihTRq37DdP9wdXuON75pDPa+tF8GY1GiugAAAAAAABACVHsiui///67XnrpJX3zzTe6cuWKqlevrqVLl+rBBx+UJJlMJr322mtatGiRLl26pJYtW2r+/PmqUaOGnTPHvcrLL0B+QSH2TgMAAAAAAABAMVSsHix68eJFtWzZUmXKlNE333yjQ4cOacaMGapYsaJ5zLRp0/TOO+9owYIF2r17t8qVK6eIiAhlZmbaMXMAAAAAAAAAQElUrFaiv/nmmwoMDNTSpUvNbSEh/1shbDKZ9Pbbb2v8+PHq1q2bJOnDDz+Ur6+vVq9eraeeeuqu5wwAAAAAAAAAKLmK1Ur0//znP3rwwQfVq1cv+fj4qHHjxlq0aJG5/8SJE0pOTla7du3MbZ6enmrWrJni4uIKjZmVlSWj0WhxAAAAAAAAAABwM4pVEf23334z72/+7bffatiwYfrnP/+pDz74QJKUnJwsSfL19bV4na+vr7nv72JjY+Xp6Wk+AgMDi/YiAAAAAAAAAAAlRrEqoufl5alJkyaaMmWKGjdurOjoaA0ZMkQLFiy47ZgxMTFKS0szH6dPn7ZhxgAAAAAAAACAkqxYFdH9/f1Vp04di7batWvr1KlTkiQ/Pz9JUkpKisWYlJQUc9/fubi4yMPDw+IAAAAAAAAAAOBmFKsiesuWLXXkyBGLtv/+978KCgqS9OdDRv38/LR582Zzv9Fo1O7duxUaGnpXcwUAAAAAAAAAlHxO9k7gr0aPHq0WLVpoypQpevLJJ/Xjjz9q4cKFWrhwoSTJYDBo1KhRev3111WjRg2FhIToX//6lwICAhQZGWnf5AEAAAAAAAAAJU6xKqI/9NBD+uqrrxQTE6NJkyYpJCREb7/9tvr27Wse8+KLLyojI0PR0dG6dOmSHnnkEa1fv16urq52zLzkSE1NldFotEmsxMRE5eTk2CQWAAAAAAAAANhDsSqiS1KXLl3UpUsXq/0Gg0GTJk3SpEmT7mJWpUNqaqoGRw9V+tVMm8S7eiVDZ5NTlJ19zSbxAAAAAAAAAOBuK3ZFdNiP0WhU+tVMhfcbJi//yncc72jCHn057y3l5ubaIDsAAAAAAAAAuPsooqMAL//K8gsKueM4qWdP2yAbAAAAAAAAALAfB3snAAAAAAAAAABAcUURHQAAAAAAAAAAKyiiAwAAAAAAAABgBUV0AAAAAAAAAACsoIgOAAAAAAAAAIAVTvZOAAAAAAAAAHcuNzdXO3bsUFJSkvz9/RUWFiZHR0d7pwUA9zxWogMAAAAAANzjVq1aperVq6tNmzbq06eP2rRpo+rVq2vVqlX2Tg0A7nmsRAdKmdTUVBmNRpvF8/DwkLe3t83iAQAAAABuzapVq9SzZ0916dJFn376qerVq6eDBw9qypQp6tmzp7744gt1797d3mkCwD2LIjpQiqSmpmpw9FClX820WUx3N1ctWbiAQjoAAAAA2EFubq7Gjh2rLl26aPXq1XJw+HPTgebNm2v16tWKjIzUuHHj1K1bN7Z2AYDbRBEdKEWMRqPSr2YqvN8weflXvuN455POaOtH82U0GimiAwAAAIAd7NixQydPntSnn35qLqDnc3BwUExMjFq0aKEdO3YoPDzcPkkCwD2OIjpQCnn5V5ZfUIi90wAAAAAA3KGkpCRJUr169Qrtz2/PHwcAuHU8WBQAAAAAAOAe5e/vL0k6ePBgof357fnjAAC3jiI6AAAAAADAPSosLEzBwcGaMmWK8vLyLPry8vIUGxurkJAQhYWF2SlDALj3UUQHAAAAAAC4Rzk6OmrGjBlas2aNIiMjFRcXp/T0dMXFxSkyMlJr1qzRW2+9xUNFAeAOsCc6AAAAAADAPax79+764osvNHbsWLVo0cLcHhISoi+++ELdu3e3Y3YAcO+jiA4AAAAAAHCP6969u7p166YdO3YoKSlJ/v7+CgsLYwU6ANgARXQAAAAAAIASwNHRUeHh4fZOAwBKHPZEBwAAAEqo7du3q2vXrgoICJDBYNDq1ast+gcOHCiDwWBxdOjQwWLMhQsX1LdvX3l4eKhChQqKiorS5cuXLcYcOHBAYWFhcnV1VWBgoKZNm1Ygl5UrV6pWrVpydXVV/fr1tW7dOptfLwAAAFAUKKIDAAAAJVRGRoYaNmyouXPnWh3ToUMHJSUlmY9PP/3Uor9v37765ZdftHHjRq1Zs0bbt29XdHS0ud9oNKp9+/YKCgpSfHy8pk+frgkTJmjhwoXmMbt27VLv3r0VFRWl/fv3KzIyUpGRkTp48KDtLxoAAACwMbZzAQAAAEqojh07qmPHjtcd4+LiIj8/v0L7Dh8+rPXr12vPnj168MEHJUnvvvuuOnXqpLfeeksBAQFavny5rl27piVLlsjZ2Vl169ZVQkKCZs6caS62z549Wx06dNALL7wgSZo8ebI2btyoOXPmaMGCBTa8YgAo3a5du6Z58+bp+PHjqlatmp577jk5OzvbOy0AuOexEh0AAAAoxbZu3SofHx/VrFlTw4YN0/nz5819cXFxqlChgrmALknt2rWTg4ODdu/ebR7TqlUriyJNRESEjhw5oosXL5rHtGvXzmLeiIgIxcXFFeWlAUCp8uKLL6pcuXIaPXq05syZo9GjR6tcuXJ68cUX7Z0aANzzKKIDAAAApVSHDh304YcfavPmzXrzzTe1bds2dezYUbm5uZKk5ORk+fj4WLzGyclJlSpVUnJysnmMr6+vxZj88xuNye8vTFZWloxGo8UBACjciy++qOnTp8vLy0uLFi1SUlKSFi1aJC8vL02fPp1COgDcIbZzAQAAAEqpp556yvx1/fr11aBBA1WrVk1bt25V27Zt7ZiZFBsbq4kTJ9o1BwC4F1y7dk2zZs2Sr6+vzpw5IyenP0s9zzzzjAYOHKjKlStr1qxZev3119naBQBuEyvRAQAAAEiSqlatqvvuu0/Hjh2TJPn5+encuXMWY3JycnThwgXzPup+fn5KSUmxGJN/fqMx1vZil6SYmBilpaWZj9OnT9/ZxQFACTVv3jzl5OTo9ddfNxfQ8zk5OWnSpEnKycnRvHnz7JQhANz7KKIDAAAAkCSdOXNG58+fl7+/vyQpNDRUly5dUnx8vHnMd999p7y8PDVr1sw8Zvv27crOzjaP2bhxo2rWrKmKFSuax2zevNliro0bNyo0NNRqLi4uLvLw8LA4AAAFHT9+XJLUpUsX5ebmauvWrfr000+1detW5ebmqkuXLhbjAAC3ju1cAAAAgBLq8uXL5lXlknTixAklJCSoUqVKqlSpkiZOnKgePXrIz89Px48f14svvqjq1asrIiJCklS7dm116NBBQ4YM0YIFC5Sdna0RI0boqaeeUkBAgCSpT58+mjhxoqKiovTSSy/p4MGDmj17tmbNmmWed+TIkWrdurVmzJihzp07a8WKFdq7d68WLlx4d98QACiBqlWrJkmaNGmSvvnmG508edLcFxwcbP43PX8cAODWsRIdAAAAKKH27t2rxo0bq3HjxpKkMWPGqHHjxnr11Vfl6OioAwcO6PHHH9cDDzygqKgoNW3aVDt27JCLi4s5xvLly1WrVi21bdtWnTp10iOPPGJR/Pb09NSGDRt04sQJNW3aVGPHjtWrr76q6Oho85gWLVrok08+0cKFC9WwYUN98cUXWr16terVq3f33gwAKKGee+45OTg4aP78+apXr57i4uKUnp6uuLg41atXT++9954cHBz03HPP2TtVALhnsRIdAAAAKKHCw8NlMpms9n/77bc3jFGpUiV98skn1x3ToEED7dix47pjevXqpV69et1wPgDArXF0dJS7u7vS0tL0448/6sCBA6pSpYoOHDigH3/8UZLk7u4uR0dHO2cKAPcuVqIDAAAAAADco3bs2KG0tDT17dtXFy5c0LPPPqv7779fzz77rC5cuKA+ffooLS3thr/sBABYRxEdAAAAAADgHpWUlCRJWrBggYxGo4YPH6727dtr+PDhMhqNWrBggcU4AMCtYzsXoJhLTU2V0Wi0SazExETl5OTYJBYAAAAAwP78/f0lSXPmzNF7771nfrDohg0btHbtWvMzKvLHAQBuHUV0oBhLTU3V4OihSr+aaZN4V69k6GxyirKzr9kkHgAAAADAvsLCwuTj46OYmBh17txZL7zwgtzc3HT16lWtW7dOL7/8snx8fBQWFmbvVAHgnkURHSjGjEaj0q9mKrzfMHn5V77jeEcT9ujLeW8pNzfXBtkBAAAAAIqD/IdIf/fdd1q7dq253c3NzV4pAUCJQhEduAd4+VeWX1DIHcdJPXvaBtkAAAAAAIqLHTt2KDU1VQaDwVxM/yuDwaBz585px44dCg8Pv/sJAkAJwINFAQAAAAAA7lG///67JKlRo0by8/Oz6PP19VWjRo0sxgEAbh1FdAAAAAAAgHtUamqqJCkhIUH169dXXFyc0tPTFRcXp/r16yshIcFiHADg1rGdyz0uNTVVRqPRJrESExOVk5Njk1gAAAAAAKDoeXl5SZK8vb21atUqOTn9Wepp3ry5Vq1apfvvv1/nzp0zjwMA3DqK6Pew1NRUDY4eqvSrmTaJd/VKhs4mpyg7+5pN4gEAAAAAgKJ1/vx5SdK5c+fUvXt3xcTEqF69ejp48KBiY2N17tw5i3EAgFtHEf0eZjQalX41U+H9hsnLv/IdxzuasEdfzntLubm5NsgOAAAAAAAUNW9vb0lS48aNlZCQoBYtWpj7qlSposaNG2v//v3mcQCAW0cRvQTw8q8sv6CQO46Teva0DbIBAAAAAAB3y/333y9J2r9/vwwGg0Xf6dOnderUKYtxAIBbx4NFAQAAAAAA7lFhYWHy9PQstC+/qO7p6amwsLC7mRYAlCisRAcAAAAAALhH5ebmKj09XZLUqVMndezYUW5ubrp69aq++eYbrV27Vunp6crNzZWjo6OdswWAexNFdAAAAAAAgHvUvHnzlJeXp2HDhpmL5vlCQkL07LPP6r333tO8efM0atQo+yUKAPcwtnMBAAAAAAC4Rx0/flyS1LRpU5lMJou+vLw8PfjggxbjAAC3rlgV0SdMmCCDwWBx1KpVy9yfmZmp4cOHy8vLS+XLl1ePHj2UkpJix4wBAAAAAADsp1q1apKkZ555RvXq1dPIkSMVHR2tkSNHql69ehoyZIjFOADArSt227nUrVtXmzZtMp87Of0vxdGjR2vt2rVauXKlPD09NWLECHXv3l3ff/+9PVIFAAAAAACwq2effVajR4+Wg4OD1q9fr9zcXHOfo6OjHBwclJeXp2effdaOWQLAva1YrUSX/iya+/n5mY/77rtPkpSWlqbFixdr5syZevTRR9W0aVMtXbpUu3bt0g8//GDnrAEAAAAAAO6+3bt3S/pz65a8vDw9/fTT2r9/v55++mlz21/HAQBuXbEroh89elQBAQGqWrWq+vbtq1OnTkmS4uPjlZ2drXbt2pnH1qpVS1WqVFFcXJy90gUAAAAAALCb06dPS5LKli0rg8Ggjz/+WI0bN9bHH38sBwcHlS1b1mIcAODWFasierNmzbRs2TKtX79e8+fP14kTJxQWFqb09HQlJyfL2dlZFSpUsHiNr6+vkpOTrcbMysqS0Wi0OAAAAAAAAEqC/BXmERERqly5skXf/fffr/bt21uMAwDcumK1J3rHjh3NXzdo0EDNmjVTUFCQPv/8c7m5ud1WzNjYWE2cONFWKQIAAAAAABQbJpNJkvTVV18VqJ2kpqZq9erVFuMAALeuWK1E/7sKFSrogQce0LFjx+Tn56dr167p0qVLFmNSUlLk5+dnNUZMTIzS0tLMBx9fAgAAAAAAJUXVqlXNX3t4eGjhwoU6e/asFi5cKA8Pj0LHAQBuTbEuol++fFnHjx+Xv7+/mjZtqjJlymjz5s3m/iNHjujUqVMKDQ21GsPFxUUeHh4WBwAAAAAAQElQp04dSZKjo6OcnZ0VHR2tgIAARUdHy8XFRY6OjhbjAAC3rlht5zJu3Dh17dpVQUFBOnv2rF577TU5Ojqqd+/e8vT0VFRUlMaMGaNKlSrJw8NDzz//vEJDQ9W8eXN7pw4AAAAAAHDX7dq1S5KUm5ura9euacyYMapatap+++03LV++XLm5ueZxf91GFwBw84pVEf3MmTPq3bu3zp8/L29vbz3yyCP64Ycf5O3tLUmaNWuWHBwc1KNHD2VlZSkiIkLz5s2zc9YAAAAAAAD29eSTT2rVqlWaOXOmuc3JyUm9evXSypUr7ZgZANz7ilURfcWKFdftd3V11dy5czV37ty7lBFwa65dy1JiYqLN4iUmJionJ8dm8QAAAAAAJUt4eLhef/11nT17Vunp6VqwYIGOHz+uatWqaejQoWrXrp15HADg9hSrIjpwL0u/dEEnjv+mVyZPkYuLi01iXr2SobPJKcrOvmaTeAAAAACAkiU8PFw+Pj7auXOnevXqpZdffllRUVE6ePCgevXqpe+//14+Pj4U0QHgDlBEB2wk80qGHMqUUet+w3R/cDWbxDyasEdfznvLvIcdAAAAAODedeXKFf366682j/vCCy/oxRdf1MaNG7VmzRpzu6urqwwGg1544QX99NNPNp+3Vq1aKlu2rM3jAkBxQxEdsDEvvwD5BYXYJFbq2dM2iQMAAAAAsL9ff/1VTZs2LbL4WVlZFueZmZmS/iyyF4X4+Hg1adKkSGIDQHFCER0AAAAAAOAuqFWrluLj44ssfm5urlavXq0pU6bo5ZdfVmRkpBwdHYtsvlq1ahVZbAAoTiiiAwAAAAAA3AVly5Yt8pXbjo6OmjJlinr06MEqcQCwEQd7JwAAAAAAAAAAQHFFER0AAAAAAAAAACsoogMAAAAAAAAAYAVFdAAAAAAAAAAArKCIDgAAAAAAAACAFRTRAQAAAAAAAACwgiI6AAAAAAAAAABWUEQHAAAAAAAAAMAKiugAAAAAAAAAAFhBER0AAAAAAAAAACsoogMAAAAl1Pbt29W1a1cFBATIYDBo9erV5r7s7Gy99NJLql+/vsqVK6eAgAD1799fZ8+etYgRHBwsg8FgcUydOtVizIEDBxQWFiZXV1cFBgZq2rRpBXJZuXKlatWqJVdXV9WvX1/r1q0rkmsGAAAAbI0iOgAAAFBCZWRkqGHDhpo7d26BvitXrmjfvn3617/+pX379mnVqlU6cuSIHn/88QJjJ02apKSkJPPx/PPPm/uMRqPat2+voKAgxcfHa/r06ZowYYIWLlxoHrNr1y717t1bUVFR2r9/vyIjIxUZGamDBw8WzYUDAAAANuRk7wQAAAAAFI2OHTuqY8eOhfZ5enpq48aNFm1z5szRww8/rFOnTqlKlSrmdnd3d/n5+RUaZ/ny5bp27ZqWLFkiZ2dn1a1bVwkJCZo5c6aio6MlSbNnz1aHDh30wgsvSJImT56sjRs3as6cOVqwYIEtLhUAAAAoMqxEBwAAACBJSktLk8FgUIUKFSzap06dKi8vLzVu3FjTp09XTk6OuS8uLk6tWrWSs7OzuS0iIkJHjhzRxYsXzWPatWtnETMiIkJxcXFFdzEAAACAjbASHQAAAIAyMzP10ksvqXfv3vLw8DC3//Of/1STJk1UqVIl7dq1SzExMUpKStLMmTMlScnJyQoJCbGI5evra+6rWLGikpOTzW1/HZOcnGw1n6ysLGVlZZnPjUbjHV8jAAAAcDsoogMAAAClXHZ2tp588kmZTCbNnz/fom/MmDHmrxs0aCBnZ2c9++yzio2NlYuLS5HlFBsbq4kTJxZZfAAAAOBmsZ0LAAAAUIrlF9ATExO1ceNGi1XohWnWrJlycnJ08uRJSZKfn59SUlIsxuSf5++jbm2MtX3WJSkmJkZpaWnm4/Tp07d6aQAAAIBNsBIdQImWmppq049/e3h4yNvb22bxAACwp/wC+tGjR7VlyxZ5eXnd8DUJCQlycHCQj4+PJCk0NFSvvPKKsrOzVaZMGUnSxo0bVbNmTVWsWNE8ZvPmzRo1apQ5zsaNGxUaGmp1HhcXlyJd6Q4AAADcLIroAEqs1NRUDY4eqvSrmTaL6e7mqiULF1BIBwDcEy5fvqxjx46Zz0+cOKGEhARVqlRJ/v7+6tmzp/bt26c1a9YoNzfXvEd5pUqV5OzsrLi4OO3evVtt2rSRu7u74uLiNHr0aD399NPmAnmfPn00ceJERUVF6aWXXtLBgwc1e/ZszZo1yzzvyJEj1bp1a82YMUOdO3fWihUrtHfvXi1cuPDuviEAAADAbaCIDqDEMhqNSr+aqfB+w+TlX/mO451POqOtH82X0WikiA4AuCfs3btXbdq0MZ/n728+YMAATZgwQf/5z38kSY0aNbJ43ZYtWxQeHi4XFxetWLFCEyZMUFZWlkJCQjR69GiLfdI9PT21YcMGDR8+XE2bNtV9992nV199VdHR0eYxLVq00CeffKLx48fr5ZdfVo0aNbR69WrVq1evCK8eAAAAsA2K6ABKPC//yvILCrF3GgAA3HXh4eEymUxW+6/XJ0lNmjTRDz/8cMN5GjRooB07dlx3TK9evdSrV68bxgIAAACKGx4sCgAAAAAAAACAFRTRAQAAAAAAAACwgiI6AAAAAAAAAABWUEQHAAAAAAAAAMAKiugAAAAAAAAAAFhBER0AAAAAAAAAACsoogMAAAAAAAAAYAVFdAAAAAAAAAAArKCIDgAAAAAAAACAFRTRAQAAAAAAAACwgiI6AAAAAAAAAABWUEQHAAAAAAAAAMAKiugAAAAAAAAAAFhBER0AAAAAAAAAACsoogMAAAAAAAAAYAVFdAAAAAAAAAAArKCIDgAAAAAAAACAFRTRAQAAAAAAAACwgiI6AAAAAAAAAABWONk7AQD3tmvXspSYmGizeB4eHvL29rZZPAAAAAAAAOBOUEQHcNvSL13QieO/6ZXJU+Ti4mKTmO5urlqycAGFdAAAAAAAABQLxbqIPnXqVMXExGjkyJF6++23JUmZmZkaO3asVqxYoaysLEVERGjevHny9fW1b7JAKZR5JUMOZcqodb9huj+42h3HO590Rls/mi+j0UgRHQBQquXl5enYsWM6d+6c8vLyLPpatWplp6wAAACA0qnYFtH37Nmj9957Tw0aNLBoHz16tNauXauVK1fK09NTI0aMUPfu3fX999/bKVMAXn4B8gsKsXcaAACUCD/88IP69OmjxMREmUwmiz6DwaDc3Fw7ZQYAAACUTsXywaKXL19W3759tWjRIlWsWNHcnpaWpsWLF2vmzJl69NFH1bRpUy1dulS7du3SDz/8YMeMAQAAANsYOnSoHnzwQR08eFAXLlzQxYsXzceFCxfsnR4AAABQ6hTLIvrw4cPVuXNntWvXzqI9Pj5e2dnZFu21atVSlSpVFBcXd7fTBAAAAGzu6NGjmjJlimrXrq0KFSrI09PT4gAAAABwdxW77VxWrFihffv2ac+ePQX6kpOT5ezsrAoVKli0+/r6Kjk5udB4WVlZysrKMp8bjUab5gsAAADYUrNmzXTs2DFVr17d3qkAAAAAkA2L6L/99puqVq16RzFOnz6tkSNHauPGjXJ1dbVJXrGxsZo4caJNYgEAAABF4cCBA+avn3/+eY0dO1bJycmqX7++ypQpYzH2788MAgAAAFC0bFZEr169ulq3bq2oqCj17Nnztorg8fHxOnfunJo0aWJuy83N1fbt2zVnzhx9++23unbtmi5dumSxGj0lJUV+fn6FxoyJidGYMWPM50ajUYGBgbecGwAAAFBUGjVqJIPBYPEg0cGDB5u/zu/jwaIAAADA3WezIvq+ffu0dOlSjRkzRiNGjNA//vEPRUVF6eGHH77pGG3bttXPP/9s0TZo0CDVqlVLL730kgIDA1WmTBlt3rxZPXr0kCQdOXJEp06dUmhoaKExXVxc5OLicvsXBgAAABSxEydO2DsFAAAAAFbYrIjeqFEjzZ49WzNmzNB//vMfLVu2TI888ogeeOABDR48WP369ZO3t/d1Y7i7u6tevXoWbeXKlZOXl5e5PSoqSmPGjFGlSpXk4eGh559/XqGhoWrevLmtLgUAAAC4q4KCgsxfb9++XS1atJCTk+Wtek5Ojnbt2mUxFgAAAEDRc7B1QCcnJ3Xv3l0rV67Um2++qWPHjmncuHEKDAxU//79lZSUdEfxZ82apS5duqhHjx5q1aqV/Pz8tGrVKhtlDwAAANhXmzZtdOHChQLtaWlpatOmjR0yAgAAAEo3m61Ez7d3714tWbJEK1asULly5TRu3DhFRUXpzJkzmjhxorp166Yff/zxpuNt3brV4tzV1VVz587V3LlzbZw5AAAAYH/5e5//3fnz51WuXDk7ZAQAAACUbjYros+cOVNLly7VkSNH1KlTJ3344Yfq1KmTHBz+XOweEhKiZcuWKTg42FZTAgAAACVG9+7dJf35ENGBAwdaPNcnNzdXBw4cUIsWLeyVHgAAAFBq2ayIPn/+fA0ePFgDBw6Uv79/oWN8fHy0ePFiW00JAAAAlBienp6S/lyJ7u7uLjc3N3Ofs7OzmjdvriFDhtgrPQAAAKDUslkR/ejRozcc4+zsrAEDBthqSgAAAKDEWLp0qSQpODhY48aNY+sWAAAAoJiw2YNFly5dqpUrVxZoX7lypT744ANbTQMAAACUaK+99hoFdAAAAKAYsdlK9NjYWL333nsF2n18fBQdHc0KdAA35dq1LCUmJtokVmJionJycmwSCwCAu6Vx48aFPljUYDDI1dVV1atX18CBA9WmTRs7ZAcAAACUPjYrop86dUohISEF2oOCgnTq1ClbTQOgBEu/dEEnjv+mVyZPsXiY2u26eiVDZ5NTlJ19zQbZAQBwd3To0EHz589X/fr19fDDD0uS9uzZowMHDmjgwIE6dOiQ2rVrp1WrVqlbt252zhYAAAAo+WxWRPfx8dGBAwcUHBxs0f7TTz/Jy8vLVtMAKMEyr2TIoUwZte43TPcHV7vjeEcT9ujLeW8pNzfXBtkBAHB3/PHHHxo7dqz+9a9/WbS//vrrSkxM1IYNG/Taa69p8uTJFNEBAACAu8BmRfTevXvrn//8p9zd3dWqVStJ0rZt2zRy5Eg99dRTtpoGQCng5Rcgv6CCn2y5ValnT9sgGwAA7q7PP/9c8fHxBdqfeuopNW3aVIsWLVLv3r01c+ZMO2QHAAAAlD42K6JPnjxZJ0+eVNu2beXk9GfYvLw89e/fX1OmTLHVNAAAAECJ5urqql27dql69eoW7bt27ZKrq6ukP++z878GAAAAULRsVkR3dnbWZ599psmTJ+unn36Sm5ub6tevr6CgIFtNAQAAAJR4zz//vIYOHar4+Hg99NBDkv7cE/3999/Xyy+/LEn69ttv1ahRIztmCQAAAJQeNiui53vggQf0wAMP2DosAAAAUCqMHz9eISEhmjNnjj766CNJUs2aNbVo0SL16dNHkjR06FANGzbMnmkCAAAApYaDrQLl5uZq8eLF6tOnj9q1a6dHH33U4gAAAABwc/r27au4uDhduHBBFy5cUFxcnLmALklubm43tZ3L9u3b1bVrVwUEBMhgMGj16tUW/SaTSa+++qr8/f3l5uamdu3a6ejRoxZjLly4oL59+8rDw0MVKlRQVFSULl++bDHmwIEDCgsLk6urqwIDAzVt2rQCuaxcuVK1atWSq6ur6tevr3Xr1t3COwIAAADYj82K6CNHjtTIkSOVm5urevXqqWHDhhYHAAAAgJt37do1nTlzRqdOnbI4bkVGRoYaNmyouXPnFto/bdo0vfPOO1qwYIF2796tcuXKKSIiQpmZmeYxffv21S+//KKNGzdqzZo12r59u6Kjo839RqNR7du3V1BQkOLj4zV9+nRNmDBBCxcuNI/ZtWuXevfuraioKO3fv1+RkZGKjIzUwYMHb/FdAQAAAO4+m23nsmLFCn3++efq1KmTrUICAAAApc7Ro0c1ePBg7dq1y6LdZDLJYDAoNzf3pmN17NhRHTt2LLTPZDLp7bff1vjx49WtWzdJ0ocffihfX1+tXr1aTz31lA4fPqz169drz549evDBByVJ7777rjp16qS33npLAQEBWr58ua5du6YlS5bI2dlZdevWVUJCgmbOnGkuts+ePVsdOnTQCy+8IEmaPHmyNm7cqDlz5mjBggW3/B4BAAAAd5PNVqI7OzurevXqtgoHAAAAlEoDBw6Ug4OD1qxZo/j4eO3bt0/79u3T/v37tW/fPpvNc+LECSUnJ6tdu3bmNk9PTzVr1kxxcXGSpLi4OFWoUMFcQJekdu3aycHBQbt37zaPadWqlZydnc1jIiIidOTIEV28eNE85q/z5I/Jn6cwWVlZMhqNFgcAAABgDzZbiT527FjNnj1bc+bMkcFgsFVYAAAAoFRJSEhQfHy8atWqVaTzJCcnS5J8fX0t2n19fc19ycnJ8vHxseh3cnJSpUqVLMaEhIQUiJHfV7FiRSUnJ193nsLExsZq4sSJt3FlAAAAgG3ZrIi+c+dObdmyRd98843q1q2rMmXKWPSvWrXKVlMBAAAAJVadOnX0xx9/2DsNu4uJidGYMWPM50ajUYGBgXbMCAAAAKWVzYroFSpU0BNPPGGrcAAAAECp9Oabb+rFF1/UlClTVL9+/QKLUzw8PGwyj5+fnyQpJSVF/v7+5vaUlBQ1atTIPObcuXMWr8vJydGFCxfMr/fz81NKSorFmPzzG43J7y+Mi4uLXFxcbuPKAAAAANuyWRF96dKltgoFAAAAlFr5e4e3bdvWov12Hix6PSEhIfLz89PmzZvNRXOj0ajdu3dr2LBhkqTQ0FBdunRJ8fHxatq0qSTpu+++U15enpo1a2Ye88orryg7O9tc8N+4caNq1qypihUrmsds3rxZo0aNMs+/ceNGhYaG2uRaAAAAgKJksyK69OeqlK1bt+r48ePq06eP3N3ddfbsWXl4eKh8+fK2nAoAAAAokbZs2WKzWJcvX9axY8fM5ydOnFBCQoIqVaqkKlWqaNSoUXr99ddVo0YNhYSE6F//+pcCAgIUGRkpSapdu7Y6dOigIUOGaMGCBcrOztaIESP01FNPKSAgQJLUp08fTZw4UVFRUXrppZd08OBBzZ49W7NmzTLPO3LkSLVu3VozZsxQ586dtWLFCu3du1cLFy602bUCAAAARcVmRfTExER16NBBp06dUlZWlh577DG5u7vrzTffVFZWlhYsWGCrqQAAAIASq3Xr1jaLtXfvXrVp08Z8nr/H+IABA7Rs2TK9+OKLysjIUHR0tC5duqRHHnlE69evl6urq/k1y5cv14gRI9S2bVs5ODioR48eeuedd8z9np6e2rBhg4YPH66mTZvqvvvu06uvvqro6GjzmBYtWuiTTz7R+PHj9fLLL6tGjRpavXq16tWrZ7NrBQAAAIqKzYroI0eO1IMPPqiffvpJXl5e5vYnnnhCQ4YMsdU0AAAAQIm3Y8cOvffee/rtt9+0cuVK3X///froo48UEhKiRx555KbjhIeHy2QyWe03GAyaNGmSJk2aZHVMpUqV9Mknn1x3ngYNGmjHjh3XHdOrVy/16tXr+gkDAAAAxZCDrQLt2LFD48ePl7Ozs0V7cHCwfv/9d1tNAwAAAJRoX375pSIiIuTm5qZ9+/YpKytLkpSWlqYpU6bYOTsAAACg9LFZET0vL6/QhxydOXNG7u7utpoGAAAAKNFef/11LViwQIsWLTI/qFOSWrZsqX379tkxMwAAAKB0slkRvX379nr77bfN5waDQZcvX9Zrr72mTp062WoaAAAAoEQ7cuSIWrVqVaDd09NTly5duvsJAQAAAKWczYroM2bM0Pfff686deooMzNTffr0MW/l8uabb9pqGgAAAKBE8/Pz07Fjxwq079y5U1WrVrVDRgAAAEDpZrMHi1auXFk//fSTVqxYoQMHDujy5cuKiopS37595ebmZqtpAAAAgBJtyJAhGjlypJYsWSKDwaCzZ88qLi5O48aN07/+9S97pwcAAACUOjYrokuSk5OTnn76aVuGBAAAAEqV//u//1NeXp7atm2rK1euqFWrVnJxcdG4ceP0/PPP2zs9AAAAoNSxWRH9ww8/vG5///79bTUVAAAAUGIZDAa98soreuGFF3Ts2DFdvnxZderUkaurq86ePauAgAB7pwgAAACUKjYroo8cOdLiPDs7W1euXJGzs7PKli1LER0AAAC4Bc7OzqpTp475/KefflKTJk2Um5trx6wAAACA0sdmDxa9ePGixXH58mUdOXJEjzzyiD799FNbTQMAAAAAAAAAwF1jsyJ6YWrUqKGpU6cWWKUOAAAAAAAAAMC9oEiL6NKfDxs9e/ZsUU8DAAAAAAAAAIDN2WxP9P/85z8W5yaTSUlJSZozZ45atmxpq2kAAACAEunAgQPX7T9y5MhdygQAAADAX9msiB4ZGWlxbjAY5O3trUcffVQzZsyw1TQAAABAidSoUSMZDAaZTKYCffntBoPBDpkBAAAApZvNiuh5eXm2CgUAAACUOidOnLB3CgAAAAAKYbMiOgAAAIDbFxQUZO8UAAAAABTCZkX0MWPG3PTYmTNn2mpaAAAAAAAAAACKjM2K6Pv379f+/fuVnZ2tmjVrSpL++9//ytHRUU2aNDGPYx9HAAAAAAAAAMC9wmZF9K5du8rd3V0ffPCBKlasKEm6ePGiBg0apLCwMI0dO9ZWUwEAAAAAAAAAcFc42CrQjBkzFBsbay6gS1LFihX1+uuva8aMGbaaBgAAAAAAAACAu8ZmRXSj0ajU1NQC7ampqUpPT7fVNAAAAECJ9uijj+rSpUsF2o1Gox599NG7nxAAAABQytmsiP7EE09o0KBBWrVqlc6cOaMzZ87oyy+/VFRUlLp3726raQAAAIASbevWrbp27VqB9szMTO3YscMOGQEAAAClm832RF+wYIHGjRunPn36KDs7+8/gTk6KiorS9OnTbTUNAAAAUCIdOHDA/PWhQ4eUnJxsPs/NzdX69et1//332yM1AAAAoFSzWRG9bNmymjdvnqZPn67jx49LkqpVq6Zy5crZagoAAACgxGrUqJEMBoMMBkOh27a4ubnp3XfftUNmAAAAQOlmsyJ6vqSkJCUlJalVq1Zyc3OTyWSSwWCw9TQAAABAiXLixAmZTCZVrVpVP/74o7y9vc19zs7O8vHxkaOjox0zBAAAAEonmxXRz58/ryeffFJbtmyRwWDQ0aNHVbVqVUVFRalixYqaMWOGraYCAAAASpygoCBJUl5entUxLFABAAAA7j6bPVh09OjRKlOmjE6dOqWyZcua2//xj39o/fr1NxVj/vz5atCggTw8POTh4aHQ0FB988035v7MzEwNHz5cXl5eKl++vHr06KGUlBRbXQIAAABgdwMHDlRGRkaB9pMnT6pVq1Z2yAgAAAAo3WxWRN+wYYPefPNNVa5c2aK9Ro0aSkxMvKkYlStX1tSpUxUfH6+9e/fq0UcfVbdu3fTLL79I+rNQ//XXX2vlypXatm2bzp49q+7du9vqEgAAAAC7++mnn9SgQQPFxcWZ2z744AM1bNhQ9913nx0zAwAAAEonm23nkpGRYbECPd+FCxfk4uJyUzG6du1qcf7GG29o/vz5+uGHH1S5cmUtXrxYn3zyiflBS0uXLlXt2rX1ww8/qHnz5nd+EQAAAICd/fjjj3r55ZcVHh6usWPH6tixY/rmm280c+ZMDRkyxN7pAQAAAKWOzYroYWFh+vDDDzV58mRJksFgUF5enqZNm6Y2bdrccrzc3FytXLlSGRkZCg0NVXx8vLKzs9WuXTvzmFq1aqlKlSqKi4uzWkTPyspSVlaW+dxoNN5yLgAAAMDdUqZMGU2fPl1ly5bV5MmT5eTkpG3btik0NNTeqQEAAAClks2K6NOmTVPbtm21d+9eXbt2TS+++KJ++eUXXbhwQd9///1Nx/n5558VGhqqzMxMlS9fXl999ZXq1KmjhIQEOTs7q0KFChbjfX19lZycbDVebGysJk6ceLuXBQAAANxV2dnZ+r//+z/NnTtXMTEx2rlzp7p3767FixerU6dO9k4PAAAAKHVsVkSvV6+e/vvf/2rOnDlyd3fX5cuX1b17dw0fPlz+/v43HadmzZpKSEhQWlqavvjiCw0YMEDbtm277bxiYmI0ZswY87nRaFRgYOBtxwMAAACK0oMPPqgrV65o69atat68uUwmk6ZNm6bu3btr8ODBmjdvnr1TBAAAAEoVmxTRs7Oz1aFDBy1YsECvvPLKHcVydnZW9erVJUlNmzbVnj17NHv2bP3jH//QtWvXdOnSJYvV6CkpKfLz87Maz8XF5ab3ZAcAAADs7cEHH9Q777yjcuXKSfpzm8SXXnpJ7du3V79+/eycHQAAAFD6ONgiSJkyZXTgwAFbhCogLy9PWVlZatq0qcqUKaPNmzeb+44cOaJTp06xPyQAAABKjMWLF5sL6H/VuHFjxcfH2yEjAAAAoHSzSRFdkp5++mktXrz4jmLExMRo+/btOnnypH7++WfFxMRo69at6tu3rzw9PRUVFaUxY8Zoy5Ytio+P16BBgxQaGmr1oaIAAADAveijjz5Sy5YtFRAQoMTEREnS22+/rfXr19s5MwAAAKD0sdme6Dk5OVqyZIk2bdqkpk2bFlg9M3PmzBvGOHfunPr376+kpCR5enqqQYMG+vbbb/XYY49JkmbNmiUHBwf16NFDWVlZioiIYE9IAAAAlCjz58/Xq6++qlGjRumNN95Qbm6uJKlChQp6++231a1bNztnCAAAAJQud1xE/+233xQcHKyDBw+qSZMmkqT//ve/FmMMBsNNxbrRSnZXV1fNnTtXc+fOvb1kAQAAgGLu3Xff1aJFixQZGampU6ea2x988EGNGzfOjpkBAAAApdMdF9Fr1KihpKQkbdmyRZL0j3/8Q++88458fX3vODkAAACgtDlx4oQaN25coN3FxUUZGRl2yAgAAAAo3e54T3STyWRx/s0333BzDwAAANymkJAQJSQkFGhfv369ateuffcTAgAAAEo5m+2Jnu/vRXUAAAAANzZp0iSNGzdOY8aM0fDhw5WZmSmTyaQff/xRn376qWJjY/X+++/bO00AAACg1LnjIrrBYCiw5/nN7oEOAAAA4E8TJ07U0KFD9cwzz8jNzU3jx4/XlStX1KdPHwUEBGj27Nl66qmn7J0mAAAAUOrccRHdZDJp4MCBcnFxkSRlZmZq6NChKleunMW4VatW3elUAAAAQIn110909u3bV3379tWVK1d0+fJl+fj42DEzAAAAoHS74yL6gAEDLM6ffvrpOw0JAAAAlEp//0Rn2bJlVbZsWTtlAwAAAECyQRF96dKltsgDAAAAKPUeeOCBG26NeOHCBZvNFxwcrMTExALtzz33nObOnavw8HBt27bNou/ZZ5/VggULzOenTp3SsGHDtGXLFpUvX14DBgxQbGysnJz+96PG1q1bNWbMGP3yyy8KDAzU+PHjNXDgQJtdBwAAAFCUbP5gUQAAAAC3Z+LEifL09Lxr8+3Zs0e5ubnm84MHD+qxxx5Tr169zG1DhgzRpEmTzOd/XRmfm5urzp07y8/PT7t27VJSUpL69++vMmXKaMqUKZKkEydOqHPnzho6dKiWL1+uzZs365lnnpG/v78iIiLuwlUCAAAAd4YiOgAAAFBMPPXUU3d1/3Nvb2+L86lTp6patWpq3bq1ua1s2bLy8/Mr9PUbNmzQoUOHtGnTJvn6+qpRo0aaPHmyXnrpJU2YMEHOzs5asGCBQkJCNGPGDElS7dq1tXPnTs2aNYsiOgAAAO4JDvZOAAAAAEDB/dDvtmvXrunjjz/W4MGDLXJZvny57rvvPtWrV08xMTG6cuWKuS8uLk7169eXr6+vuS0iIkJGo1G//PKLeUy7du0s5oqIiFBcXNx188nKypLRaLQ4AAAAAHtgJToAAABQDJhMJrvOv3r1al26dMlir/I+ffooKChIAQEBOnDggF566SUdOXJEq1atkiQlJydbFNAlmc+Tk5OvO8ZoNOrq1atyc3MrNJ/Y2FhNnDjRVpcHAAAA3DaK6AAAAEAxkJeXZ9f5Fy9erI4dOyogIMDcFh0dbf66fv368vf3V9u2bXX8+HFVq1atSPOJiYnRmDFjzOdGo1GBgYFFOicAAABQGIroAAAAQCmXmJioTZs2mVeYW9OsWTNJ0rFjx1StWjX5+fnpxx9/tBiTkpIiSeZ91P38/Mxtfx3j4eFhdRW6JLm4uMjFxeWWrwUAAACwNfZEBwAAAEq5pUuXysfHR507d77uuISEBEmSv7+/JCk0NFQ///yzzp07Zx6zceNGeXh4qE6dOuYxmzdvtoizceNGhYaG2vAKAAAAgKLDSnQAAACgFMvLy9PSpUs1YMAAOTn978eD48eP65NPPlGnTp3k5eWlAwcOaPTo0WrVqpUaNGggSWrfvr3q1Kmjfv36adq0aUpOTtb48eM1fPhw8yryoUOHas6cOXrxxRc1ePBgfffdd/r888+1du1au1wvAFzP0aNHlZ6ebu807sjhw4ct/nsvc3d3V40aNeydBgBQRAcAAABKs02bNunUqVMaPHiwRbuzs7M2bdqkt99+WxkZGQoMDFSPHj00fvx48xhHR0etWbNGw4YNU2hoqMqVK6cBAwZo0qRJ5jEhISFau3atRo8erdmzZ6ty5cp6//33FRERcdeuEQBuxtGjR/XAAw/YOw2befrpp+2dgk3897//pZAOwO4oogMAAAClWPv27WUymQq0BwYGatu2bTd8fVBQkNatW3fdMeHh4dq/f/9t5wgAd0P+CvSPP/5YtWvXtnM2t+/q1as6efKkgoODr/vsieLu8OHDevrpp+/5TwYAKBkoogMAAAAAAPx/tWvXVpMmTeydxh1p2bKlvVMAgBKFB4sCAAAAAAAAAGAFRXQAAAAAAAAAAKygiA4AAAAAAAAAgBUU0QEAAAAAAAAAsIIiOgAAAAAAAAAAVlBEBwAAAAAAAADACoroAAAAAAAAAABYQREdAAAAAAAAAAArKKIDAAAAAAAAAGAFRXQAAAAAAAAAAKygiA4AAAAAAAAAgBUU0QEAAAAAAAAAsIIiOgAAAAAAAAAAVlBEBwAAAAAAAADACoroAAAAAAAAAABYQREdAAAAAAAAAAArKKIDAAAAAAAAAGAFRXQAAAAAAAAAAKygiA4AAAAAAAAAgBUU0QEAAAAAAAAAsIIiOgAAAAAAAAAAVjjZOwEAKO1SU1NlNBptFs/Dw0Pe3t42i1fc8wMAAAAAAChKFNEBwI5SU1M1OHqo0q9m2iymu5urlixcYJNCdXHPDwAAAAAAoKhRRAcAOzIajUq/mqnwfsPk5V/5juOdTzqjrR/Nl9FotEmRurjnBwAAAAAAUNQoogNAMeDlX1l+QSH2TsOq4p4fAAAAAABAUeHBogAAAAAAAAAAWEERHQAAAAAAAAAAKyiiAwAAAAAAAABgBUV0AAAAAAAAAACsKFZF9NjYWD300ENyd3eXj4+PIiMjdeTIEYsxmZmZGj58uLy8vFS+fHn16NFDKSkpdsoYAAAAAAAAAFCSFasi+rZt2zR8+HD98MMP2rhxo7Kzs9W+fXtlZGSYx4wePVpff/21Vq5cqW3btuns2bPq3r27HbMGAAAAAAAAAJRUTvZO4K/Wr19vcb5s2TL5+PgoPj5erVq1UlpamhYvXqxPPvlEjz76qCRp6dKlql27tn744Qc1b97cHmkDAAAAAAAAAEqoYrUS/e/S0tIkSZUqVZIkxcfHKzs7W+3atTOPqVWrlqpUqaK4uLhCY2RlZcloNFocAAAAAAAAAADcjGJbRM/Ly9OoUaPUsmVL1atXT5KUnJwsZ2dnVahQwWKsr6+vkpOTC40TGxsrT09P8xEYGFjUqQMAAAAAAAAASohiW0QfPny4Dh48qBUrVtxRnJiYGKWlpZmP06dP2yhDAAAAAAAAAEBJV6z2RM83YsQIrVmzRtu3b1flypXN7X5+frp27ZouXbpksRo9JSVFfn5+hcZycXGRi4tLUacMAAAAAAAAACiBitVKdJPJpBEjRuirr77Sd999p5CQEIv+pk2bqkyZMtq8ebO57ciRIzp16pRCQ0PvdroAAAAAAAAAgBKuWK1EHz58uD755BP9+9//lru7u3mfc09PT7m5ucnT01NRUVEaM2aMKlWqJA8PDz3//PMKDQ1V8+bN7Zw9AAAAAAAAAKCkKVZF9Pnz50uSwsPDLdqXLl2qgQMHSpJmzZolBwcH9ejRQ1lZWYqIiNC8efPucqa3LzU1VUaj0SaxEhMTlZOTY5NYAEqOa9eylJiYaJNY/DsDAAAAAABKu2JVRDeZTDcc4+rqqrlz52ru3Ll3ISPbSk1N1eDooUq/mmmTeFevZOhscoqys6/ZJB6Ae1/6pQs6cfw3vTJ5ik2eB8G/MwAAAAAAoLQrVkX0ks5oNCr9aqbC+w2Tl3/lG7/gBo4m7NGX895Sbm6uDbIDUBJkXsmQQ5kyat1vmO4PrnbH8fh3BgAAAAAAlHYU0e3Ay7+y/IJCbjzwBlLPnrZBNgBKIi+/AP6dAQDc0IQJEzRx4kSLtpo1a+rXX3+VJGVmZmrs2LFasWKFxVaKvr6+5vGnTp3SsGHDtGXLFpUvX14DBgxQbGysnJz+96PG1q1bNWbMGP3yyy8KDAzU+PHjzds1AgAAAMWdg70TAAAAAGA/devWVVJSkvnYuXOnuW/06NH6+uuvtXLlSm3btk1nz55V9+7dzf25ubnq3Lmzrl27pl27dumDDz7QsmXL9Oqrr5rHnDhxQp07d1abNm2UkJCgUaNG6ZlnntG33357V68TAAAAuF2sRAcAAABKMScnJ/n5+RVoT0tL0+LFi/XJJ5/o0UcflSQtXbpUtWvX1g8//KDmzZtrw4YNOnTokDZt2iRfX181atRIkydP1ksvvaQJEybI2dlZCxYsUEhIiGbMmCFJql27tnbu3KlZs2YpIiLirl4rAAAAcDsoogMA7qpr17KUmJhos3geHh7y9va2WTwAKG2OHj2qgIAAubq6KjQ0VLGxsapSpYri4+OVnZ2tdu3amcfWqlVLVapUUVxcnJo3b664uDjVr1/fYnuXiIgIDRs2TL/88osaN26suLg4ixj5Y0aNGnXdvLKyspSVlWU+NxqNtrlgALiOSh5OyslIlDHVzd6plHo5GYmq5EHZCkDxwL9GAIC7Jv3SBZ04/ptemTxFLi4uNonp7uaqJQsXUEgHgNvQrFkzLVu2TDVr1lRSUpImTpyosLAwHTx4UMnJyXJ2dlaFChUsXuPr66vk5GRJUnJyskUBPb8/v+96Y4xGo65evSo3t8ILVbGxsQX2aweAota5lbeMh17Xj4fsnQmkP78fAFAcUEQHANw1mVcy5FCmjFr3G6b7g6vdcbzzSWe09aP5MhqNFNEB4DZ07NjR/HWDBg3UrFkzBQUF6fPPP7da3L5bYmJiNGbMGPO50WhUYGCgHTMCUBqs3Z6qES/OVa1ateydSqn366+/au2LT2qUvRMBAFFEBwDYgZdfgPyCQuydBgDgbypUqKAHHnhAx44d02OPPaZr167p0qVLFqvRU1JSzHuo+/n56ccff7SIkZKSYu7L/29+21/HeHh4XLdQ7+LiYrNPLQHAzbpgzJFTuSB5eNe2dyqlntPpq7pgzLF3GgAgSXKwdwIAAAAAiofLly/r+PHj8vf3V9OmTVWmTBlt3rzZ3H/kyBGdOnVKoaGhkqTQ0FD9/PPPOnfunHnMxo0b5eHhoTp16pjH/DVG/pj8GAAAAEBxx0p0ALgFtn4oZmJionJyWF0BALCPcePGqWvXrgoKCtLZs2f12muvydHRUb1795anp6eioqI0ZswYVapUSR4eHnr++ecVGhqq5s2bS5Lat2+vOnXqqF+/fpo2bZqSk5M1fvx4DR8+3LyKfOjQoZozZ45efPFFDR48WN99950+//xzrV271p6XDgAAANw0iugAcJOK4qGYV69k6GxyirKzr9kkHgAAt+LMmTPq3bu3zp8/L29vbz3yyCP64YcfzM+ZmDVrlhwcHNSjRw9lZWUpIiJC8+bNM7/e0dFRa9as0bBhwxQaGqpy5cppwIABmjRpknlMSEiI1q5dq9GjR2v27NmqXLmy3n//fUVERNz16wUAAABuB0V0ALhJtn4opiQdTdijL+e9pdzcXJvEAwDgVqxYseK6/a6urpo7d67mzp1rdUxQUJDWrVt33Tjh4eHav3//beUIAAAA2BtFdAC4RbZ8KGbq2dM2iQMAAAAAAICiwYNFAQAAAAAAAACwgiI6AAAAAAAAAABWUEQHAAAAAAAAAMAKiugAAAAAAAAAAFhBER0AAAAAAAAAACsoogMAAAAAAAAAYAVFdAAAAAAAAAAArKCIDgAAAAAAAACAFU72TgAAgDtx7VqWEhMTbRbPw8ND3t7eNosHAAAAAADubRTRAQD3rPRLF3Ti+G96ZfIUubi42CSmu5urlixcQCEdAAAAAABIoogOALiHZV7JkEOZMmrdb5juD652x/HOJ53R1o/my2g0UkQHAAAAAACSKKIDAEoAL78A+QWF2DsNAAAAAABQAvFgUQAAAAAAAAAArKCIDgAAAAAAAACAFRTRAQAAAAAAAACwgiI6AAAAAAAAAABWUEQHAAAAAAAAAMAKiugAAAAAAAAAAFhBER0AAAAAAAAAACsoogMAAAAAAAAAYAVFdAAAAAAAAAAArKCIDgAAAAAAAACAFRTRAQAAAAAAAACwgiI6AAAAAAAAAABWUEQHAAAAAAAAAMAKiugAAAAAAAAAAFhBER0AAAAAAAAAACsoogMAAAAAAAAAYAVFdAAAAAAAAAAArKCIDgAAAAAAAACAFU72TgAAAAAAAMDerly5Iknat2+fnTO5M1evXtXJkycVHBwsNzc3e6dz2w4fPmzvFADAjCI6AAAAAAAo9X799VdJ0pAhQ+ycCf7K3d3d3ikAAEV0AAAAAACAyMhISVKtWrVUtmxZ+yZzBw4fPqynn35aH3/8sWrXrm3vdO6Iu7u7atSoYe80AKB4FdG3b9+u6dOnKz4+XklJSfrqq6/M/xOTJJPJpNdee02LFi3SpUuX1LJlS82fP59/UAEAAAAAwB2577779Mwzz9g7DZupXbu2mjRpYu80AKBEKFYPFs3IyFDDhg01d+7cQvunTZumd955RwsWLNDu3btVrlw5RUREKDMz8y5nCgAAAAAAAAAoDYrVSvSOHTuqY8eOhfaZTCa9/fbbGj9+vLp16yZJ+vDDD+Xr66vVq1frqaeeupupAgAAAAAAAABKgWK1Ev16Tpw4oeTkZLVr187c5unpqWbNmikuLs6OmQEAAAAAAAAASqpitRL9epKTkyVJvr6+Fu2+vr7mvsJkZWUpKyvLfG40GosmQQAAAAAAAABAiXPPrES/XbGxsfL09DQfgYGB9k4JAAAAAAAAAHCPuGeK6H5+fpKklJQUi/aUlBRzX2FiYmKUlpZmPk6fPl2keQIAAAAAAAAASo57pogeEhIiPz8/bd682dxmNBq1e/duhYaGWn2di4uLPDw8LA4AAAAAAAAAAG5GsSqiX758WQkJCUpISJD058NEExISdOrUKRkMBo0aNUqvv/66/vOf/+jnn39W//79FRAQoMjISLvmDQAAANyLYmNj9dBDD8nd3V0+Pj6KjIzUkSNHLMaEh4fLYDBYHEOHDrUYc+rUKXXu3Flly5aVj4+PXnjhBeXk5FiM2bp1q5o0aSIXFxdVr15dy5YtK+rLAwAAAGyiWD1YdO/evWrTpo35fMyYMZKkAQMGaNmyZXrxxReVkZGh6OhoXbp0SY888ojWr18vV1dXe6UMAAAA3LO2bdum4cOH66GHHlJOTo5efvlltW/fXocOHVK5cuXM44YMGaJJkyaZz8uWLWv+Ojc3V507d5afn5927dqlpKQk9e/fX2XKlNGUKVMk/bk4pnPnzho6dKiWL1+uzZs365lnnpG/v78iIiLu3gUDAAAAt6FYFdHDw8NlMpms9hsMBk2aNMniBh4AgOIuNTVVRqPRZvE8PDzk7e1ts3gASq/169dbnC9btkw+Pj6Kj49Xq1atzO1ly5a1+hyiDRs26NChQ9q0aZN8fX3VqFEjTZ48WS+99JImTJggZ2dnLViwQCEhIZoxY4YkqXbt2tq5c6dmzZpFER0AAADFXrEqogMAUNKkpqZqcPRQpV/NtFlMdzdXLVm4gEI6AJtLS0uTJFWqVMmiffny5fr444/l5+enrl276l//+pd5NXpcXJzq168vX19f8/iIiAgNGzZMv/zyixo3bqy4uDi1a9fOImZERIRGjRpVtBcEAAAA2ABFdAAAipDRaFT61UyF9xsmL//KdxzvfNIZbf1ovoxGI0V0ADaVl5enUaNGqWXLlqpXr565vU+fPgoKClJAQIAOHDigl156SUeOHNGqVaskScnJyRYFdEnm8+Tk5OuOMRqNunr1qtzc3Arkk5WVpaysLPO5LT/RAwAAANwKiugAANwFXv6V5RcUYu80AMCq4cOH6+DBg9q5c6dFe3R0tPnr+vXry9/fX23bttXx48dVrVq1IssnNjZWEydOLLL4AAAAwM1ysHcCAAAAAOxrxIgRWrNmjbZs2aLKla//qZlmzZpJko4dOyZJ8vPzU0pKisWY/PP8fdStjfHw8Ch0FbokxcTEKC0tzXycPn361i8MAAAAsAFWogMA8BfXrmUpMTHRZvESExOVk5Njs3j/j707j4uq+v84/h4GGFAWEUVEUdzXNNM0zLUw18zS3HNLzdIyTS0r10wry0rLpcUl03LJrNQyXMndNBdyQ3PNfQMEZZm5vz/8OV+nAQVlEXk9Hw8eMed87rmfe5j0zsfDuQCQkQzD0Msvv6wff/xRa9asUYkSt/+NmR07dkiSChcuLEkKDQ3Vu+++q7NnzyogIECSFB4eLh8fH1WsWNEes2zZModxwsPDFRoamup5LBaLLBbLnVwWAAAAkKEoogMA8P9iL1/U4UP/6K13xmZY4eZqfJxOnj6jpKTEDBkPADJS3759NXfuXP3000/y9va272Hu6+srT09PHTp0SHPnzlWzZs3k7++vXbt2acCAAapXr56qVKkiSXriiSdUsWJFPffcc/rggw90+vRpvf322+rbt6/9z9I+ffros88+05AhQ9SjRw+tWrVK8+fP19KlS7Pt2gEAAIC0oogOAMD/uxYfJxc3N9V/7kUVCcmYfX6jdmzVD5M/lNVqzZDxACAjTZkyRZLUoEEDh/YZM2aoW7ducnd314oVK/TJJ58oLi5OwcHBat26td5++217rNls1pIlS/Tiiy8qNDRUefPmVdeuXTV69Gh7TIkSJbR06VINGDBAn376qYoWLaqvvvpKjRs3zpLrBAAAAO4GRXQAAP7DPzAowx4Ceu4ke/gCuHcZhnHL/uDgYK1du/a24xQvXtxpu5b/atCggf7666905QcAAADcC3iwKAAAAAAAAAAAqaCIDgAAAAAAAABAKiiiAwAAAAAAAACQCoroAAAAAAAAAACkgiI6AAAAAAAAAACpoIgOAAAAAAAAAEAqKKIDAAAAAAAAAJAKiugAAAAAAAAAAKTCNbsTAAAA2evcuXOKiYnJsPF8fHxUsGDBDBsPAAAAAIDsRBEdAIBc7Ny5c+rRu49ir17LsDG9PT00/YupFNIBAAAAAPcFiugAAORiMTExir16TQ2ee1H+hYve9XgXTp3QmtlTFBMTQxEdAAAAAHBfoIgOAADkX7ioAouXyO40AAAAAAC45/BgUQAAAAAAAAAAUkERHQAAAAAAAACAVLCdCwAAOUxiYoKOHj2aIWMdPXpUycnJGTLWDRmZ3/XxEuXu7p5h4/n4+LBfOwAAAAAgzSiiAwCQg8RevqjDh/7RW++MlcViuevxrsbH6eTpM0pKSsyA7DI+v8TEBB0/ckTFS5aSq2vG3LZ4e3po+hdTKaQDAAAAANKEIjoAADnItfg4ubi5qf5zL6pISKm7Hi9qx1b9MPlDWa3WDMguc/I7OvlD1enYO0PGu3DqhNbMnqKYmBiK6AAAAACANKGIDgBADuQfGKTA4iXuepxzJ49nQDbOMjq/jBoPAAAAAID04sGiAAAAAAAAAACkgiI6AAAAAAAAAACpoIgOAAAAAAAAAEAqKKIDAAAAAAAAAJAKiugAAAAAAAAAAKSCIjoAAAAAAAAAAKmgiA4AAAAAAAAAQCpcszsBAACArJSYmKCjR49m6Jg+Pj4qWLBgho4JAADuP/Hx8dq3b1+mnmPv3r0O/81M5cuXV548eTL9PACQ3SiiAwCAXCP28kUdPvSP3npnrCwWS4aN6+3poelfTKWQDgAAbmnfvn2qXr16lpyrc+fOmX6Obdu26aGHHsr08wBAdqOIDgAAco1r8XFycXNT/edeVJGQUhky5oVTJ7Rm9hTFxMRQRAcAALdUvnx5bdu2LVPPcfXqVR05ckQhISHy9PTM1HOVL18+U8cHgHsFRXQAAJDr+AcGKbB4iexOAwAA5DJ58uTJkpXbjz76aKafAwByEx4sCgAAAAAAAABAKiiiAwAAAAAAAACQCrZzAQAAQLY6d+6cYmJiMmw8Hx8f9qcHAAAAkGEoogMAACDbnDt3Tj1691Hs1WsZNqa3p4emfzGVQjoAAACADEERHQAAANkmJiZGsVevqcFzL8q/cNG7Hu/CqRNaM3uKYmJiKKIDAAAAyBAU0QEAAJDt/AsXVWDxEtmdBgAAAAA44cGiAAAAAAAAAACkgiI6AAAAAAAAAACpYDsXAACAu5SYmKCjR49m2Hg+Pj7s5w0AAAAA9wiK6AAAAHch9vJFHT70j956Z6wsFkuGjOnt6aHpX0ylkA4AAAAA94AcW0T//PPPNX78eJ0+fVpVq1bVpEmTVLNmzexOCwAA5DLX4uPk4uam+s+9qCIhpe56vAunTmjN7CmKiYmhiA4AAAAA94AcWUSfN2+eBg4cqKlTp6pWrVr65JNP1LhxY+3fv18BAQHZnR4AAMiF/AODFFi8RHanAQAAAADIYDnywaITJkxQr1691L17d1WsWFFTp05Vnjx5NH369OxODQAAAAAAAABwH8lxRfTExERt27ZNYWFh9jYXFxeFhYVp48aN2ZgZAAAAAAAAAOB+k+O2czl//rysVqsKFSrk0F6oUCHt27fPKT4hIUEJCQn219HR0ZKkmJiYzE00BbGxsUpOTtLJQwd0Ne7KXY939thh2WxWnTx8UIbVynj32XiZMSbj3VvjZcaYjHdvjZcZYzLevTVeZox56fRJXY2P1549exQbG5sBGd7bjh8/roRr1zLs/ujS6ZNKTk5SbGxslt/v3TifYRhZet7c4sa8Zsd9PAAAAO5Pab2HNxk57C7/5MmTKlKkiDZs2KDQ0FB7+5AhQ7R27Vpt3rzZIX7kyJEaNWpUVqcJAACAXOr48eMqWrRodqdx3zlx4oSCg4OzOw0AAADch253D5/jVqIXKFBAZrNZZ86ccWg/c+aMAgMDneKHDh2qgQMH2l/bbDZdvHhR/v7+MplMmZ5vbhATE6Pg4GAdP35cPj4+2Z3OfYk5zhrMc9ZgnrMG85w1mOfMl5Pm2DAMxcbGKigoKLtTuS8FBQXp+PHj8vb25j4eAG4hJ/3dCQDZLa338DmuiO7u7q7q1atr5cqVatWqlaTrhfGVK1eqX79+TvEWi0UWi8WhLV++fFmQae7j4+PDX9CZjDnOGsxz1mCeswbznDWY58yXU+bY19c3u1O4b7m4uLDCHwDSIaf83QkA2S0t9/A5roguSQMHDlTXrl1Vo0YN1axZU5988oni4uLUvXv37E4NAAAAAAAAAHAfyZFF9Hbt2uncuXMaPny4Tp8+rQcffFC//fab08NGAQAAAAAAAAC4GzmyiC5J/fr1S3H7FmQ9i8WiESNGOG2bg4zDHGcN5jlrMM9Zg3nOGsxz5mOOAQBIH/7uBICMZzIMw8juJAAAAAAAAAAAuBe5ZHcCAAAAAAAAAADcqyiiAwAAAAAAAACQCoroAAAAAAAAAACkgiI6NG7cOD388MPy9vZWQECAWrVqpf379zvENGjQQCaTyeGrT58+DjHHjh1T8+bNlSdPHgUEBGjw4MFKTk52iFmzZo0eeughWSwWlS5dWjNnzszsy7tnjBw50mkOy5cvb++/du2a+vbtK39/f3l5eal169Y6c+aMwxjM8e2FhIQ4zbPJZFLfvn0l8V6+UxEREXryyScVFBQkk8mkxYsXO/QbhqHhw4ercOHC8vT0VFhYmKKiohxiLl68qE6dOsnHx0f58uXT888/rytXrjjE7Nq1S3Xr1pWHh4eCg4P1wQcfOOWyYMEClS9fXh4eHnrggQe0bNmyDL/e7HCrOU5KStLrr7+uBx54QHnz5lVQUJC6dOmikydPOoyR0vv/vffec4jJzXMs3f693K1bN6c5bNKkiUMM7+Xbu908p/TntMlk0vjx4+0xvJ8BAEif2/39CwC4cxTRobVr16pv377atGmTwsPDlZSUpCeeeEJxcXEOcb169dKpU6fsXzd/ULVarWrevLkSExO1YcMGzZo1SzNnztTw4cPtMYcPH1bz5s3VsGFD7dixQ6+++qp69uyp5cuXZ9m1ZrdKlSo5zOG6devsfQMGDNAvv/yiBQsWaO3atTp58qSeeeYZez9znDZbt251mOPw8HBJ0rPPPmuP4b2cfnFxcapatao+//zzFPs/+OADTZw4UVOnTtXmzZuVN29eNW7cWNeuXbPHdOrUSX///bfCw8O1ZMkSRUREqHfv3vb+mJgYPfHEEypevLi2bdum8ePHa+TIkfriiy/sMRs2bFCHDh30/PPP66+//lKrVq3UqlUrRUZGZt7FZ5FbzXF8fLy2b9+uYcOGafv27Vq0aJH279+vli1bOsWOHj3a4f398ssv2/ty+xxLt38vS1KTJk0c5vC7775z6Oe9fHu3m+eb5/fUqVOaPn26TCaTWrdu7RDH+xkAgLRLy30OAOAOGcB/nD171pBkrF271t5Wv359o3///qkes2zZMsPFxcU4ffq0vW3KlCmGj4+PkZCQYBiGYQwZMsSoVKmSw3Ht2rUzGjdunLEXcI8aMWKEUbVq1RT7Ll++bLi5uRkLFiywt+3du9eQZGzcuNEwDOb4TvXv398oVaqUYbPZDMPgvZwRJBk//vij/bXNZjMCAwON8ePH29suX75sWCwW47vvvjMMwzD27NljSDK2bt1qj/n1118Nk8lk/Pvvv4ZhGMbkyZMNPz8/+zwbhmG8/vrrRrly5eyv27ZtazRv3twhn1q1ahkvvPBChl5jdvvvHKdky5YthiTj6NGj9rbixYsbH3/8carHMMeOUprnrl27Gk899VSqx/BeTr+0vJ+feuop47HHHnNo4/0MAMCdS8vfvwCAtGMlOpxER0dLkvLnz+/QPmfOHBUoUECVK1fW0KFDFR8fb+/buHGjHnjgARUqVMje1rhxY8XExOjvv/+2x4SFhTmM2bhxY23cuDGzLuWeExUVpaCgIJUsWVKdOnXSsWPHJEnbtm1TUlKSw/yUL19exYoVs88Pc5x+iYmJ+vbbb9WjRw+ZTCZ7O+/ljHX48GGdPn3aYU58fX1Vq1Yth/dvvnz5VKNGDXtMWFiYXFxctHnzZntMvXr15O7ubo9p3Lix9u/fr0uXLtljmPvroqOjZTKZlC9fPof29957T/7+/qpWrZrGjx/vsBURc5w2a9asUUBAgMqVK6cXX3xRFy5csPfxXs54Z86c0dKlS/X888879fF+BgAAAHAvcM3uBHBvsdlsevXVV/Xoo4+qcuXK9vaOHTuqePHiCgoK0q5du/T6669r//79WrRokSTp9OnTDkVHSfbXp0+fvmVMTEyMrl69Kk9Pz8y8tGxXq1YtzZw5U+XKldOpU6c0atQo1a1bV5GRkTp9+rTc3d2dimGFChW67fzd6LtVTG6Z4/9avHixLl++rG7dutnbeC9nvBvzktKc3DxnAQEBDv2urq7Knz+/Q0yJEiWcxrjR5+fnl+rc3xgjt7h27Zpef/11dejQQT4+Pvb2V155RQ899JDy58+vDRs2aOjQoTp16pQmTJggiTlOiyZNmuiZZ55RiRIldOjQIb355ptq2rSpNm7cKLPZzHs5E8yaNUve3t4OW5hJvJ8BAAAA3DsoosNB3759FRkZ6bBXtySHvV4feOABFS5cWI8//rgOHTqkUqVKZXWaOVLTpk3t31epUkW1atVS8eLFNX/+/FxXdM0qX3/9tZo2baqgoCB7G+9l5HRJSUlq27atDMPQlClTHPoGDhxo/75KlSpyd3fXCy+8oHHjxslisWR1qjlS+/bt7d8/8MADqlKlikqVKqU1a9bo8ccfz8bM7l/Tp09Xp06d5OHh4dDO+xkAAADAvYLtXGDXr18/LVmyRKtXr1bRokVvGVurVi1J0sGDByVJgYGBOnPmjEPMjdeBgYG3jPHx8cmVReR8+fKpbNmyOnjwoAIDA5WYmKjLly87xJw5c+a283ej71YxuXGOjx49qhUrVqhnz563jOO9fPduzEtKc3LznJ09e9ahPzk5WRcvXsyQ9/iN/vvdjQL60aNHFR4e7rAKPSW1atVScnKyjhw5Iok5vhMlS5ZUgQIFHP6M4L2ccf744w/t37//tn9WS7yfAQAAAGQfiuiQYRjq16+ffvzxR61atcrpV6NTsmPHDklS4cKFJUmhoaHavXu3Q2HhRoGnYsWK9piVK1c6jBMeHq7Q0NAMupKc5cqVKzp06JAKFy6s6tWry83NzWF+9u/fr2PHjtnnhzlOnxkzZiggIEDNmze/ZRzv5btXokQJBQYGOsxJTEyMNm/e7PD+vXz5srZt22aPWbVqlWw2m/0fMkJDQxUREaGkpCR7THh4uMqVKyc/Pz97TG6d+xsF9KioKK1YsUL+/v63PWbHjh1ycXGxbz/CHKffiRMndOHCBYc/I3gvZ5yvv/5a1atXV9WqVW8by/sZAAAAQLbJ7iebIvu9+OKLhq+vr7FmzRrj1KlT9q/4+HjDMAzj4MGDxujRo40///zTOHz4sPHTTz8ZJUuWNOrVq2cfIzk52ahcubLxxBNPGDt27DB+++03o2DBgsbQoUPtMf/884+RJ08eY/DgwcbevXuNzz//3DCbzcZvv/2W5decHV577TVjzZo1xuHDh43169cbYWFhRoECBYyzZ88ahmEYffr0MYoVK2asWrXK+PPPP43Q0FAjNDTUfjxznHZWq9UoVqyY8frrrzu0816+c7GxscZff/1l/PXXX4YkY8KECcZff/1lHD161DAMw3jvvfeMfPnyGT/99JOxa9cu46mnnjJKlChhXL161T5GkyZNjGrVqhmbN2821q1bZ5QpU8bo0KGDvf/y5ctGoUKFjOeee86IjIw0vv/+eyNPnjzGtGnT7DHr1683XF1djQ8//NDYu3evMWLECMPNzc3YvXt31k1GJrnVHCcmJhotW7Y0ihYtauzYscPhz+qEhATDMAxjw4YNxscff2zs2LHDOHTokPHtt98aBQsWNLp06WI/R26fY8O49TzHxsYagwYNMjZu3GgcPnzYWLFihfHQQw8ZZcqUMa5du2Yfg/fy7d3uzwzDMIzo6GgjT548xpQpU5yO5/0MAED6peXvXwDAnaGIDkNSil8zZswwDMMwjh07ZtSrV8/Inz+/YbFYjNKlSxuDBw82oqOjHcY5cuSI0bRpU8PT09MoUKCA8dprrxlJSUkOMatXrzYefPBBw93d3ShZsqT9HLlBu3btjMKFCxvu7u5GkSJFjHbt2hkHDx6091+9etV46aWXDD8/PyNPnjzG008/bZw6dcphDOY4bZYvX25IMvbv3+/Qznv5zq1evTrFPye6du1qGIZh2Gw2Y9iwYUahQoUMi8ViPP74407zf+HCBaNDhw6Gl5eX4ePjY3Tv3t2IjY11iNm5c6dRp04dw2KxGEWKFDHee+89p1zmz59vlC1b1nB3dzcqVapkLF26NNOuOyvdao4PHz6c6p/Vq1evNgzDMLZt22bUqlXL8PX1NTw8PIwKFSoYY8eOdSj+GkbunmPDuPU8x8fHG0888YRRsGBBw83NzShevLjRq1cv4/Tp0w5j8F6+vdv9mWEYhjFt2jTD09PTuHz5stPxvJ8BAEi/tPz9CwC4MybDMIxMWuQOAAAAAAAAAECOxp7oAAAAAAAAAACkgiI6AAAAAAAAAACpoIgOAAAAAAAAAEAqKKIDAAAAAAAAAJAKiugAAAAAAAAAAKSCIjoAAAAAAAAAAKmgiA4AAAAAAAAAQCooogMAAAAAAAAAkAqK6ACAe9qRI0dkMpm0Y8eO7E4FAAAAAADkQhTRASCbmUymW36NHDnyjsdOawH6XilUd+vWTa1atcrWHAAAAAAAAG7mmt0JAEBud+rUKfv38+bN0/Dhw7V//357m5eXV3akBQAAAAAAALESHQCyXWBgoP3L19dXJpPJoe37779XhQoV5OHhofLly2vy5Mn2Y3v06KEqVaooISFBkpSYmKhq1aqpS5cukqQSJUpIkqpVqyaTyaQGDRrcUY42m03jxo1TiRIl5OnpqapVq2rhwoX2/jVr1shkMmnlypWqUaOG8uTJo9q1azv8Y4AkjRkzRgEBAfL29lbPnj31xhtv6MEHH5QkjRw5UrNmzdJPP/1kX4W/Zs0a+7H//POPGjZsqDx58qhq1arauHHjHV0LAAAAAABAelBEB4B72Jw5czR8+HC9++672rt3r8aOHathw4Zp1qxZkqSJEycqLi5Ob7zxhiTprbfe0uXLl/XZZ59JkrZs2SJJWrFihU6dOqVFixbdUR7jxo3TN998o6lTp+rvv//WgAED1LlzZ61du9Yh7q233tJHH32kP//8U66ururRo4fDtbz77rt6//33tW3bNhUrVkxTpkyx9w8aNEht27ZVkyZNdOrUKZ06dUq1a9d2GHvQoEHasWOHypYtqw4dOig5OfmOrgcAAAAAACCt2M4FAO5hI0aM0EcffaRnnnlG0vWV5Xv27NG0adPUtWtXeXl56dtvv1X9+vXl7e2tTz75RKtXr5aPj48kqWDBgpIkf39/BQYG3lEOCQkJGjt2rFasWKHQ0FBJUsmSJbVu3TpNmzZN9evXt8e+++679tdvvPGGmjdvrmvXrsnDw0OTJk3S888/r+7du0uShg8frt9//11XrlyRdH3bGk9PTyUkJKSY66BBg9S8eXNJ0qhRo1SpUiUdPHhQ5cuXv6PrAgAAAAAASAtWogPAPSouLk6HDh3S888/Ly8vL/vXmDFjdOjQIXtcaGioBg0apHfeeUevvfaa6tSpk6F5HDx4UPHx8WrUqJFDHt98841DHpJUpUoV+/eFCxeWJJ09e1aStH//ftWsWdMh/r+vb+VWYwMAAAAAAGQWVqIDwD3qxgrtL7/8UrVq1XLoM5vN9u9tNpvWr18vs9msgwcPZloeS5cuVZEiRRz6LBaLw2s3Nzf79yaTyZ5fRsjMsQEAAAAAAFJDER0A7lGFChVSUFCQ/vnnH3Xq1CnVuPHjx2vfvn1au3atGjdurBkzZti3THF3d5ckWa3WO86jYsWKslgsOnbsmMPWLelVrlw5bd261f7QU0naunWrQ4y7u/td5QoAAAAAAJDRKKIDwD1s1KhReuWVV+Tr66smTZooISFBf/75py5duqSBAwfqr7/+0vDhw7Vw4UI9+uijmjBhgvr376/69eurZMmSCggIkKenp3777TcVLVpUHh4e8vX1TfV8+/fvd2qrVKmSBg0apAEDBshms6lOnTqKjo7W+vXr5ePjo65du6bpWl5++WX16tVLNWrUUO3atTVv3jzt2rVLJUuWtMeEhIRo+fLl2r9/v/z9/W+ZKwAAAAAAQFagiA4A97CePXsqT548Gj9+vAYPHqy8efPqgQce0Kuvvqpr166pc+fO6tatm5588klJUu/evbV06VI999xzioiIkKurqyZOnKjRo0dr+PDhqlu3rtasWZPq+dq3b+/Udvz4cb3zzjsqWLCgxo0bp3/++Uf58uXTQw89pDfffDPN19KpUyf9888/GjRokK5du6a2bduqW7du2rJliz2mV69eWrNmjWrUqKErV65o9erVCgkJSfM5AAAAAAAAMprJMAwju5MAAOROjRo1UmBgoGbPnp3dqQAAAAAAAKSIlegAgCwRHx+vqVOnqnHjxjKbzfruu++0YsUKhYeHZ3dqAAAAAAAAqWIlOgAgS1y9elVPPvmk/vrrL127dk3lypXT22+/rWeeeSa7UwMAAAAAAEgVRXQAAAAAAAAAAFLhkt0JAAAAAAAAAABwr6KIDgAAAAAAAABAKiiiAwAAAAAAAACQCoroAAAAAAAAAACkgiI6AAAAAAAAAACpoIgOAAAAAAAAAEAqKKIDAAAAAAAAAJAKiugAAAAAAAAAAKSCIjoAAAAAAAAAAKmgiA4AAAAAAAAAQCooogMAAAAAAAAAkAqK6AAAAAAAAAAApIIiOgAAAAAAAAAAqaCIDgAAAAAAAABAKiiiA0Am6tatm0JCQjJ0zJkzZ8pkMunIkSMZOm5mGDlypEwmU5acq0GDBmrQoIH99Zo1a2QymbRw4cIsOX9m/KwBAACQ/f57n3m3QkJC1K1btwwbDwCQ+SiiA7jnHTp0SC+88IJKliwpDw8P+fj46NFHH9Wnn36qq1evZnd6mWbs2LFavHhxdqdhd6N4f+PLw8NDQUFBaty4sSZOnKjY2NgMOc/Jkyc1cuRI7dixI0PGy0j3cm4AAAD3o927d6tNmzYqXry4PDw8VKRIETVq1EiTJk1yiLvbe+c9e/Zo5MiRGbZQZcOGDRo5cqQuX76cIePdjXPnzql///4qX768PD09FRAQoJo1a+r111/XlStXsjs9AMgRTIZhGNmdBACkZunSpXr22WdlsVjUpUsXVa5cWYmJiVq3bp1++OEHdevWTV988UV2p5mqbt26ac2aNXd0M+7l5aU2bdpo5syZDu1Wq1VJSUmyWCxZtspbul5E7969u0aPHq0SJUooKSlJp0+f1po1axQeHq5ixYrp559/VpUqVezHJCcnKzk5WR4eHmk+z59//qmHH35YM2bMSNcKncTEREmSu7u7pOsr0Rs2bKgFCxaoTZs2aR7nTnNLSkqSzWaTxWLJkHMBAADkdhs2bFDDhg1VrFgxde3aVYGBgTp+/Lg2bdqkQ4cO6eDBg/bY1O6d02rhwoV69tlntXr1aqdV5/+9z0yLDz/8UIMHD9bhw4edflsxISFBLi4ucnNzu6Nc0+PixYuqVq2aYmJi1KNHD5UvX14XLlzQrl27tGTJEu3atYvfpgSANHDN7gQAIDWHDx9W+/btVbx4ca1atUqFCxe29/Xt21cHDx7U0qVLszHD7GE2m2U2m7Pt/E2bNlWNGjXsr4cOHapVq1apRYsWatmypfbu3StPT09Jkqurq1xdM/evmvj4eOXJkyddH2oyQ1Z8CAIAAMhN3n33Xfn6+mrr1q3Kly+fQ9/Zs2ezLI+Mvs/MykUXX3/9tY4dO6b169erdu3aDn0xMTHZfg+dHeLi4pQ3b97sTgNADsN2LgDuWR988IGuXLmir7/+2qGAfkPp0qXVv39/SdKRI0dkMplSXHliMpk0cuRI++sb+3QfOHBAnTt3lq+vrwoWLKhhw4bJMAwdP35cTz31lHx8fBQYGKiPPvrIYbzU9iS/sQf3mjVrbnldH374oWrXri1/f395enqqevXqTvt2m0wmxcXFadasWfbtU26sfP7v+Vu0aKGSJUumeK7Q0FCHgrckffvtt6pevbo8PT2VP39+tW/fXsePH79lzrfz2GOPadiwYTp69Ki+/fZbe3tKe6KHh4erTp06ypcvn7y8vFSuXDm9+eabkq7P4cMPPyxJ6t69u/3ab/xcGzRooMqVK2vbtm2qV6+e8uTJYz82tb0qrVar3nzzTQUGBipv3rxq2bKl0/Wmti/lzWPeLreU9kSPi4vTa6+9puDgYFksFpUrV04ffvih/vtLYCaTSf369dPixYtVuXJlWSwWVapUSb/99lvKEw4AAJALHDp0SJUqVXIqoEtSQECA/ftb3TsfPXpUL730ksqVKydPT0/5+/vr2WefdbiXnzlzpp599llJUsOGDe1j3LivT+k+c9KkSapUqZLy5MkjPz8/1ahRQ3PnzpV0/R548ODBkqQSJUrYx7txzpTuPS9fvqwBAwYoJCREFotFRYsWVZcuXXT+/Pk0nfNWc2g2m/XII4849fn4+Dj8xmha7oml/33umT9/vkaNGqUiRYrI29tbbdq0UXR0tBISEvTqq68qICBAXl5e6t69uxISEhzGvHH/u2DBAlWsWFGenp4KDQ3V7t27JUnTpk1T6dKl5eHhoQYNGjh99vrjjz/07LPPqlixYrJYLAoODtaAAQOctvvs1q2bvLy8dOjQITVr1kze3t7q1KmTRowYITc3N507d87penv37q18+fLp2rVrt5xbALkLK9EB3LN++eUXlSxZ0mnFREZp166dKlSooPfee09Lly7VmDFjlD9/fk2bNk2PPfaY3n//fc2ZM0eDBg3Sww8/rHr16mXIeT/99FO1bNlSnTp1UmJior7//ns9++yzWrJkiZo3by5Jmj17tnr27KmaNWuqd+/ekqRSpUqleh1dunTR1q1b7UVe6foHhk2bNmn8+PH2tnfffVfDhg1T27Zt1bNnT507d06TJk1SvXr19Ndff6X4ASWtnnvuOb355pv6/fff1atXrxRj/v77b7Vo0UJVqlTR6NGjZbFYdPDgQa1fv16SVKFCBY0ePVrDhw9X7969VbduXUlyeA9cuHBBTZs2Vfv27dW5c2cVKlTolnm9++67MplMev3113X27Fl98sknCgsL044dO+wr5tMiLbndzDAMtWzZUqtXr9bzzz+vBx98UMuXL9fgwYP177//6uOPP3aIX7dunRYtWqSXXnpJ3t7emjhxolq3bq1jx47J398/zXkCAADcL4oXL66NGzcqMjJSlStXTjXuVvfOW7du1YYNG9S+fXsVLVpUR44c0ZQpU9SgQQPt2bNHefLkUb169fTKK69o4sSJevPNN1WhQgVJsv/3v7788ku98soratOmjfr3769r165p165d2rx5szp27KhnnnlGBw4c0HfffaePP/5YBQoUkCQVLFgwxfGuXLmiunXrau/everRo4ceeughnT9/Xj///LNOnDihAgUK3Pact5pDq9Wq2bNnq2vXrref9HQYN26cPD099cYbb+jgwYOaNGmS3Nzc5OLiokuXLmnkyJHatGmTZs6cqRIlSmj48OEOx//xxx/6+eef1bdvX/t4LVq00JAhQzR58mS99NJLunTpkj744AP16NFDq1atsh+7YMECxcfH68UXX5S/v7+2bNmiSZMm6cSJE1qwYIHDeZKTk9W4cWPVqVNHH374ofLkyaPQ0FCNHj1a8+bNU79+/eyxiYmJWrhwoVq3bp2uLSkB5AIGANyDoqOjDUnGU089lab4w4cPG5KMGTNmOPVJMkaMGGF/PWLECEOS0bt3b3tbcnKyUbRoUcNkMhnvvfeevf3SpUuGp6en0bVrV3vbjBkzDEnG4cOHHc6zevVqQ5KxevVqe1vXrl2N4sWLO8TFx8c7vE5MTDQqV65sPPbYYw7tefPmdThvauePjo42LBaL8dprrznEffDBB4bJZDKOHj1qGIZhHDlyxDCbzca7777rELd7927D1dXVqT21827dujXVGF9fX6NatWr21zfm+oaPP/7YkGScO3cu1TG2bt2a6s+yfv36hiRj6tSpKfbVr1/f/vrGz6NIkSJGTEyMvX3+/PmGJOPTTz+1txUvXjzFuf7vmLfK7b8/68WLFxuSjDFjxjjEtWnTxjCZTMbBgwftbZIMd3d3h7adO3cakoxJkyY5nQsAACA3+P333w2z2WyYzWYjNDTUGDJkiLF8+XIjMTHRKTa1e+f/3nsbhmFs3LjRkGR888039rYFCxY43cvf8N97wqeeesqoVKnSLXMfP358ip8ZDMP53nP48OGGJGPRokVOsTabLc3nTMnp06eNggULGpKM8uXLG3369DHmzp1rXL58+bZ53ZDafXblypUdfhYdOnQwTCaT0bRpU4fjQ0NDnT4TSTIsFovD/EybNs2QZAQGBjrcvw8dOtRpLlP6uY4bN87h849hXL9Hl2S88cYbTvGhoaFGrVq1HNoWLVqU6vsAQO7Gdi4A7kkxMTGSJG9v70w7R8+ePe3fm81m1ahRQ4Zh6Pnnn7e358uXT+XKldM///yTYee9efXzpUuXFB0drbp162r79u13NJ6Pj4+aNm2q+fPnO2wTMm/ePD3yyCMqVqyYJGnRokWy2Wxq27atzp8/b/8KDAxUmTJltHr16ru7MF1/oFNsbGyq/TdWuv/000+y2Wx3dA6LxaLu3bunOb5Lly4O76M2bdqocOHCWrZs2R2dP62WLVsms9msV155xaH9tddek2EY+vXXXx3aw8LCHH7boEqVKvLx8cnQ9x4AAEBO0qhRI23cuFEtW7bUzp079cEHH6hx48YqUqSIfv755zSNcfO9d1JSki5cuKDSpUsrX758d3z/nS9fPp04cUJbt269o+P/64cfflDVqlX19NNPO/Xd2BrxTs9ZqFAh7dy5U3369NGlS5c0depUdezYUQEBAXrnnXecthlMjy5dujg8F6hWrVoyDEM9evRwiKtVq5aOHz+u5ORkh/bHH3/cYTvEWrVqSZJat27tcP9+o/3m++Kbf65xcXE6f/68ateuLcMw9Ndffznl+uKLL6aY/+bNm3Xo0CF725w5cxQcHKz69evf8toB5D4U0QHck3x8fCTplgXZu3WjuHyDr6+vPDw87L9ueXP7pUuXMuy8S5Ys0SOPPCIPDw/lz59fBQsW1JQpUxQdHX3HY7Zr107Hjx/Xxo0bJV3f+3Dbtm1q166dPSYqKkqGYahMmTIqWLCgw9fevXsz5OFMV65cueU/fLRr106PPvqoevbsqUKFCql9+/aaP39+ugrqRYoUSdcDkMqUKePw2mQyqXTp0k77Kma0o0ePKigoyGk+bvxa8NGjRx3a//t+lCQ/P78Mfe8BAADkNA8//LAWLVqkS5cuacuWLRo6dKhiY2PVpk0b7dmz57bHX716VcOHD7c/o6ZAgQIqWLCgLl++fMf336+//rq8vLxUs2ZNlSlTRn379rVvT3gnDh06dMvtau72nIULF9aUKVN06tQp7d+/XxMnTlTBggU1fPhwff3113ecd0qfpyQpODjYqd1msznNd3qOl+RwX3zs2DF169ZN+fPnl5eXlwoWLGgvfP/3PK6uripatKhT/u3atZPFYtGcOXPsxy1ZskSdOnVyeq4TAFBEB3BP8vHxUVBQkCIjI9MUn9pNjtVqTfUYs9mcpjZJDis07uRcN/zxxx9q2bKlPDw8NHnyZC1btkzh4eHq2LHjXa0CefLJJ5UnTx7Nnz9fkjR//ny5uLjYH5AkSTabTSaTSb/99pvCw8OdvqZNm3bH55ekEydOKDo6WqVLl041xtPTUxEREVqxYoWee+457dq1S+3atVOjRo3SNH83xshod/MzzShpee8BAADkVu7u7nr44Yc1duxYTZkyRUlJSU57X6fk5Zdf1rvvvqu2bdtq/vz5+v333xUeHi5/f/87/s3IChUqaP/+/fr+++9Vp04d/fDDD6pTp45GjBhxR+Nl1TlNJpPKli2rl19+WREREXJxcbEXkG/0pyS1e+LU7l/Tel97p8dbrVY1atRIS5cu1euvv67FixcrPDxcM2fOlCSnn6vFYpGLi3P5y8/PTy1atLDPwcKFC5WQkKDOnTuneH4AuRsPFgVwz2rRooW++OILbdy4UaGhobeM9fPzk3T9qfY3++9q34xwN+f64Ycf5OHhoeXLl8tisdjbZ8yY4RSbntUPefPmVYsWLbRgwQJNmDBB8+bNU926dRUUFGSPKVWqlAzDUIkSJVS2bNk0j51Ws2fPliQ1btz4lnEuLi56/PHH9fjjj2vChAkaO3as3nrrLa1evVphYWEZvuojKirK4bVhGDp48KCqVKlib/Pz83P6eUrXf6YlS5a0v05PbsWLF9eKFSsUGxvrsBp937599n4AAACkX40aNSRJp06dsreldp+2cOFCde3aVR999JG97dq1a073fum9B82bN6/atWundu3aKTExUc8884zeffddDR06VB4eHukar1SpUmlaPHS7c6ZHyZIl5efn5zCHab0nzm67d+/WgQMHNGvWLHXp0sXeHh4enu6xunTpoqeeekpbt27VnDlzVK1aNVWqVCkj0wVwn2AlOoB71pAhQ5Q3b1717NlTZ86cceo/dOiQPv30U0nXV64XKFBAERERDjGTJ0/O8Lxu7Ft987msVqu++OKL2x5rNptlMpkcVnMcOXJEixcvdorNmzdvijexqWnXrp1Onjypr776Sjt37nTYykWSnnnmGZnNZo0aNcppFYhhGLpw4UKaz/Vfq1at0jvvvKMSJUqoU6dOqcZdvHjRqe3BBx+UJCUkJEi6ft2S8z9S3KlvvvnGYVughQsX6tSpU2ratKm9rVSpUtq0aZMSExPtbUuWLNHx48cdxkpPbs2aNZPVatVnn33m0P7xxx/LZDI5nB8AAADOVq9eneJv5d14tk25cuXsbandO5vNZqcxJk2a5LS6Oj33ef+9b3Z3d1fFihVlGIaSkpLSPV7r1q21c+dO/fjjj059N3JPyzlTsnnzZsXFxTm1b9myRRcuXHCYw7TeE2e3GyvVb/65GoZh/2yYHk2bNlWBAgX0/vvva+3ataxCB5AqVqIDuGeVKlVKc+fOVbt27VShQgV16dJFlStXVmJiojZs2KAFCxaoW7du9viePXvqvffeU8+ePVWjRg1FRETowIEDGZ5XpUqV9Mgjj2jo0KG6ePGi8ufPr++//97pQTkpad68uSZMmKAmTZqoY8eOOnv2rD7//HOVLl1au3btcoitXr26VqxYoQkTJigoKEglSpSwP1QnJc2aNZO3t7cGDRoks9ms1q1bO/SXKlVKY8aM0dChQ3XkyBG1atVK3t7eOnz4sH788Uf17t1bgwYNuu01/Prrr9q3b5+Sk5N15swZrVq1SuHh4SpevLh+/vnnW66CGT16tCIiItS8eXMVL15cZ8+e1eTJk1W0aFHVqVPHnme+fPk0depUeXt7K2/evKpVq5ZKlChx29xSkj9/ftWpU0fdu3fXmTNn9Mknn6h06dLq1auXPaZnz55auHChmjRporZt2+rQoUP69ttvHR70md7cnnzySTVs2FBvvfWWjhw5oqpVq+r333/XTz/9pFdffdVpbAAAADh6+eWXFR8fr6efflrly5e3fw6YN2+eQkJCHB42n9q9c4sWLTR79mz5+vqqYsWK2rhxo1asWCF/f3+Hcz344IMym816//33FR0dLYvFoscee0wBAQFOeT3xxBMKDAzUo48+qkKFCmnv3r367LPP1Lx5c/tvIFavXl2S9NZbb6l9+/Zyc3PTk08+aS+u32zw4MFauHChnn32WfXo0UPVq1fXxYsX9fPPP2vq1KmqWrVqms6ZktmzZ2vOnDl6+umnVb16dbm7u2vv3r2aPn26PDw89Oabb9pj03pPnN3Kly+vUqVKadCgQfr333/l4+OjH3744Y6eJeTm5qb27dvrs88+k9lsVocOHTIhYwD3BQMA7nEHDhwwevXqZYSEhBju7u6Gt7e38eijjxqTJk0yrl27Zo+Lj483nn/+ecPX19fw9vY22rZta5w9e9aQZIwYMcIeN2LECEOSce7cOYfzdO3a1cibN6/T+evXr29UqlTJoe3QoUNGWFiYYbFYjEKFChlvvvmmER4ebkgyVq9e7TBm8eLFHY79+uuvjTJlyhgWi8UoX768MWPGDHtON9u3b59Rr149w9PT05BkdO3a1TAMw5gxY4YhyTh8+LBTrp06dTIkGWFhYanO5w8//GDUqVPHyJs3r5E3b16jfPnyRt++fY39+/eneszN573x5e7ubgQGBhqNGjUyPv30UyMmJsbpmP9e18qVK42nnnrKCAoKMtzd3Y2goCCjQ4cOxoEDBxyO++mnn4yKFSsarq6uhiRjxowZhmGk/LO4oX79+kb9+vXtr1evXm1IMr777jtj6NChRkBAgOHp6Wk0b97cOHr0qNPxH330kVGkSBHDYrEYjz76qPHnn386jXmr3FL6WcfGxhoDBgwwgoKCDDc3N6NMmTLG+PHjDZvN5hAnyejbt69TTsWLF7f/3AEAAHKbX3/91ejRo4dRvnx5w8vLy3B3dzdKly5tvPzyy8aZM2ccYlO7d7506ZLRvXt3o0CBAoaXl5fRuHFjY9++fSneZ3355ZdGyZIlDbPZ7HBf/997wmnTphn16tUz/P39DYvFYpQqVcoYPHiwER0d7TDeO++8YxQpUsRwcXFxuH9P6dwXLlww+vXrZxQpUsRwd3c3ihYtanTt2tU4f/58us75X7t27TIGDx5sPPTQQ0b+/PkNV1dXo3Dhwsazzz5rbN++3Sk+LffEN+6zFyxY4HDsjc8LW7dudWhP6fNXSve/hw8fNiQZ48ePd2hP6Xx79uwxwsLCDC8vL6NAgQJGr169jJ07dzrcnxtG6p/xbrZlyxZDkvHEE0/cMg5A7mYyDJ5YBgAAAAAAgNxn586devDBB/XNN9/oueeey+50ANyj2BMdAAAAAAAAudKXX34pLy8vPfPMM9mdCoB7GHuiAwAAAAAAIFf55ZdftGfPHn3xxRfq169fivvVA8ANbOcCAAAAAACAXCUkJERnzpxR48aNNXv27Fs+oBUAKKIDAAAAAAAAAJAK9kQHAAAAAAAAACAVFNEBAAAAAAAAAEhFrnuwqM1m08mTJ+Xt7S2TyZTd6QAAAOA+YRiGYmNjFRQUJBcX1qoAAAAA94tcV0Q/efKkgoODszsNAAAA3KeOHz+uokWLZncaAAAAADJIriui33ja8vHjx+Xj45PN2QAAAOB+ERMTo+DgYPv9JgAAAID7Q64rot/YwsXHx4ciOgAAADIcWwYCAAAA9xc2awQAAAAAAAAAIBUU0QEAAAAAAAAASAVFdAAAAAAAAAAAUkERHQAAAAAAAACAVFBEBwAAAAAAAAAgFRTRAQAAAAAAAABIBUV0AAAAAAAAAABSQREdAAAAAAAAAIBUUEQHAAAAAAAAACAVFNEBAAAAAAAAAEgFRXQAAAAAAAAAAFKRrUX0iIgIPfnkkwoKCpLJZNLixYtve8yaNWv00EMPyWKxqHTp0po5c2am5wkAAAAAAAAAyJ2ytYgeFxenqlWr6vPPP09T/OHDh9W8eXM1bNhQO3bs0KuvvqqePXtq+fLlmZwpAAAAAAAAACA3cs3Okzdt2lRNmzZNc/zUqVNVokQJffTRR5KkChUqaN26dfr444/VuHHjzEoTAAAAOYQ1Mkq2g8fkUrqYzJXLZHc6AAAAAO4D2VpET6+NGzcqLCzMoa1x48Z69dVXUz0mISFBCQkJ9tcxMTGZlR4AAMAdyczCr2EYkv1LN31/05cthbabYo3U+myGpP+O5XwO4zbntx09KePfszIF5JepcEHJapWSrf//X5tktcqwv/7ffw2rVbLarrclW2XExMo4df76nEZsk3o8TSEdAAAAwF3LUUX006dPq1ChQg5thQoVUkxMjK5evSpPT0+nY8aNG6dRo0ZlVYoAAOA+ZY2MUvKaLdK1JJlKFJFLYIH/FXSttv8VdB3+a5Xh0GZzijeuxEkXoq+fI2KbkrzzyuTuduvi9X8L3/r/vpSK4TnJ3n8ybiyTSbZDxymiAwAAALhrOaqIfieGDh2qgQMH2l/HxMQoODg4GzMCAADZ4cZqb1OJojKXCpZxNUG6dk3G1UTpWoKMq9dkO/yvjNPnZfLKI1Nez/+PSZDtYrR0Mdo+lnHyrGyZlWhsnLK19G2SZDKl8nWLPpfr/zWlJfamPuNyrBQb97/z+/nKpUiA5Ooimc0ymc2Sq1m66b8ms8v172+0m80yuZplO3lW1rV/Xh/bMORSins+AAAAAHcvRxXRAwMDdebMGYe2M2fOyMfHJ8VV6JJksVhksViyIj0AAJDNrJFRsu0/IlNBP5n8fGX9O0rG8TMyEhL/VwSP2Kbk24yTpiJ2gXxyCS4smV2uF3rNLv9f0P3ffx3aXc2Si4tju6tZtuOnZQ3faC/8ujav9//F3xvFad2iGH1TAVu6qYD9v6J2ugrhpv8fIwtZI6OUNP3H6/naDLk9/dgdrx43S3IpFSzboeNyKRXMKnQAAAAAGSJHFdFDQ0O1bNkyh7bw8HCFhoZmU0YAACAj3WpvcMMwlDT9R9mO/CuTj5dMBf2uryC/9v8ryWPjpKsJqYycAoub5GGRycMieXrIiI6VLv3v2SmmEkVkfqiiTJ4W2c5cuF7ovolby4YZUqQ1Vy4jl+DAXFv4NVcuI/V4OsOu31y5TK6bQwAAAACZK1uL6FeuXNHBgwftrw8fPqwdO3Yof/78KlasmIYOHap///1X33zzjSSpT58++uyzzzRkyBD16NFDq1at0vz587V06dLsugQAAHCXrJFRsm7YcX3LlLMXr7dFbFNykYDrK7evJciIvybFXbUfY8RdlXHq3K0HdnOVkv6z5vzGau9ureRapaxTHjeviHZtWNNejDVLcgkOlHXzbkmGzLWqZGihNrcXfnP79QMAAAC4t5kMI/ueOLVmzRo1bNjQqb1r166aOXOmunXrpiNHjmjNmjUOxwwYMEB79uxR0aJFNWzYMHXr1i3N54yJiZGvr6+io6Pl4+OTAVcBAADSwhoZJev6v2QkJsklpIhM+bxlO3hctt0H7mxAX2+5Na8neVpksrjL9u8ZJf+02l4oN4c9IuuKTfaiuDnsESkp+Zarna2RUbl2RTjuHveZAAAAwP0pW4vo2YEPNwAAZDxrZJSsm3bKuJoolzLF5VIo//WHcl5NkHEtQca/Z2TbdzhdY5rrVpdL2eIyeV7fbiV5407Z1m3/X3/YI3JrVs8pj5uL4BTFkZW4zwQAAADuTxTRAQDAHbmxf7lhNsu2ekv6ByjgJ5eA/LLtOeTQ7FKpVKrbpSQti5Bt72G5VCjhVEAHshv3mQAAAMD9KUc9WBQAAGSOmx/o6VKp9PW9xG88tDMhQda9h2UcPSn5esvknUfG0ZOyHTia+oC+XnIJDpQ8PWTysMiIjZNtxz6HELeWDeyrxdO617hbs3oSxXMAAAAAQBaiiA4AQC5gjYySLeqYjIREGZdjZfLJK1NeTxlX4mWcPi/j37PX4yK2SSZJd/l7am6tGzkVw60PVUixWM5DJQEAAAAA9zKK6AAA5HDWyCjZDhyVqWghuRQNlBEXL8Vfk/XAERnHT8tITJLOXnQ45pY18hudJkkWi2TYpIQke7cpqKBMgQVk277X/hDPm5nDHkmxKE6xHAAAAACQE1FEBwDgHpe8O0rG3kMyFfCTyc9H1r3/XF85bnaRcSVeuhx7R+OaihaSuVoFGZdjZf1jm70g7tq+icxVy0vubjKZTLJGRilp+o+Si0myGXJtUuf6NiwPlrc/tFMSD/AEAAAAANyXeLAoAAD3kOv7g++SkZgsl+KFr2/BcvRk2gdwNcuU3/f6XuYxV1KO+f9iuVuPp+0Fb2tk1C2L4LfrB8B9JgAAAHC/oogOAEA2uPlBnoZhyBZ5UIq/JtvfB299oLublPj/W6uYTDIVC5Rx9JRTYfy/q8fNYY9cf1iom6uUlEwxHMgE3GcCAAAA9ye2cwEAIIslfLVQxp5/JP3/gzzTwVyvuqwrNv1va5XHH5HkvJWKuXIZqcfTrB4HAAAAAOAuUUQHACAT3LzS3KVCKSkuXtbte5W8dqsUnco2K6kxSTKuP7DTrVk9uRQrnHLR/D94kCcAAAAAAHePIjoAAHch8Zc1su06IJdC/nIpWVRGbJxsx8/I+Oe4pHSsNL9p2xXj1HlJhsy1qkhKeZU5xXEAAAAAALIGRXQAANLIGhl1/UGfScnSpWjZLlyWzl+WJNkuXJZtz6F0j+lStazM1SvdctsVCuYAAAAAAGQfiugAAKTgxnYspsIFZcTEybp9j3Tmwq0P8rTIXKuKjLirsm2NtD/s07VDM8nDXckzFttXnMvfV+ZqFeTWrJ4kCuUAAAAAANyrKKIDAKD/L5rv+UdGQoJs/56Vzl5M9xjmR6vZi+LWB8o4rS438aBPAAAAAAByHIroAIBcLXn7XiX/tEqKjUvfgf+/ytylalkZ56PlUqGEvYAupbxvOXuZAwAAAACQ81BEBwDkKje2aXEpXUzJ6/6SceBI2g78/21YXCqVkqlwQSkpmRXlAAAAAADkAhTRAQC5RtKyCFlXbJIkWSO2pR54Y5V5pVIy16oiSWzDAgAAAABALkURHQBw37qx6twwm2Uc+VfG4X9ve4xL1bIy5fNxKphTPAcAAAAAIHeiiA4AuG9YI6OUHPGndDVBcnWVcfRk2g8ukE9uLRtSLAcAAAAAAA4oogMAcjxrZJSSfl0nnTqXruNSeygoAAAAAADADRTRAQA5ljUySklL1kpnL94++P/3OTcFB8rkk1fmWlVYdQ4AAAAAAG6LIjoAIEdKmDJPRtTRW8aYwx6RkpIlN1cpKZkHgwIAAAAAgHSjiA4AyDGSd+5X8u8bpAuXpcSkVONMxQLlGhZKwRwAAAAAANw1iugAgBwh8efVsq3ZessYiucAAAAAACCjUUQHANzTrJFRSvr+Vyn+WupBBfLJrWVDiucAAAAAACDDUUQHAGQ7a2SUbAePyaV0MXsh3DAMJS1eKdsf21M/0N9X5moV5NasXhZlCgAAAAAAchuK6ACAbGONjJJ13V+yHThy/XXENiX5eks2mxQXL9mMVI81hz1C8RwAAAAAAGQ6iugAgGyR8PUiGX8fdO6Ijr31gWzdAgAAAAAAshBFdABAlrJGRinpxxXSpZSL5abiQXJr84Rsx08ref5vkkmSIck7j8y1qrD6HAAAAAAAZCmK6ACALJPwxQIZ+w7fMsb18VpyKRIglyIBMnl5ynbouFxKBbPyHAAAAAAAZAuK6ACATJe8Y5+SfwiX4q6m2G8KCZJL8SCnYrm5chmK5wAAAAAAIFtRRAcAZKqEid/KOHIy1X6XqmXl3rVV1iUEAAAAAACQDhTRAQCZJuGz71IvoPOAUAAAAAAAkANQRAcAZChrZJSSlqyVLkZLydYUY1h9DgAAAAAAcgqK6ACADGGNjFLy8vUy/j2bepCHRW4dm7H6HAAAAAAA5BgU0QEAd80aGaWk6T/eNo4COgAAAAAAyGkoogMA0iV5d5Ssa7dKSckyFSssFx8vJUdsTf2AAvnkUshf5lpVKKADAAAAAIAchyI6ACBNrJFRSlr2h3T6vL3NOH5attQO8M4jc60qcmtWL0vyAwAAAAAAyAwU0QEAt5S8O0rJv6yRzl9Ksd9UyF/ysMg4etLexoNDAQAAAADA/YIiOgDAzhoZJdvBYzKFFJFL4YJKXr5eth37bnmMa/N6Mlcuo6RlEbLtPSyXCiVYfQ4AAAAAAO4bJsMwjOxOIivFxMTI19dX0dHR8vHxye50AOCekbg0QraVm9IcbyoWKNewUPY5B4D/x30mAAAAcH9iJToA5FLJu6Nk27lPRkycjDMXpNi4NB1H8RwAAAAAAOQmFNEBIJdIXLJWts27JBmSi/m2RXNTcKCM46cl0/VDKJ4DAAAAAIDciCI6AOQCiUsjZFu1OV3HuDYKlSTZDh2XS6lgiucAAAAAACBXoogOAPc5a2SUbKu33DrIZJIMQ6bgQJl88spcq4q9aE7xHAAAAAAA5GYU0QHgPmSNjFJyxDYZF6Oli9EpxpjDHpGSkiU3VykpmdXmAAAAAAAAKaCIDgD3maRlEbKu2HTLGJeqZeXWrF4WZQQAAAAAAJBzuWR3AgCAjJPWArp711ZZkxAAAAAAAEAOx0p0ALhPWCOjUi2gp7TXOQAAAAAAAG6PIjoA5HDJu6Nk231Att0HUuw3hz3C1i0AAAAAAAB3iCI6AORQiTMWy7b/HykxOdUYCugAAAAAAAB3hyI6AOQgiUsjZNu8S4q/Jtlst4zl4aEAAAAAAAB3jyI6ANzjrJFRsq7fIdvJs1JsXJqO4eGhAAAAAAAAGYMiOgDco6yRUUr+bZ2Mk+duG2sqFiiXsiFSUrJcSgXz8FAAAAAAAIAMQhEdAO5B1sgoJU3/8faB/r4yV6vAti0AAAAAAACZhCI6ANwjrJFRsh08JlOJokpevDL1QO88Mvn5yDUslBXnAAAAAAAAmYwiOgDcAxxWnkdsSzHGVCyQwjkAAAAAAEAWo4gOANnMGhmlpG+X3DKGB4UCAAAAAABkD4roAJCNEn9ZI9vqLbeMoYAOAAAAAACQfSiiA0AWsEZGybb/iEwF/GTy87n+OuqYFB2bYrxL1bIy5fORS6lgtm8BAAAAAADIRhTRASCD3HgwqEvpYjJXLqPk7Xtl231AtnOXpJNn0zwOK88BAAAAAADuHRTRASADJC2LkHXFJkmSNWKbklxdpeTklIPdXKWklPvMYY/IrVm9zEoTAAAAAAAA6eSS3Ql8/vnnCgkJkYeHh2rVqqUtW269N/Ann3yicuXKydPTU8HBwRowYICuXbuWRdkCgDNrZJS9gG6XWgFdkrl+jevfuJgkSabgQLlUKiW3Hk9TQAcAAAAAALjHZOtK9Hnz5mngwIGaOnWqatWqpU8++USNGzfW/v37FRAQ4BQ/d+5cvfHGG5o+fbpq166tAwcOqFu3bjKZTJowYUI2XAEASEmLVzk3BhaQTp+XTJKM/zXfWGnuUqywbIeOs+c5AAAAAADAPc5kGIZx+7DMUatWLT388MP67LPPJEk2m03BwcF6+eWX9cYbbzjF9+vXT3v37tXKlSvtba+99po2b96sdevWpemcMTEx8vX1VXR0tHx8fDLmQgDkWtbIKCVN/9Gp3a3H05Ik26Hj9u1bKJgDwP2N+0wAAADg/pRtK9ETExO1bds2DR061N7m4uKisLAwbdy4McVjateurW+//VZbtmxRzZo19c8//2jZsmV67rnnUj1PQkKCEhIS7K9jYmIy7iIA5ErJu6NkXblJxrUEKfqKc0ABP3uxnKI5AAAAAABAzpZtRfTz58/LarWqUKFCDu2FChXSvn37UjymY8eOOn/+vOrUqSPDMJScnKw+ffrozTffTPU848aN06hRozI0dwC5lzUySskznFee38ytZYOsSQYAAAAAAACZLtsfLJoea9as0dixYzV58mRt375dixYt0tKlS/XOO++keszQoUMVHR1t/zp+/HgWZgzgfmGNjFLC598padZPt4xzqVqW1ecAAAAAAAD3kWxbiV6gQAGZzWadOXPGof3MmTMKDAxM8Zhhw4bpueeeU8+ePSVJDzzwgOLi4tS7d2+99dZbcnFx/jcBi8Uii8WS8RcAINdI+GqhjD3/pNrvUrWsjPPRcqlQQm7N6mVhZgAAAAAAAMhs2VZEd3d3V/Xq1bVy5Uq1atVK0vUHi65cuVL9+vVL8Zj4+HinQrnZbJYkZePzUQHcp6yRUUpa+LsUE5dqjEvVsnLv2irrkgIAAAAAAECWyrYiuiQNHDhQXbt2VY0aNVSzZk198skniouLU/fu3SVJXbp0UZEiRTRu3DhJ0pNPPqkJEyaoWrVqqlWrlg4ePKhhw4bpySeftBfTAeBuWSOjlBy+Ucbx06kH+fvKXK0CK88BAAAAAADuc9laRG/Xrp3OnTun4cOH6/Tp03rwwQf122+/2R82euzYMYeV52+//bZMJpPefvtt/fvvvypYsKCefPJJvfvuu9l1CQDuM4lL1si2akvqAR4WuXVsxr7nAAAAAAAAuYTJyGX7oMTExMjX11fR0dHy8fHJ7nQA3EMSZvwoY3fULWPcejxNAR0AkCLuMwEAAID7U7auRAeAe8W1T2dLR0+l3Fkgn1wK+ctcqwoFdAAAAAAAgFyGIjqAXC9pWUSqBXRz2CPsew4AAAAAAJCLUUQHkKtZI6NkXZ3CHugF8smtZUNWngMAAAAAAORyFNEB5FrWyCglTf/RucPNVR5v9s76hAAAAAAAAHDPccnuBAAguyTN+zXFdnP9GlmcCQAAAAAAAO5VrEQHkOskLYuQdcMOKf6aU59L1bLsgQ4AAAAAAAA7iugAcpWkZRGyrtiUYp9L1bJy79oqaxMCAAAAAADAPY3tXADkKta1f6bc4Z2XAjoAAAAAAACcUEQHkGskLYuQkpJT7HN79okszgYAAAAAAAA5Adu5AMg1rJt2ObWZigXKNSxU5splsiEjAAAAAAAA3OsoogPIPeKuOr52McnyapfsyQUAAAAAAAA5Atu5AMgVkpZFSIbh2JjHM3uSAQAAAAAAQI5BER1ArmBds9WpzfxIlWzIBAAAAAAAADkJRXQA971r42dIyVbHRjdXuTWrlz0JAQAAAAAAIMdgT3QA9y1rZJSS5iyVEhKd+sz1a2RDRgAAAAAAAMhpKKIDuC8lzPhRxu6oFPtMIUGsQgcAAAAAAECaUEQHcN+59uls6eipFPtMIUGyvNI5izMCAAAAAABATsWe6ADuK0nLIiigAwAAAAAAIMOwEh3AfcX6x3bnRg+LzHWqsYULAAAAAAAA0o0iOoD7RsKMH50fIurqIo+x/bMnIQAAAAAAAOR4bOcC4L6QMHNxig8SNRUulA3ZAAAAAAAA4H7BSnQAOVry7igl/7JGOn8pxX7XRo9kaT4AAAAAAAC4v6S7iB4XF6e8efNmRi4AkC7WyCglz/gx5U4Pi9w6NpO5cpmsTQoAAAAAAAD3lXRv51KoUCH16NFD69aty4x8ACDNkuYsTbWPAjoAAAAAAAAyQrqL6N9++60uXryoxx57TGXLltV7772nkydPZkZuAJCqa2OnOT9EVJKpWKDcejxNAR0AAAAAAAAZwmQYhnEnB547d06zZ8/WzJkztXfvXjVu3Fg9evRQy5Yt5ep67261HhMTI19fX0VHR8vHxye70wGQTolLI2TbsF266lxAd6laVu5dW2V9UgAAiPtMAAAA4H51x0X0m02aNEmDBw9WYmKiChQooD59+uiNN95Qnjx5MiLHDMWHGyDnSpy1WLadB1LuDMgvjzd6Zm1CAADchPtMAAAA4P50x0vGz5w5o1mzZmnmzJk6evSo2rRpo+eff14nTpzQ+++/r02bNun333/PyFwB5GLWyCgK6AAAAAAAAMhy6S6iL1q0SDNmzNDy5ctVsWJFvfTSS+rcubPy5ctnj6ldu7YqVKiQkXkCyOWS5v3m3OjmKnP9GnJrVi/rEwIAAAAAAECukO4ievfu3dW+fXutX79eDz/8cIoxQUFBeuutt+46OQCQpGsfz5Lirjq1uz33JA8QBQAAAAAAQKZK957o8fHx9+Re52nFXpVAzmGNjFLSgt+l2DinPh4iCgC413CfCQAAANyf0r0S3dvbW6dOnVJAQIBD+4ULFxQQECCr1ZphyQHInZJ37lfy0gjp/KUU+00hQRTQAQAAAAAAkCXSXURPbeF6QkKC3N3d7zohALlX4tII2f7YJiUmpRpjCgmS5ZXOWZgVAAAAAAAAcrM0F9EnTpwoSTKZTPrqq6/k5eVl77NarYqIiFD58uUzPkMA9z1rZJSSFq+ULsbcMo4tXAAAAAAAAJDV0lxE//jjjyVdX4k+depUmc1me5+7u7tCQkI0derUjM8QwH0taVmErCs23TLGVCxQrmGhPEQUAAAAAAAAWS7NRfTDhw9Lkho2bKhFixbJz88v05ICcP9L3h2l5KVrpbMXU42heA4AAAAAAIDslu490VevXp0ZeQDIRayRUUqe8WPqAQXyya1lQ4rnAAAAAAAAyHZpKqIPHDhQ77zzjvLmzauBAwfeMnbChAkZkhiA+1PyrgNK/vaXVPvNYY/IrVm9LMwIAAAAAAAASF2aiuh//fWXkpKS7N+nxmQyZUxWAO5L1sgoJc9cnGIfW7cAAAAAAADgXpSmIvrNW7iwnQuAO2EYhpLmLE2xz6VqWbl3bZW1CQEAAAAAAABpkO490QEgvZJ3Ryn5+6VSQqJTHwV0AAAAAAAA3MvSVER/5pln0jzgokWL7jgZAPefWz1ElAI6AADAvefQoUP64IMPFB4erpMnT8rd3V0PPPCA2rZtq969e8vT0zO7U9TkyZOVJ08edevW7Y7HGDlypEaNGpVq/7p16/Too49Kkrp166ZZs2Y5xZQrV0779u1zaDt16pRGjBih8PBwnT59WkFBQXrqqaf01ltvyd/f/47zBQAA2SdNRXRfX9/MzgPAfSq1LVxMIUEU0AEAAO4xS5cu1bPPPiuLxaIuXbqocuXKSkxM1Lp16zR48GD9/fff+uKLL7I7TU2ePFkFChS4qyL6M888o9KlSzu1v/nmm7py5Yoefvhhh3aLxaKvvvrKoe2/n5WvXLmi0NBQxcXF6aWXXlJwcLB27typzz77TKtXr9a2bdvk4uJyxzkDAIDskaYi+owZMzI7DwD3oYQv5rOFCwAAQA5x+PBhtW/fXsWLF9eqVatUuHBhe1/fvn118OBBLV2a8gKJnKhKlSqqUqWKQ9vx48d14sQJ9ezZU+7u7g59rq6u6ty58y3H/Pnnn3X06FEtWbJEzZs3t7fnz59fo0eP1s6dO1WtWrWMuwgAAJAl+CdwAJkicWmEjH1HnNpZgQ4AAHBv+uCDD3TlyhV9/fXXDgX0G0qXLq3+/fvbXycnJ+udd95RqVKlZLFYFBISojfffFMJCQkOx5lMJo0cOdJpvJCQEIeV5DNnzpTJZNL69es1cOBAFSxYUHnz5tXTTz+tc+fOORz3999/a+3atTKZTDKZTGrQoIG9/9ChQzp06NAdzcF3330nwzDUqVOnFPutVqtiYmJSPf5GX6FChRzab8znvbAVDgAASL80rUR/6KGHtHLlSvn5+alatWoymUypxm7fvj3DkgOQ81gjo5S8fL2Mf886d3paZHnl1qt3AAAAkD1++eUXlSxZUrVr105TfM+ePTVr1iy1adNGr732mjZv3qxx48Zp7969+vHHlJ+JkxYvv/yy/Pz8NGLECB05ckSffPKJ+vXrp3nz5kmSPvnkE7388svy8vLSW2+9JcmxaP34449Lko4cOZLuc8+ZM0fBwcGqV6+eU198fLx8fHwUHx8vPz8/dejQQe+//768vLzsMfXq1ZOLi4v69++vjz76SEWLFtWuXbv07rvvqlWrVipfvny6cwIAANkvTUX0p556ShaLRZLUqlWrzMwHQA5mjYxS0vTUPzC5dWiWhdkAAAAgrWJiYvTvv//qqaeeSlP8zp07NWvWLPXs2VNffvmlJOmll15SQECAPvzwQ61evVoNGza8o1z8/f31+++/2xdv2Ww2TZw4UdHR0fL19VWrVq309ttvq0CBArfdXiU9/v77b+3atUtDhgxxWjhWuHBhDRkyRA899JBsNpt+++03TZ48WTt37tSaNWvk6nr9o3XFihX1xRdfaNCgQQoNDbUf37VrV6f91AEAQM6RpiL6iBEjUvweAG6WNO+3VPvMYY/IXLlMFmYDAACAtLqxDYm3t3ea4pctWyZJGjhwoEP7a6+9pg8//FBLly694yJ67969HYrYdevW1ccff6yjR4867WGekjtZgS5dX4UuKcWtXMaNG+fwun379ipbtqzeeustLVy4UO3bt7f3FSlSRDVr1lSzZs1UvHhx/fHHH5o4caIKFCigDz/88I5yAwAA2StNRfSU/Pnnn9q7d6+k6//aXr169QxLCkDOkzDjRynuqlO7qVigXMNCKaADAADcw3x8fCRJsbGxaYo/evSoXFxcVLp0aYf2wMBA5cuXT0ePHr3jXIoVK+bw2s/PT5J06dKlOx7zdgzD0Ny5c1W5cuU0FeolacCAARo2bJhWrFhhL6KvX79eLVq00KZNm1SjRg1J13+b28fHR6NGjVKPHj1UsWLFTLsOAACQOdL9YNETJ06obt26qlmzpvr376/+/fvr4YcfVp06dXTixInMyBHAPc4aGSVjd5RTu0vVsrK82oUCOgAAwD3Ox8dHQUFBioyMTNdxt3pe1u1YrdYU281mc4rthmHc8bluZ/369Tp69GiqDxRNiaenp/z9/XXx4kV727Rp01SoUCF7Af2Gli1byjAMbdiwIcNyBgAAWSfdRfSePXsqKSlJe/fu1cWLF3Xx4kXt3btXNptNPXv2zIwcAdzjkr7/1bnRO6/cu7bK8lwAAABwZ1q0aKFDhw5p48aNt40tXry4bDaboqIcF1KcOXNGly9fVvHixe1tfn5+unz5skNcYmKiTp06dce53k3xPiVz5syRyWRSx44d03xMbGyszp8/r4IFC9rbzpw5k+I/DiQlJUmSkpOT7z5ZAACQ5dJdRF+7dq2mTJmicuXK2dvKlSunSZMmKSIiIkOTA3Dvu/bpt1L8Nad2t2efyIZsAAAAcKeGDBmivHnzqmfPnjpz5oxT/6FDh/Tpp59Kkpo1u/7A+E8++cQhZsKECZKk5s2b29tKlSrl9Fnxiy++SHUlelrkzZvXqTB/c56HDh1K81hJSUlasGCB6tSp47SVjCRdu3YtxW1u3nnnHRmGoSZNmtjbypYtqzNnzmjNmjUOsd99950kqVq1amnOCwAA3DvSvSd6cHCw/V/Rb2a1WhUUFJQhSQG491kjo5T082rp/GXnzgJ+bOECAACQw5QqVUpz585Vu3btVKFCBXXp0kWVK1dWYmKiNmzYoAULFqhbt26SpKpVq6pr16764osvdPnyZdWvX19btmzRrFmz1KpVK4eHivbs2VN9+vRR69at1ahRI+3cuVPLly9XgQIF7jjX6tWra8qUKRozZoxKly6tgIAAPfbYY5Kkxx9/XFLaHzC6fPlyXbhwIdWtXE6fPq1q1aqpQ4cOKl++vP2YZcuWqUmTJnrqqafssf369dOMGTP05JNP6uWXX1bx4sW1du1afffdd2rUqJFq1ap1x9cMAACyT7qL6OPHj9fLL7+szz//3L7P259//qn+/fvzpHEgl7BGRilp+o+p9ru1bJB1yQAAACDDtGzZUrt27dL48eP1008/acqUKbJYLKpSpYo++ugj9erVyx771VdfqWTJkpo5c6Z+/PFHBQYGaujQoRoxYoTDmL169dLhw4f19ddf67ffflPdunUVHh5uL3bfieHDh+vo0aP64IMPFBsbq/r169uL6Ok1Z84cubm56dlnn02xP1++fGrRooXCw8M1a9YsWa1WlS5dWmPHjtWgQYPk4vK/X/AuV66ctm3bprffflvffvutTp8+raCgIA0aNEijRo26o/wAAED2MxlpeDqLn5+fw55zcXFxSk5Olqvr9Rr8je/z5s3r8FCVe1FMTIx8fX0VHR1tfwI9gPS5Nvwz6Uq8c4dPXrm1eYJV6ACAXIn7TAAAAOD+lKaV6P/d5w5A7pU4a3GKBXSXqmV5kCgAAAAAAADuO2kqonft2jWz8wCQAyRMXyQj8qBTOwV0AAAAAAAA3K/SvSf6za5du6bExESHNn51Fbg/Jcz4McUCurzzUkAHAAAAAADAfcvl9iGO4uLi1K9fPwUEBChv3rzy8/Nz+AJw/0mc/5uM3VEp9rk9+0QWZwMAAAAAAABknXQX0YcMGaJVq1bZn9L+1VdfadSoUQoKCtI333yTGTkCyEbWyCjZNu1y7vCwyK3H0zxEFAAAAAAAAPe1dBfRf/nlF02ePFmtW7eWq6ur6tatq7fffltjx47VnDlz0p3A559/rpCQEHl4eKhWrVrasmXLLeMvX76svn37qnDhwrJYLCpbtqyWLVuW7vMCSJukOUtTbHfr2IwCOgAAACRJJpNJI0eOzO40AAAAMkW6i+gXL15UyZIlJV3f//zixYuSpDp16igiIiJdY82bN08DBw7UiBEjtH37dlWtWlWNGzfW2bNnU4xPTExUo0aNdOTIES1cuFD79+/Xl19+qSJFiqT3MgCkwbWPv5ESEp3azWGPUEAHAAC4z8ycOVMmk0kmk0nr1q1z6jcMQ8HBwTKZTGrRokU2ZCglJSVp1KhRKlmypCwWi0qWLKkxY8YoOTnZKXbbtm1q0qSJfHx85O3trSeeeEI7duxwivv999/1/PPPq3LlyjKbzQoJCcn8CwEAADlKuh8sWrJkSR0+fFjFihVT+fLlNX/+fNWsWVO//PKL8uXLl66xJkyYoF69eql79+6SpKlTp2rp0qWaPn263njjDaf46dOn6+LFi9qwYYPc3NwkiRscIJNc+/gb6fhpp3ZTSJDcmtXLhowAAACQFTw8PDR37lzVqVPHoX3t2rU6ceKELBaL0zFXr16Vq2u6P16mW+fOnbVgwQL16NFDNWrU0KZNmzRs2DAdO3ZMX3zxhT1u+/btqlOnjoKDgzVixAjZbDZNnjxZ9evX15YtW1SuXDl77Ny5czVv3jw99NBDCgoKyvRrAAAAOU+6V6J3795dO3fulCS98cYb+vzzz+Xh4aEBAwZo8ODBaR4nMTFR27ZtU1hY2P+ScXFRWFiYNm7cmOIxP//8s0JDQ9W3b18VKlRIlStX1tixY2W1WtN7GQBuIfHn1SkW0BWQX5ZXOmd9QgAAAMgyzZo104IFC5xWd8+dO1fVq1dXYGCg0zEeHh6ZXkTfunWr5s+fr7fffltfffWV+vTpo5kzZ+q1117TV199pV27/vccn2HDhsnT01MbN27Ua6+9psGDB2vDhg2y2Wx68803HcYdO3asYmJitH79elWtWjVTrwEAAORM6S6iDxgwQK+88ookKSwsTHv37tXcuXP1119/qX///mke5/z587JarSpUqJBDe6FChXT6dArFO0n//POPFi5cKKvVqmXLlmnYsGH66KOPNGbMmFTPk5CQoJiYGIcvALdmW/+XU5spJEgeb/TMhmwAAACQlTp06KALFy4oPDzc3paYmKiFCxeqY8eOKR7z3z3RR44cKZPJpIMHD6pbt27Kly+ffH191b17d8XHxzsce/78ee3bt8+p/b/++OMPSVL79u0d2tu3by/DMDRv3jyH2LCwMPn7+9vbChcurPr162vJkiW6cuWKvT0oKMj+m84AAAApSXcR/b9CQkL0zDPPqEqVKhmRzy3ZbDYFBAToiy++UPXq1dWuXTu99dZbmjp1aqrHjBs3Tr6+vvav4ODgTM8TyMmSlkVISf/ZU9LVhRXoAAAAuURISIhCQ0P13Xff2dt+/fVXRUdHOxWwb6dt27aKjY3VuHHj1LZtW82cOVOjRo1yiPnss89UoUIFbdmy5ZZjJSQkSJI8PT0d2vPkySPp+h7oN8f+N+5GbGJioiIjI9N1HQAAIHe7oyL6ypUr1aJFC5UqVUqlSpVSixYttGLFinSNUaBAAZnNZp05c8ah/cyZMyn+eqB0feVA2bJlZTab7W0VKlTQ6dOnlZjo/PBDSRo6dKiio6PtX8ePH09XnkBuYo2MknXFJqd2U+FCKUQDAADgftWxY0ctXrxYV69elSTNmTNH9evXT/ee4dWqVdMPP/ygF198UV9++aWefvppff3113eU0419zNevX+/QfmOF+r///usQu2nTJoetPxMTE7V582anWAAAgNtJdxF98uTJatKkiby9vdW/f3/1799fPj4+atasmT7//PM0j+Pu7q7q1atr5cqV9jabzaaVK1cqNDQ0xWMeffRRHTx4UDabzd524MABFS5cWO7u7ikeY7FY5OPj4/AFIGVJi1L+xzDXRo9kcSYAAADITm3bttXVq1e1ZMkSxcbGasmSJalu5XIrffr0cXhdt25dXbhwwWGbzZEjR8owDDVo0OCWYzVr1kzFixfXoEGDtGjRIh09elTz58/XW2+9JVdXV3vBX5JeeuklHThwQM8//7z27NmjyMhIdenSRadOnZIkh1gAAIDbSXcRfezYsfr444/13Xff6ZVXXtErr7yiuXPn6uOPP9bYsWPTNdbAgQP15ZdfatasWdq7d69efPFFxcXFqXv37pKkLl26aOjQofb4F198URcvXlT//v114MABLV26VGPHjlXfvn3TexkA/iPh60XS5VindnPYIzJXLpMNGQEAACC7FCxYUGFhYZo7d64WLVokq9WqNm3apHucYsWKObz28/OTJF26dCndY3l4eGjp0qXy9/dX69atFRISoi5dumj48OHKnz+/vLy87LF9+vTRm2++qblz56pSpUp64IEHdOjQIQ0ZMkSSHGIBAABuJ91F9MuXL6tJkyZO7U888YSio6PTNVa7du304Ycfavjw4XrwwQe1Y8cO/fbbb/aHjR47dsy+UkCSgoODtXz5cm3dulVVqlTRK6+8ov79++uNN95I72UAuEnirMUy/j7o3FHAT27N6mV9QgAAAMh2HTt21K+//qqpU6eqadOmypcvX7rHuHkrzpsZhnFHOVWqVEmRkZGKjIzUH3/8oZMnT6pXr146f/68ypYt6xD77rvv6syZM/rjjz+0a9cubd261f5bzf+NBQAAuBXX9B7QsmVL/fjjjxo8eLBD+08//aQWLVqkO4F+/fqpX79+KfatWbPGqS00NFSbNjnv2Qzgzlgjo2TbeSDFPreWDbI2GQAAANwznn76ab3wwgvatGmT5s2bl93p2JlMJlWqVMn+etmyZbLZbAoLC3OK9fPzU506deyvV6xYoaJFi6p8+fJZkisAALg/pKmIPnHiRPv3FStW1Lvvvqs1a9bY9y7ftGmT1q9fr9deey1zsgSQaZIWr0qxnW1cAAAAcjcvLy9NmTJFR44c0ZNPPplp5zl//rzOnz+vYsWKKU+ePOk69urVqxo2bJgKFy6sDh063DJ23rx52rp1qz788EO5uKT7l7IBAEAulqYi+scff+zw2s/PT3v27NGePXvsbfny5dP06dP19ttvZ2yGADJNwvRF0kXnbZjMYY+wjQsAAADUtWvXTD/HZ599plGjRmn16tW3fbho27ZtFRQUpIoVKyomJkbTp0/XP//8o6VLl8rb29seFxERodGjR+uJJ56Qv7+/Nm3apBkzZqhJkybq37+/w5i7du3Szz//LEk6ePCgoqOjNWbMGElS1apVM/UfEAAAQM6QpiL64cOHMzsPAFksaVmEjEj2QQcAAEDOUaNGDc2YMUPTpk2Tp6en6tatq7lz5+rBBx90iCtSpIjMZrPGjx+v2NhYlShRQmPGjNHAgQPl6ur4MXj79u0aNmyYQ9uN1127dqWIDgAAZDLu9Iku+t/DYEwmU4YllNliYmLk6+ur6Oho+fj4ZHc6QLa5NuQjKdnq1O7W42m2cQEA4A5wnwkAAADcn+5oI7hvvvlGDzzwgDw9PeXp6akqVapo9uzZGZ0bgEyStCzCuYBudqGADgAAAAAAAPxHmrZzudmECRM0bNgw9evXT48++qgkad26derTp4/Onz+vAQMGZHiSADKWdcMOpzZzw5oU0AEAAAAAAID/SHcRfdKkSZoyZYq6dOlib2vZsqUqVaqkkSNHUkQHcoL4a46vXUzsgw4AAAAAAACkIN3buZw6dUq1a9d2aq9du7ZOnTqVIUkByDzX3pni3JjHM+sTAQAAAAAAAHKAdBfRS5curfnz5zu1z5s3T2XKsBUEcK+yRkYp4aNZ0qVYpz7zI1WyISMAAAAAAADg3pfu7VxGjRqldu3aKSIiwr4n+vr167Vy5coUi+sAsp81MkpJ039MuTMgP1u5AAAAAAAAAKlI90r01q1ba8uWLSpQoIAWL16sxYsXq0CBAtqyZYuefvrpzMgRwF2wRkYpac6SlDs9LfJ4o2fWJgQAAAAAAADkIOkqoiclJalHjx7y8/PTt99+q23btmnbtm369ttvVa1atczKEcAdsq9AT0hKsd+tQ7MszggAAAD3qpkzZ8pkMslkMmndunVO/YZhKDg4WCaTSS1atMiGDFM2b948de7cWWXKlJHJZFKDBg1SjOvWrZv9+lL6+vfff+2xNptNU6dO1YMPPigvLy8VKlRITZs21YYNGxzG/Pvvv/Xss8+qZMmSypMnjwoUKKB69erpl19+uWXOSUlJqlixokwmkz788MO7ngMAAJC50rWdi5ubm3744QcNGzYss/IBkIGS5v2WYrupWKBcw0JlrsxzDAAAAODIw8NDc+fOVZ06dRza165dqxMnTshisWRTZimbMmWKtm3bpocfflgXLlxINe6FF15QWFiYQ5thGOrTp49CQkJUpEgRe/vgwYM1YcIEde7cWS+99JIuX76sadOmqX79+lq/fr1q1qwpSTp69KhiY2PVtWtXBQUFKT4+Xj/88INatmypadOmqXfv3inmMmnSJB07diwDrh4AAGSFdO+J3qpVKy1evFgDBgzIjHwAZJCkZRFS3FWndpeqZeXetVXWJwQAAIAcoVmzZlqwYIEmTpwoV9f/fWScO3euqlevrvPnz2djds5mz56tIkWKyMXFRZUrV041LjQ0VKGhoQ5t69atU3x8vDp16mRvS05O1pQpU9SmTRvNnj3b3n5jxfmcOXPsRfRmzZqpWTPH3+7s16+fqlevrgkTJqRYRD979qxGjx6t119/XcOHD7+jawYAAFkr3XuilylTRqNHj1abNm00btw4TZw40eELQPYzkpNlXbnZqZ0COgAAAG6nQ4cOunDhgsLDw+1tiYmJWrhwoTp27JjiMR9++KFq164tf39/eXp6qnr16lq4cKFDzIwZM2QymTR9+nSH9rFjx8pkMmnZsmX2tlOnTmnfvn1KSkp5W8KbBQcHy8Ul3R9tJV3/hwGTyeRwXUlJSbp69aoKFSrkEBsQECAXFxd5enreckyz2azg4GBdvnw5xf433nhD5cqVU+fOne8oZwAAkPXSvRL966+/Vr58+ez7od/MZDLplVdeybDkAKRf4pI1sq3eKhmGY4ebKwV0AAAA3FZISIhCQ0P13XffqWnTppKkX3/9VdHR0Wrfvn2Ki6c+/fRTtWzZUp06dVJiYqK+//57Pfvss1qyZImaN28uSerevbsWLVqkgQMHqlGjRgoODtbu3bs1atQoPf/88w4ruocOHapZs2bp8OHDCgkJyZTrTEpK0vz581W7dm2Hc3h6eqpWrVqaOXOmQkNDVbduXV2+fFnvvPOO/Pz8UlxdHhcXp6tXryo6Olo///yzfv31V7Vr184pbsuWLZo1a5bWrVsnk8mUKdcFAAAyXrqL6IcPH86MPABkgGufzpaOnkqxz1y/RhZnAwAAgJyqY8eOGjp0qK5evSpPT0/NmTNH9evXV1BQUIrxBw4ccFih3a9fPz300EOaMGGCvYguSV9++aUqVaqk559/XkuWLFHXrl0VGBioCRMmZPo1/dfy5ct14cIFh61cbvj222/Vrl07h9XiJUuW1Pr161WyZEmn+Ndee03Tpk2TJLm4uOiZZ57RZ5995hBjGIZefvlltWvXTqGhoTpy5EjGXhAAAMg06fqdt02bNumtt97S4MGD9dtvKT+wEED2SFoWkWoB3RQSJLdm9bI4IwAAAORUbdu21dWrV7VkyRLFxsZqyZIlqW7lIsmhgH7p0iVFR0erbt262r59u0NcYGCgPv/8c4WHh6tu3brasWOHpk+fLh8fH4e4mTNnyjCMTFuFLl3fysXNzU1t27Z16vP29lalSpXUt29fLVq0SJMnT1ZycrJatWqV4p7wr776qsLDwzVr1iw1bdpUVqtViYmJDjEzZ87U7t279f7772faNQEAgMyR5pXoCxcuVLt27eTp6Sk3NzdNmDBB77//vgYNGpSZ+QFII+uarSm2sw86AAAA0qtgwYIKCwvT3LlzFR8fL6vVqjZt2qQav2TJEo0ZM0Y7duxQQkKCvT2lLUvat2+vb7/9VkuXLlXv3r31+OOPZ8o13MqVK1f0008/qXHjxvL393foS05OVlhYmBo0aKBJkybZ28PCwlSpUiWNHz/eqRBevnx5lS9fXpLUpUsXPfHEE3ryySe1efNmmUwmxcTEaOjQoRo8eLCCg4Mz/wIBAECGSvNK9HHjxqlXr16Kjo7WpUuXNGbMGI0dOzYzcwOQRknLIqRkq2Oji0luPZ6mgA4AAIA70rFjR/3666+aOnWqmjZtqnz58qUY98cff6hly5by8PDQ5MmTtWzZMoWHh6tjx44y/vucHkkXLlzQn3/+KUnas2ePbDZbZl5GihYvXqz4+PgUt3KJiIhQZGSkWrZs6dBepkwZVahQQevXr7/t+G3atNHWrVt14MABSdcfvJqYmKh27drpyJEjOnLkiE6cOCHp+sr9I0eOOK1cBwAA9440F9H379+vQYMGyWw2S7q+51tsbKzOnj2backBSBvr2j+d2syP1ZK5cplsyAYAAAD3g6efflouLi7atGnTLbdy+eGHH+Th4aHly5erR48eatq0qcLCwlKN79u3r2JjYzVu3DitW7dOn3zySSZkf2tz5syRl5eXU6Fcks6cOSNJslqtTn1JSUlKTk6+7fhXr16VJEVHR0uSjh07pkuXLqlSpUoqUaKESpQoobp160qSxo4dqxIlSmjPnj13fD0AACBzpXk7l/j4eId96tzd3eXh4aErV64oICAgU5IDcHtJyyKkpP/cyLu5sgc6AAAA7oqXl5emTJmiI0eO6Mknn0w1zmw2y2QyORSdjxw5osWLFzvFLly4UPPmzdPEiRP18ssva+fOnXr77bfVokULlS1b1h536tQpRUdHq1SpUnJzc8vQ6zp37pxWrFihDh06KE+ePE79N/L4/vvv1aRJE3v79u3btX//fvXu3dvedvbsWafPw0lJSfrmm2/k6empihUrSpJeeeUVtWrVyiHu7NmzeuGFF9StWzc99dRTKlGiREZdIgAAyGBpLqJL0ldffSUvLy/76+TkZM2cOVMFChSwt73yyisZlx2AW7JGRqW4F7q5fo1syAYAAAD3m65du942pnnz5powYYKaNGmijh076uzZs/r8889VunRp7dq1yx539uxZvfjii2rYsKH69esnSfrss8+0evVqdevWTevWrZOLy/Vflh46dKhmzZqlw4cP3/bhohEREYqIiJB0vUAeFxenMWPGSJLq1aunevUcF5fMmzdPycnJKW7lIknVq1dXo0aNNGvWLMXExOiJJ57QqVOnNGnSJHl6eurVV1+1x77wwguKiYlRvXr1VKRIEZ0+fVpz5szRvn379NFHH9k/Pz/00EN66KGHHM5z5MgRSVKlSpWcCuwAAODekuYierFixfTll186tAUGBmr27Nn21yaTiSI6kEWSd0cpecaPzh2uLqxCBwAAQJZ57LHH9PXXX+u9997Tq6++qhIlSuj999/XkSNHHIroL774ohISEjRjxgz7A0f9/f31xRdf6KmnntKHH36oIUOGpPv8q1at0qhRoxzahg0bJkkaMWKEUxF9zpw5CggIuOWWMz/99JM+/PBDff/99/rtt9/k7u6uunXr6p133lG5cuXsce3atdPXX3+tKVOm6MKFC/L29lb16tX1/vvvp7hVDAAAyJlMRkpPermPxcTEyNfXV9HR0Q7b0wA5hTUyStZNu2Tbd1hK4SFMpuDCsgx4LhsyAwAgd+M+EwAAALg/pWs7FwDZyxoZpaTpKaw+v4lro0eyKBsAAAAAAADg/kcRHchBkn5enWqfqVigXMNCZa5cJgszAgAAAAAAAO5vFNGBHMIaGSWdv5xin0vVsnLv2ipL8wEAAAAAAAByA4roQA6R9FMKq9A9LHLr2IzV5wAAAAAAAEAmccnuBADcXsJXC6ULl53aKaADAAAAAAAAmeuOiuiHDh3S22+/rQ4dOujs2bOSpF9//VV///13hiYHQEpaFiFjzz/OHQX8KKADAAAAAAAAmSzdRfS1a9fqgQce0ObNm7Vo0SJduXJFkrRz506NGDEiwxMEcjvrmq0ptru1bJC1iQAAAAAAAAC5ULqL6G+88YbGjBmj8PBwubu729sfe+wxbdq0KUOTA3K7pGURUrLVsdHsIrceT7MKHQAAABlq5syZMplMMplMWrdunVO/YRgKDg6WyWRSixYtsiHDu3fj+v779d577znEjRw5MsU4Dw8PpzGjo6M1ZMgQlSlTRp6enipevLief/55HTt2LMUc5s2bp9DQUOXNm1f58uVT7dq1tWrVqtvmbrPZNHXqVD344IPy8vJSoUKF1LRpU23YsMEpNioqSu3bt1fRokWVJ08elS9fXqNHj1Z8fHwaZwoAANws3Q8W3b17t+bOnevUHhAQoPPnz2dIUgCus67a4tRmbliTAjoAAAAyjYeHh+bOnas6deo4tK9du1YnTpyQxWLJpswyRqNGjdSlSxeHtmrVqqUYO2XKFHl5edlfm81mh36bzaZGjRppz549eumll1S2bFkdPHhQkydP1vLly7V37155e3vb40eOHKnRo0erTZs26tatm5KSkhQZGal///33tnkPHjxYEyZMUOfOnfXSSy/p8uXLmjZtmurXr6/169erZs2akqTjx4+rZs2a8vX1Vb9+/ZQ/f35t3LhRI0aM0LZt2/TTTz+lea4AAMB16S6i58uXT6dOnVKJEiUc2v/66y8VKVIkwxIDcrtrY7+UbDbHRjdXuTWrlz0JAQAAIFdo1qyZFixYoIkTJ8rV9X8fGefOnavq1avn+MVTZcuWVefOndMU26ZNGxUoUCDV/k2bNmnr1q367LPP1LdvX3t7uXLl1KNHD61YsUJPP/20PXb06NH66KOPNGDAgHTlnJycrClTpqhNmzaaPXu2vf3ZZ59VyZIlNWfOHHsRffbs2bp8+bLWrVunSpUqSZJ69+4tm82mb775RpcuXZKfn1+6zg8AQG6X7u1c2rdvr9dff12nT5+WyWSSzWbT+vXrNWjQIKd/zQdwZxJmLpbOX3JqN9evkfXJAAAAIFfp0KGDLly4oPDwcHtbYmKiFi5cqI4dO6Z4jM1m0yeffKJKlSrJw8NDhQoV0gsvvKBLlxzvaX/66Sc1b95cQUFBslgsKlWqlN555x1ZrY5bGDZo0ECVK1fWnj171LBhQ+XJk0dFihTRBx984HTuY8eOad++fem6xqtXr+ratWu3jTMMQzExMTIMI8X+mJgYSVKhQoUc2gsXLixJ8vT0tLd98sknCgwMVP/+/WUYhv35YmmRlJSkq1evOp0nICBALi4uDue5VU4uLi4O27ICAIC0SXcRfezYsSpfvryCg4N15coVVaxYUfXq1VPt2rX19ttvZ0aOQK5ijYySseuAc0dAflahAwAAINOFhIQoNDRU3333nb3t119/VXR0tNq3b5/iMS+88IIGDx6sRx99VJ9++qm6d++uOXPmqHHjxkpKSrLHzZw5U15eXho4cKA+/fRTVa9eXcOHD9cbb7zhNOalS5fUpEkTVa1aVR999JHKly+v119/Xb/++qtDXJcuXVShQoU0X9/MmTOVN29eeXp6qmLFiiluV3pDyZIl5evrK29vb3Xu3Flnzpxx6K9Ro4by5s2rYcOGadWqVfr333+1du1aDRkyRA8//LDCwsLssStXrtTDDz+siRMnqmDBgvL29lbhwoX12Wef3TZnT09P1apVSzNnztScOXN07Ngx7dq1S926dZOfn5969+5tj23QoIEk6fnnn9eOHTt0/PhxzZs3T1OmTNErr7yivHnzpnmuAADAdSYjtX9Sv41jx44pMjJSV65cUbVq1VSmTM7YozkmJka+vr6Kjo6Wj49PdqcDOLk29gvp/GXHRk+LPN7tny35AACAtOE+EzndzJkz1b17d23dulWbN2/W0KFDdebMGXl6eqpt27Y6f/68Vq1apZCQEFWuXFlLliyRJK1bt05169bVnDlzHFaqL1++XE2aNHFov3r1qsOqaUnq06ePZs+erYsXL9r3W2/QoIHWrl2rb775Rs8995yk66vhixcvrkcffVQLFy60H38jNi0fbR999FG1bdtWJUqU0MmTJ/X5558rMjJSkydP1osvvmiP+/TTT3Xw4EGFhobKYrHojz/+0Oeff64SJUrozz//dPh/fOnSperVq5dOnTplb2vcuLEWLlxo30/90qVLyp8/v/z9/ZWQkKARI0aoWLFimjFjhn777TdNnTpVL7zwwi1zP3jwoNq1a6ft27fb20qWLKlly5apXLlyDrFjxozR2LFjdfXqVXvbW2+9pTFjxtx2jgAAgLN074m+bt061alTR8WKFVOxYsUyIycg17JGRjkX0CW5dWiW9ckAAAAg12rbtq1effVVLVmyRE2aNNGSJUs0ceLEFGMXLFggX19fNWrUyGG/9OrVq8vLy0urV6+2F9FvLqDHxsYqISFBdevW1bRp07Rv3z5VrVrV3u/l5eWwd7m7u7tq1qypf/75x+H8a9asSfN1rV+/3uF1jx49VL16db355pvq1q2bPb/+/R0XsLRu3Vo1a9ZUp06dNHnyZIeV8wULFlS1atXUr18/VapUSTt27NAHH3yg7t27a8GCBZJk37rlwoUL+v7779WuXTtJ1/dcf+CBBzRmzJjbFtG9vb1VqVIlhYaG6vHHH9fp06f13nvvqVWrVvrjjz8c9m4PCQlRvXr11Lp1a/n7+2vp0qUaO3asAgMD1a9fvzTPFwAAuC7dRfTHHntMRYoUUYcOHdS5c2dVrFgxM/ICcqWkxaucGwv4yVw5Z/ymBwAAAO4PBQsWVFhYmObOnav4+HhZrVa1adMmxdioqChFR0crICAgxf6zZ8/av//777/19ttva9WqVfa9u2+Ijo52eF20aFGZTCaHNj8/P+3atetOLilF7u7u6tevn/r06aNt27apTp06qcZ27NhRr732mlasWGEvov/zzz9q2LChvvnmG7Vu3VqS9NRTTykkJETdunXTr7/+qqZNm9qL825ubg7z6OLionbt2mnEiBE6duxYqgvVkpOTFRYWpgYNGmjSpEn29rCwMFWqVEnjx4/X+++/L0n6/vvv1bt3bx04cEBFixaVJD3zzDOy2Wx6/fXX1aFDB/n7+9/FrAEAkPuku4h+8uRJff/99/ruu+/03nvvqUqVKurUqZM6dOhg/wsaQPol746SLkY7tbu1bJD1yQAAACDX69ixo3r16qXTp0+radOmypcvX4pxNptNAQEBmjNnTor9BQsWlCRdvnxZ9evXl4+Pj0aPHq1SpUrJw8ND27dv1+uvvy6bzeZwnNlsTnG8O9yRNFXB/9fencfZWP//H3+eM6ttxjBj7AYZZBvrkGw1jCVLZUl8LElapMiS1CBlfBJRtkrFR0QkhMg2lsguTYUhURlrzGD2c67fH/3mfJ3OmTHD7B732+3cbnNe79d1Xa/z7spt5jXveV8VKkiS/v777wzl3pq3YMECJSQk6JFHHrHL69Kli6R/Vr536NBBJUqUkKenp4oXL+7wuVJ/+XD16tU0m+g7duxQZGSkpk+fbhevVq2aatasabfCfs6cOapfv77Dz+ddunTRggULdPjwYbu92gEAwO1luonu6+uroUOHaujQoTp9+rSWLFmihQsXauzYsWrZsqW2bnWykhZAulJ+ilLKglWOA6xCBwAAQC559NFHNWTIEP3www9atmxZmnlVq1bV5s2b1bx5c4f9zm8VERGhK1euaOXKlWrZsqUtfvr06SytO7NSt4dJbfanxTAM/f7776pfv74tduHCBRmGIYvFYpeb+jDVlJQUSf+sOA8KCtL+/fuVlJQkd3d3W+65c+due/3UB5r++zqp10q9Tmquj4+P07xbawIAABlnvpuDK1eurFdffVVTpkxRnTp1tH379qyqC7hnWCKjlPLZ15KTFTWsQgcAAEBuKVq0qObOnasJEyaoc+fOaeb17NlTFotFkyZNchhLSUnRtWvXJP3fyvJbV5InJSVpzpw5d1Xn2bNndezYsdvmXbp0ySF2/fp1zZgxQ76+vmrYsGG6uXPnztWlS5fUvn17WywwMFCGYejLL7+0y/3iiy8kya7h3qtXL1ksFi1cuNAWS0hI0OLFi3X//ferbNmytvixY8d09uxZu+tI/2zVcqtDhw7p+PHjdtcJDAzU4cOHdeLECYeazGaz6tat6/DZAABA+jK9Ej3V999/r8WLF2vFihVKSEhQ165dFR4enpW1AQWeJTJKyYvXOR0z1wtkFToAAAByVf/+/W+b06pVKw0ZMkTh4eE6cuSI2rVrJzc3N0VFRWn58uWaOXOmunfvrgceeEA+Pj7q37+/hg0bJpPJpEWLFt319iz9+vXT9u3bb3ue2bNna9WqVercubMqVqyo6Ohoffrppzp79qwWLVpktzq8UqVK6tWrl+rUqSNPT0/t2rVLS5cuVVBQkN0DQAcMGKB3331XQ4YM0eHDh1WrVi0dOnRI8+fPV61atfToo4/acocMGaL58+frhRde0IkTJ1SxYkUtWrRIZ86c0TfffGNXa82aNdWqVSvbQ1MbNmyotm3bauHChYqNjVW7du0UHR2tDz74QIUKFdLLL79sO3bUqFH69ttv1aJFCw0dOlQlS5bU2rVr9e233+rpp5+2a9YDAICMyXQTfezYsVq6dKnOnTuntm3baubMmeratasKFy6cHfUBBVby+h2ybP7B6ZgpoKzc+3fL2YIAAACAOzRv3jw1bNhQH374oV577TW5uroqICBAffv2VfPmzSXJ1sx95ZVX9Prrr8vHx0d9+/bVww8/rNDQ0GyvsXnz5tq9e7fmz5+vK1euqEiRImrSpIk+/fRTPfTQQ3a5ffr00e7du/XVV18pISFBlSpV0ujRozVu3Di7n31LliypAwcOKCwsTN98843mzZunkiVL6qmnntLkyZPtGvOFChXS1q1bNXr0aH366ae6efOmgoKCtG7dugx9/tWrV+vdd9/V0qVLtWHDBrm7u6tFixaaNGmSqlevbstr2bKldu/erQkTJmjOnDm6cuWKKleurLffflujR4/OgpkEAODeYzIy+Wv/5s2bq0+fPurZs6d8fX2zq65sExsbK29vb8XExMjLyyu3y8E9KnHBKhlHTzgdMwWUlcewvjlcEQAAuFt8nwkAAAAUTJleiX7rU78BZF7SwrQb6OZ6gaxABwAAAAAAAPKQDDXR16xZow4dOsjNzU1r1qxJN7dLly5ZUhhQkCR9/o2sJ89KLmbp6nWnOS4hTeXWsWUOVwYAAAAAAAAgPRnazsVsNuv8+fMqVaqUzGZz2iczmWSxWLK0wKzGn9kipyXOWiLjtz/TTvD0kNuTHXmIKAAA+RzfZwIAAAAFU4ZWolutVqdfA0ifJTIq/Qa6RAMdAAAAAAAAyMPSXlaehv/9739KTEx0iCclJel///tflhQFFBTJ3+12DPr6yFzrPplrVZXbU4/SQAcAAAAAAADysAxt53IrFxcXRUdHq1SpUnbxK1euqFSpUmznAtwi4ZV3pH/9H0bjHACAgonvMwEAAICCKdMr0Q3DkMlkcoj/+eef8vb2zpKigIIg4b1FDg10mc000AEAAIB/CQgI0IABA2zvIyIiZDKZFBERkWs1AQAApMpwE71+/fpq0KCBTCaTHn74YTVo0MD2qlevnlq0aKGQkJDsrBXIN5IWrpL+iHaIm+vcl/PFAAAAABmwYMECmUwmmUwm7dq1y2HcMAxVqFBBJpNJjzzySC5UmLsCAgJs83Pr69lnn3XIvXbtmp555hn5+fmpSJEiatOmjQ4dOuSQN3z4cDVo0EAlSpRQ4cKFVbNmTU2YMEE3btxwyI2KitITTzyh8uXLq3DhwqpRo4befPNNxcXFZaj+v/76Sz179lTx4sXl5eWlrl276rfffnOa+8knn6hmzZry9PRUtWrV9MEHH2ToGgAAFFQZerCoJHXr1k2SdOTIEYWGhqpo0aK2MXd3dwUEBOjxxx/P8gKB/Cbxs69l/BTlOFCqhNz7d8vxegAAAIDM8PT01JIlS/Tggw/axbdv364///xTHh4e2V5Dy5YtFR8fL3d392y/VmYEBQXplVdesYsFBgbavbdarerUqZN+/PFHjRo1Sr6+vpozZ45at26tgwcPqlq1//vL1P3796tFixYaOHCgPD09dfjwYU2ZMkWbN2/Wjh07ZDb/s+7tjz/+UJMmTeTt7a2hQ4eqRIkS2rNnj8aPH6+DBw9q9erV6dZ948YNtWnTRjExMXrttdfk5uam9957T61atdKRI0dUsmRJW+6HH36oZ599Vo8//rhGjBihnTt3atiwYYqLi9OYMWPudgoBAMiXMtxEHz9+vKR/fvveq1cveXp6ZltRQH6VvH6H8wZ6sSLyfPXpnC8IAAAAyKSOHTtq+fLlev/99+Xq+n8/Mi5ZskQNGzbU5cuXs70Gs9mcJ3/mLFeunPr27ZtuzooVK7R7924tX75c3bt3lyT17NlTgYGBGj9+vJYsWWLLdbbiv2rVqho5cqT27dunpk2bSpIWLVqka9euadeuXapVq5Yk6ZlnnpHVatX//vc/Xb16VT4+PmnWNGfOHEVFRWnfvn1q3LixJKlDhw6qXbu2pk2bpsmTJ0uS4uPjNW7cOHXq1EkrVqyQJA0ePFhWq1WTJk3SM888k+51AAAoqDK9J3r//v3z5DczQF5g2XXYadytR7scrgQAAAC4M71799aVK1e0adMmWywpKUkrVqzQk08+6fQYq9WqGTNmqFatWvL09JS/v7+GDBmiq1ev2uUZhqG33nrLtiVJmzZt9PPPPzucz9me6Dt37lSPHj1UsWJFeXh4qEKFCho+fLji4+Ptjh0wYICKFi2qv/76S926dVPRokXl5+enkSNHymKx2OVGR0fr2LFjSk5OzvD8JCUl6ebNm2mOr1ixQv7+/nrsscdsMT8/P/Xs2VOrV69WYmJiuucPCAiQ9M+WMKliY2MlSf7+/na5ZcqUkdlsvu2K/RUrVqhx48a2Brok1ahRQw8//LC+/PJLW2zbtm26cuWKnn/+ebvjX3jhBd28eVPr1q1L9zoAABRUmW6iWywWvfvuu2rSpIlKly6tEiVK2L2Ae1HKT1FKmPyxlPCvb4jNJrk99SgPEwUAAEC+ERAQoGbNmumLL76wxb799lvFxMToiSeecHrMkCFDNGrUKDVv3lwzZ87UwIEDtXjxYoWGhto1qMPCwvTGG2+oXr16mjp1qqpUqaJ27dql25ROtXz5csXFxem5557TBx98oNDQUH3wwQfq16+fQ67FYlFoaKhKliypd999V61atdK0adP00Ucf2eWNHTtWNWvW1F9//ZWhudm6dasKFy6sokWLKiAgQDNnznTIOXz4sBo0aGDbiiVVkyZNFBcXpxMnTtjFU1JSdPnyZZ07d07fffedXn/9dRUrVkxNmjSx5bRu3VqSNGjQIB05ckR//PGHli1bprlz52rYsGEqUqRImjVbrVYdPXpUjRo1chhr0qSJTp06pevXr9tql+SQ27BhQ5nNZts4AAD3mgxv55Jq4sSJmj9/vl555RW9/vrrGjdunH7//XetWrVKYWFh2VEjkKclfRMh67Z9TsdM5UrTQAcAAEC+8+STT2rs2LGKj49XoUKFtHjxYrVq1Uply5Z1yN21a5fmz5+vxYsX261Ub9Omjdq3b6/ly5frySef1KVLl/TOO++oU6dO+uabb2QymSRJ48aNs20nkp7//ve/KlSokO39M888o/vuu0+vvfaazp49q4oVK9rGEhIS1KtXL73xxhuSpGeffVYNGjTQJ598oueee+6O5qRu3bp68MEHVb16dV25ckULFizQyy+/rHPnzum///2vLS86OlotW7Z0OL5MmTKSpHPnzqlOnTq2+IEDB9SsWTPb++rVq2vNmjV2i9Tat2+vSZMmafLkyVqzZo0tPm7cOL311lvp1v33338rMTHRdv20aqpevbqio6Pl4uKiUqVK2eW5u7urZMmSOnfuXLrXAgCgoMr0SvTFixfr448/1iuvvCJXV1f17t1b8+fPV1hYmH744YfsqBHIsyyRUWk20CXJtW3THKwGAAAAyBo9e/ZUfGsAkZQAAD7lSURBVHy81q5dq+vXr2vt2rVpbuWyfPlyeXt7q23btrp8+bLt1bBhQxUtWlTbtm2TJG3evFlJSUl68cUXbQ10SXr55ZczVNOtDfSbN2/q8uXLeuCBB2QYhtMV0s8++6zd+xYtWui3336ziy1YsECGYdi2UEnPmjVrNHr0aHXt2lVPPfWUtm/frtDQUE2fPl1//vmnLS8+Pt7pw1dTt0X99/Yz999/vzZt2qRVq1Zp9OjRKlKkiG7cuOFwfEBAgFq2bKmPPvpIX331lZ566ilNnjxZs2bNSrfu1OtlpKb0Hubq6enpUDsAAPeKTK9EP3/+vO235kWLFlVMTIwk6ZFHHrH9lh+4VyQv/85p3FSxtFxDmrEKHQAAAPmSn5+fQkJCtGTJEsXFxclisdgekvlvUVFRiomJcVi9nOrixYuSpDNnzkiSqlWz/x7Zz88vQw+rPHv2rMLCwrRmzRqHvdZTfy5N5enpKT8/P7uYj4+Pw3F3w2Qyafjw4dq4caMiIiJsDxwtVKiQ033PExISbOO38vLyUkhIiCSpa9euWrJkibp27apDhw6pXr16kqSlS5fqmWee0YkTJ1S+fHlJ0mOPPSar1aoxY8aod+/eKlmypNM6U6+XkZoKFSqkpKQkp+dJSEhwqB0AgHtFpleily9fXtHR0ZL+eWr4d9/900Tcv3+/099sZ8Ts2bMVEBAgT09PBQcHa9++tFf23mrp0qUymUzq1q3bHV0XuBuWyCjpuuPejS4hTeXxcj8a6AAAAMjXnnzySX377beaN2+eOnTooOLFizvNs1qtKlWqlDZt2uT09eabb951LRaLRW3bttW6des0ZswYrVq1Sps2bdKCBQtsNdzKxcXlrq+ZERUqVJD0z5YpqcqUKWP7mflWqTFnW+LcKvWBpEuXLrXF5syZo/r169sa6Km6dOmiuLi4dPcqL1GihDw8PDJUU5kyZWSxWGy/+EiVlJSkK1eu3LZ2AAAKqkyvRH/00Ue1ZcsWBQcH68UXX1Tfvn31ySef6OzZsxo+fHimC1i2bJlGjBihefPmKTg4WDNmzFBoaKiOHz+e5koGSfr99981cuRItWjRItPXBLJC8pcbHYPFisito+P+hwAAAEB+8+ijj2rIkCH64YcftGzZsjTzqlatqs2bN6t58+bprlSuVKmSpH9WrlepUsUWv3Tp0m1XiP/00086ceKEFi5caPcg0U2bNmX042SL1O1hbl31HhQUpJ07d8pqtdo9XHTv3r0qXLiwAgMD0z1nYmKirFar3er6CxcuOF2tn/rQ1pSUlDTPZzabVadOHR04cMBhbO/evapSpYqKFStmq136Z5/2jh072vIOHDggq9VqGwcA4F6T6ZXoU6ZM0WuvvSZJ6tWrl3bs2KHnnntOK1as0JQpUzJdwPTp0zV48GANHDhQ999/v+bNm6fChQvr008/TfMYi8WiPn36aOLEiXbffAE5JXn9DulGnEPcrUe7XKgGAAAAyHpFixbV3LlzNWHCBHXu3DnNvJ49e8pisWjSpEkOYykpKbp27ZokKSQkRG5ubvrggw9kGIYtZ8aMGbetJXVl+a3HGYahmTNnZvDTOBcdHa1jx47ZmtFp+fvvv2WxWOxiycnJmjJlitzd3dWmTRtbvHv37rpw4YJWrlxpi12+fFnLly9X586dbX/Bfe3aNafXnT9/viSpUaNGtlhgYKAOHz6sEydO2OV+8cUXMpvNqlu3ri129uxZHTt2zC6ve/fu2r9/v10j/fjx49q6dat69Ohhiz300EMqUaKE5s6da3f83LlzVbhwYXXq1CmNGQIAoGDL9Er0f2vWrJndk8QzIykpSQcPHtTYsWNtMbPZrJCQEO3ZsyfN4958802VKlVKgwYN0s6dO+/o2sDdsETsd4iZ6wWyhQsAAAAKlP79+982p1WrVhoyZIjCw8N15MgRtWvXTm5uboqKitLy5cs1c+ZMde/eXX5+fho5cqTCw8P1yCOPqGPHjjp8+LC+/fZb+fr6pnuNGjVqqGrVqho5cqT++usveXl56auvvrrrPc7Hjh2rhQsX6vTp0+k+XHTNmjV666231L17d1WuXFl///23lixZosjISE2ePFmlS5e25Xbv3l1NmzbVwIED9csvv8jX11dz5syRxWLRxIkTbXkREREaNmyYunfvrmrVqikpKUk7d+7UypUr1ahRI9se65I0atQoffvtt2rRooWGDh2qkiVLau3atfr222/19NNP222z0q9fP23fvt3uFw7PP/+8Pv74Y3Xq1EkjR46Um5ubpk+fLn9/f73yyiu2vEKFCmnSpEl64YUX1KNHD4WGhmrnzp36/PPP9fbbb6tEiRJ3Nd8AAORXGWqir1mzJsMn7NKlS4ZzL1++LIvFIn9/f7u4v7+/w2/OU+3atUuffPKJjhw5kqFrJCYm2j1AJTY2NsP1Ac4kr98hpdivQpGbq9z7d8uVegAAAIDcNm/ePDVs2FAffvihXnvtNbm6uiogIEB9+/ZV8+bNbXlvvfWWPD09NW/ePG3btk3BwcH67rvvbrvC2c3NTd98842GDRum8PBweXp66tFHH9XQoUNtD9/MTnXq1NH999+vzz//XJcuXZK7u7uCgoL05Zdf2q3klv5ZNb9+/XqNGjVK77//vuLj49W4cWMtWLBA1atXtztnmzZttHr1akVHR8swDFWtWlVhYWEaNWqU3N3dbbktW7bU7t27NWHCBM2ZM0dXrlxR5cqV9fbbb2v06NG3rb9YsWKKiIjQ8OHD9dZbb8lqtap169Z67733HB7A+vzzz8vNzU3Tpk3TmjVrVKFCBb333nt66aWX7nIWAQDIv0zGrb+eTsOt+7ilezKTyeFP3NJz7tw5lStXTrt377ZbzT569Ght375de/futcu/fv266tatqzlz5qhDhw6SpAEDBujatWtatWqV02tMmDDB7rf9qWJiYuTl5ZXhWoFUCa/NlBLsn2zvEtKUvdABALjHxcbGytvbm+8zAQAAgAImQyvR//2k86zi6+srFxcXXbhwwS5+4cIFuz+HS3Xq1Cn9/vvvdvvxpdbm6uqq48ePq2rVqnbHjB07ViNGjLC9j42NtT1BHcis5PU7HBrocjXTQAcAAAAAAAAKqLveE/1uuLu7q2HDhtqyZYu6desm6Z+m+JYtWzR06FCH/Bo1auinn36yi73++uu6fv26Zs6c6bQ57uHhYXtwC3A3LJFRsmz+wSFuKuPvJBsAAAAAAABAQZDpJvqbb76Z7nhYWFimzjdixAj1799fjRo1UpMmTTRjxgzdvHlTAwcOlPTPQ1HKlStn2/eudu3adscXL15ckhziQFZLXrLeady1bdMcrgQAAAAAAABATsl0E/3rr7+2e5+cnKzTp0/L1dXV9hCUzOjVq5cuXbqksLAwnT9/XkFBQdqwYYPtYaNnz57N8J7sQHZxuo2L/tkL3aV2tVyoCAAAAAAAAEBOyNCDRW8nNjZWAwYM0KOPPqr//Oc/WVFXtuGBT8ispHU7ZN36g/Sv/1NMAWXlMaxv7hQFAADyHL7PBAAAAAqmLGmiS9JPP/2kzp076/fff8+K02UbfrhBRlkio5SyaY+MP847DrqY5Tl1ZM4XBQAA8iy+zwQAAAAKpizbJyUmJkYxMTFZdTogV1kio5T86dfOG+iSXNo0yeGKAAAAgPxrwoQJMplMuV0GAADAHcl0E/3999+3e82cOVOvvvqqevXqpQ4dOmRHjUCOS162wfmAm6tcQprKrWPLnC0IAAAAyAE//fSTunfvrkqVKsnT01PlypVT27Zt9cEHH9jlTZ48WatWrcqxuiZPnqymTZvKz89Pnp6eqlatml5++WVdunTJIddqteqdd95R5cqV5enpqbp16+qLL75wyBswYIBMJpPDq0aNGnZ5qb8ASOv1/fff37b+a9eu6ZlnnpGfn5+KFCmiNm3a6NChQw55CQkJCg8P1/3336/ChQurXLly6tGjh37++edMzBYAAMhqmX6w6HvvvWf33mw2y8/PT/3799fYsWOzrDAgtySt2SbdjHc65vafzjxIFAAAAAXS7t271aZNG1WsWFGDBw9W6dKl9ccff+iHH37QzJkz9eKLL9pyJ0+erO7du6tbt245UtvBgwcVFBSkJ554QsWKFdOvv/6qjz/+WOvWrdORI0dUpEgRW+64ceM0ZcoUDR48WI0bN9bq1av15JNPymQy6YknnrA7r4eHh+bPn28X8/b2tnv/2GOP6b777nOo6bXXXtONGzfUuHHjdGu3Wq3q1KmTfvzxR40aNUq+vr6aM2eOWrdurYMHD6patf/7+aJPnz5as2aNBg8erAYNGujcuXOaPXu2mjVrpp9++kmVKlXK8JwBAICsk+km+unTp7OjDiDPsO486BAzVSwt15BmNNABAABQYL399tvy9vbW/v37Vbx4cbuxixcv5k5R/99XX33lEGvWrJm6d++ub775xtYc/+uvvzRt2jS98MILmjVrliTp6aefVqtWrTRq1Cj16NFDLi4utnO4urqqb9++6V67bt26qlu3rl3sjz/+0J9//qmnn35a7u7u6R6/YsUK7d69W8uXL1f37t0lST179lRgYKDGjx+vJUuW2GpfuXKlRo4cqalTp9qOb9GihR566CGtXLlSw4cPT/daAAAge2TZnuhAQZAwcY5ksdoH3Vzl8XI/GugAAAAo0E6dOqVatWo5NNAlqVSpUravTSaTbt68qYULF9q2NBkwYIBtfNeuXWrcuLE8PT1VtWpVffjhh06vd/nyZR07dkxxcXF3VG9AQICkf7ZKSbV69WolJyfr+eeft6v3ueee059//qk9e/Y4nMdisSg2NjZT1/7iiy9kGIb69Olz29wVK1bI399fjz32mC3m5+ennj17avXq1UpMTJQkXb9+XZLk7+9vd3yZMmUkSYUKFcpUjQAAIOtkuomekJCgqVOnqmPHjmrUqJEaNGhg9wLyq4Twj6WYGw5xl1aNcqEaAAAAIGdVqlRJBw8eVGRkZLp5ixYtkoeHh1q0aKFFixZp0aJFGjJkiKR/9lRv166dLl68qAkTJmjgwIEaP368vv76a4fzzJo1SzVr1tS+ffsyVJ9hGLp8+bLOnz+vnTt3atiwYXJxcVHr1q1tOYcPH1aRIkVUs2ZNu2ObNGliG79VXFycvLy85O3trRIlSuiFF17QjRuOPxP82+LFi1WhQgW1bHn7ZyUdPnxYDRo0kNls/+N3kyZNFBcXpxMnTkiSqlatqvLly2vatGn65ptv9Oeff2rfvn169tlnVblyZYetaAAAQM7J9HYugwYN0nfffafu3burSZMmPGEdBULy+h3SpasOcVNAWR4iCgAAgHvCyJEj1aFDBwUFBalJkyZq0aKFHn74YbVp00Zubm62vL59++rZZ59VlSpVHLZCCQsLk2EY2rlzpypWrChJevzxx1WnTp27ru/ChQu2VdmSVL58eS1ZssTuQaDR0dHy9/d3+Dk19bhz587ZxUaPHq0GDRrIarVqw4YNmjNnjn788UdFRETI1dX5j8s///yzjh49qtGjR2fo5+Ho6GinzfZba6pTp47c3Nz01Vdf6cknn1SXLl1seQ0bNtTu3bud/oUAAADIGZluoq9du1br169X8+bNs6MeIFdYfjjqGCxVQh7D0t8fEQAAACgo2rZtqz179ig8PFwbN27Unj179M4778jPz0/z58+3a+w6Y7FYtHHjRnXr1s3WQJekmjVrKjQ0VOvXr7fLnzBhgiZMmJDh+kqUKKFNmzYpISFBhw8f1sqVKx1WjcfHx8vDw8PhWE9PT9t4qvDwcLucJ554QoGBgRo3bpxWrFiR5srvxYsXS1KGtnLJbE0+Pj4KCgpSjx491LRpU508eVLh4eHq0aOHNm3aZDsGAADkrExv51KuXDkVK1YsO2oBck98gv17k+T56tO5UwsAAACQSxo3bqyVK1fq6tWr2rdvn8aOHavr16+re/fu+uWXX9I99tKlS4qPj1e1ao7PEqpevfpd1+bu7q6QkBA98sgjeuONNzR79mwNGjRIa9euteUUKlTItsf4rRISEmzj6Rk+fLjMZrM2b97sdNwwDC1ZskS1a9d2eNhoWjJaU0xMjFq0aKFmzZopPDxcXbt21SuvvKKvvvpKu3bt0meffZah6wEAgKyX6Sb6tGnTNGbMGJ05cyY76gFyXNK6HY4PE/V0z51iAAAAgDzA3d1djRs31uTJkzV37lwlJydr+fLluV2WnQceeEBlypSxrQyX/tki5fz58zIMwy43OjpaklS2bNl0z1moUCGVLFlSf//9t9Px77//XmfOnMnwKvTUmlKvn15NX331lS5cuOCw4r9Vq1by8vLS999/n+FrAgCArJXpJnqjRo2UkJCgKlWqqFixYipRooTdC8hPLJFRsm75wSFu8i2ZC9UAAAAAeU+jRo0kya4R7GwvcD8/PxUqVEhRUVEOY8ePH8+W2hISEhQTE2N7HxQUpLi4OP366692eXv37rWNp+f69eu6fPmy/Pz8nI4vXrxYJpNJTz75ZIZrDAoK0qFDh2S12i/c2bt3rwoXLqzAwEBJ/+z5Lv2zLc6tDMOQxWJRSkpKhq8JAACyVqab6L1799Zff/2lyZMn64MPPtB7771n9wLyk+RVW53GXds2zeFKAAAAgNy1bds2hxXckmx7md+6JUuRIkV07do1uzwXFxeFhoZq1apVOnv2rC3+66+/auPGjQ7nvXz5so4dO6a4uLh067p586bTnK+++kpXr161NfklqWvXrnJzc9OcOXNsMcMwNG/ePJUrV04PPPCApH+a79evX3c456RJk2QYhtq3b+8wlroa/8EHH7Tb8/1W0dHROnbsmJKTk22x7t2768KFC1q5cqXdZ1++fLk6d+5s2y89tZm+dOlSu3OuWbNGN2/eVP369Z1eEwAAZL9MP1h09+7d2rNnj+rVq5cd9QA5xhIZJf0d4xB3CWkql9qO+zgCAAAABdmLL76ouLg4Pfroo6pRo4aSkpK0e/duLVu2TAEBARo4cKAtt2HDhtq8ebOmT5+usmXLqnLlygoODtbEiRO1YcMGtWjRQs8//7xSUlL0wQcfqFatWjp69Kjd9WbNmqWJEydq27Ztat26dZp1RUVFKSQkRL169VKNGjVkNpt14MABff755woICNBLL71kyy1fvrxefvllTZ06VcnJyWrcuLFWrVqlnTt3avHixXJxcZEknT9/XvXr11fv3r1Vo0YNSdLGjRu1fv16tW/fXl27dnWoY+PGjbpy5Uq6W7mMHTtWCxcu1OnTpxUQECDpnyZ606ZNNXDgQP3yyy/y9fXVnDlzZLFYNHHiRNuxnTt3Vq1atfTmm2/qzJkztgeLzpo1S2XKlNGgQYPS/o8HAACyVaab6DVq1LB7ejiQ3yR+tFzGybOO+6BLkq+P3Dq2zPmiAAAAgFz27rvvavny5Vq/fr0++ugjJSUlqWLFinr++ef1+uuvq3jx4rbc6dOn65lnntHrr7+u+Ph49e/fX8HBwapbt642btyoESNGKCwsTOXLl9fEiRMVHR3t0ETPqPLly+vxxx/X1q1btXDhQiUnJ6tSpUoaOnSoxo0bp5Il7bdinDJlinx8fPThhx9qwYIFqlatmj7//HO7LViKFy+uRx55RJs2bdLChQtlsVh03333afLkyRo5cqTMZsc/2l68eLHc3NzUo0ePTNXv4uKi9evXa9SoUXr//fcVHx+vxo0ba8GCBXar+93d3bVz505NmjRJ69at0xdffKFixYqpW7dumjx5snx9fTM5cwAAIKuYDGd/r5eO7777ThMnTtTbb7+tOnXqyM3NzW7cy8srSwvMarGxsfL29lZMTEyerxVZL2HmIumM40N9Urk99Sir0AEAwB3h+0wAAACgYMr0SvTUveEefvhhu7hhGDKZTA4PQQHyCktkVLoNdHO9QBroAAAAAAAAAOxkuom+bdu27KgDyHYpm/Y4Bgt5yFSiuMw1K7ONCwAAAAAAAAAHmW6it2rVKjvqALKdcf6yQ8ytd0dWnwMAAAAAAABIU6ab6Dt27Eh3vGVLVvMi77FERknJKfZBd1ca6AAAAAAAAADSlekmeuvWrR1iJpPJ9jV7oiMvSl62wSFm8vfLhUoAAAAAAAAA5CfmzB5w9epVu9fFixe1YcMGNW7cWN9991121AjclcQFq6Sb8Q5x17ZNc74YAAAAAAAAAPlKpleie3t7O8Tatm0rd3d3jRgxQgcPHsySwoCskLx+h4yjJxzi5nqBbOUCAAAAAAAA4LYyvRI9Lf7+/jp+/HhWnQ64a5bIKFk2/+A4UKyI3Pt3y/F6AAAAAAAAAOQ/mV6JfvToUbv3hmEoOjpaU6ZMUVBQUFbVBdy15OXOtxdy69EuhysBAAAAAAAAkF9luokeFBQkk8kkwzDs4k2bNtWnn36aZYUBd8MSGSVdv+kQdwlpyjYuAAAAAAAAADIs003006dP2703m83y8/OTp6dnlhUF3I2kdTtkjdjnOFCsiNw6tsz5ggAAAAAAAADkW5luoleqVCk76gCyRNLa7bJu3et0jG1cAAAAAAAAAGRWhh8sunXrVt1///2KjY11GIuJiVGtWrW0c+fOLC0OyIykbyLSbKCb6wWyjQsAAAAAAACATMtwE33GjBkaPHiwvLy8HMa8vb01ZMgQTZ8+PUuLAzIqef0OWbc52cLFbJJLSFO59++W4zUBAAAAAAAAyP8y3ET/8ccf1b59+zTH27Vrp4MHD2ZJUUBmWXYdchp3eSiYfdABAAAAAAAA3LEM74l+4cIFubm5pX0iV1ddunQpS4oCMsMSGSUlJNkHTZLLw01poAMAAAAAAAC4KxleiV6uXDlFRkamOX706FGVKVMmS4oCMiN58TqHmKl8GRroAAAAAAAAAO5ahpvoHTt21BtvvKGEhASHsfj4eI0fP16PPPJIlhYH3E7SwlVSYpJD3LVt05wvBgAAAAAAAECBYzIMw8hI4oULF9SgQQO5uLho6NChql69uiTp2LFjmj17tiwWiw4dOiR/f/9sLfhuxcbGytvbWzExMU4fkor8JWHMdCk5xS5mrhfIg0QBAECO4/tMAAAAoGDK8J7o/v7+2r17t5577jmNHTtWqb13k8mk0NBQzZ49O8830FEAWaz2702igQ4AAAAAAAAgy2S4iS5JlSpV0vr163X16lWdPHlShmGoWrVq8vHxya76gPT9+w8p3DJ1SwMAAAAAAABAuu6o4+jj46PGjRtndS1ApiSv3+HYRHd3z51iAAAAAAAAABRIGX6wKJDXWCL2O8RcmtbNhUoAAAAAAAAAFFTsfYF8xxIZpeTlG6UUi/2Am6vcOrbMnaIAAAAAAAAAFEg00ZGvWCKjlPzp107HXFo1yuFqAAAAAAAAABR0bOeCfCV55WancVNAWVahAwAAAAAAAMhyrERHvmGJjJKuXXeIm+sFyr1/t5wvCAAAAAAAAECBx0p05BvJyzY4Bj3daaADAAAAAAAAyDY00ZEvJMxYJN2Md4i7PNggF6oBAAAAAAAAcK+giY48L3n9DulstEOcfdABAAAAAAAAZDea6MjzLD8cdYiZAsrKY1jfXKgGAAAAAAAAwL2EJjryvoQk+/dmEw10AAAAAAAAADmCJjryNEtklJSSYh90dcmdYgAAAAAAAADcc1xzuwAgLYlzlso4edZxwN0954sBAAAAAAAAcE+iiY48J/GTlTJ+PSVZDafjLk3r5nBFAAAAAAAAAO5VNNGRpyQtXCXj55NpjpvrBcqtY8scrAgAAAAAAADAvYwmOvIUa+Qp5wO+xeXWpY1calfL2YIAAAAAAAAA3NNooiPPSF6/Q7JYHOIuIU1ZfQ4AAAAAAAAgV9BER56Q9E2ErNv2OcRNFcrQQAcAAAAAAACQa8y5XQBgiYxy2kCXJNe2TXO4GgAAAAAAAAD4PzTRkeuSV211GncJacoe6AAAAAAAAAByFU105CpLZJT0d4xDnH3QAQAAAAAAAOQFNNGRq5K/3ekY9PWhgQ4AAAAAAAAgT6CJjlyTtG6HFH3ZIe7WpXXOFwMAAAAAAAAATrjmdgG4NyUtXCXrjyccBzzd2QcdAAAAAAAAQJ6RJ1aiz549WwEBAfL09FRwcLD27duXZu7HH3+sFi1ayMfHRz4+PgoJCUk3H3mPJTLKeQNdksuDDXK4GgAAAAAAAABIW6430ZctW6YRI0Zo/PjxOnTokOrVq6fQ0FBdvHjRaX5ERIR69+6tbdu2ac+ePapQoYLatWunv/76K4crx51K2bTHadxcL5C90AEAAAAAAADkKSbDMIzcLCA4OFiNGzfWrFmzJElWq1UVKlTQiy++qFdfffW2x1ssFvn4+GjWrFnq16/fbfNjY2Pl7e2tmJgYeXl53XX9yLyEke9KVqtdzCWkKQ10AACQr/F9JgAAAFAw5epK9KSkJB08eFAhISG2mNlsVkhIiPbscb5a+d/i4uKUnJysEiVKOB1PTExUbGys3Qu5J2HGIocGujzdaaADAAAAAAAAyJNytYl++fJlWSwW+fv728X9/f11/vz5DJ1jzJgxKlu2rF0j/lbh4eHy9va2vSpUqHDXdePOJH72tXQ22iHOPugAAAAAAAAA8qpc3xP9bkyZMkVLly7V119/LU9PT6c5Y8eOVUxMjO31xx9/5HCVkP55mKjxU5RD3BRQllXoAAAAAAAAAPIs19y8uK+vr1xcXHThwgW7+IULF1S6dOl0j3333Xc1ZcoUbd68WXXr1k0zz8PDQx4eHllSL+5c8pptjsFiReQxrG/OFwMAAAAAAAAAGZSrK9Hd3d3VsGFDbdmyxRazWq3asmWLmjVrluZx77zzjiZNmqQNGzaoUaNGOVEq7oIlMkq6fM0h7tajXc4XAwAAAAAAAACZkKsr0SVpxIgR6t+/vxo1aqQmTZpoxowZunnzpgYOHChJ6tevn8qVK6fw8HBJ0n//+1+FhYVpyZIlCggIsO2dXrRoURUtWjTXPgfSlvzFesegr49calfL+WIAAAAAAAAAIBNyvYneq1cvXbp0SWFhYTp//ryCgoK0YcMG28NGz549K7P5/xbMz507V0lJSerevbvdecaPH68JEybkZOnIgOT1O6T4RIe4W5fWOV8MAAAAAAAAAGSSyTAMI7eLyEmxsbHy9vZWTEyMvLy8crucAi8hbJZ0I84uZgooy17oAACgwOH7TAAAAKBgytU90VGwWSKjHBroMokGOgAAAAAAAIB8gyY6soUlMkrJn37tOFCkcM4XAwAAAAAAAAB3iCY6skXymm1O4y5N6+ZwJQAAAAAAAABw52iiI8tZIqOky9cc4uZ6gXLr2DLnCwIAAAAAAACAO0QTHVkuedm3jkFfH7n375bjtQAAAAAAAADA3aCJjiyVMHORdDPBIe7WpXXOFwMAAAAAAAAAd4kmOrJM4oJV0ploh7i5XqBcalfL+YIAAAAAAAAA4C7RREeWsERGyTh6wnGgWBG2cQEAAAAAAACQb9FER5ZIXrnFadytR7scrgQAAAAAAAAAsg5NdNw1S2SUdC3WIe4S0pRtXAAAAAAAAADkazTRcdeSV211DPr6yK1jy5wvBgAAAAAAAACyEE103BVLZJT0d4xD3K1L65wvBgAAAAAAAACyGE103JWUTXscg74+bOMCAAAAAAAAoECgiY47ZomMkvHXRYc4q9ABAAAAAAAAFBSuuV0A8idLZJSSP/3accDdlVXoAAAAAAAAAAoMVqLjjiSv+M5p3OTvl8OVAAAAAAAAAED2oYmOTLNERkmxN52OubZtmsPVAAAAAAAAAED2YTsXZFryqq2OQU8PuT3Zka1cAAAAAAAAABQorERHplgio6S/YxziNNABAAAAAAAAFEQ00ZEpyWu2OQZ9fWigAwAAAAAAACiQaKIjwyyRUdLlaw5xty6tc7wWAAAAAAAAAMgJ7ImODEn5KUopi9Y4DrAKHQAAAAAAAEABRhMdt5W0brusW/Y6HWMVOgAAAAAAAICCjCY60pUwbYH010WnY+Z6gaxCBwAAAAAAAFCgsSc60pQw8/N0G+ju/bvlbEEAAAAAAAAAkMNYiQ6nEuevkM6cczrmEtJUbh1b5nBFAAAAAAAAAJDzaKLDQfL6HTJ++c1xwM1Vbv/pzBYuAAAAAAAAAO4ZbOcCB5adh5zGaaADAAAAAAAAuNewEh12LJFRUmKSfdBsktuAbjTQAQAAAAAAANxzaKLDJuXoCaUsXusQN5UrTQMdAAAAAAAAwD2JJjokSUnrdsi65QenY65tm+ZwNQAAAAAAAACQN7AnOmSJjEqzgW6uF8gqdAAAAAAAAAD3LJroUPKSdU7j5nqBcu/fLWeLAQAAAAAAAIA8hO1c7nFJC1dJCUkOcZeQpnLr2DLnCwIAAAAAAACAPISV6PewpHU7ZP3xhEPcXC+QBjoAAAAAAAAAiCb6PSt5fRoPEnUxs4ULAAAAAAAAAPx/NNHvQZbIKFm27nU65tKmSQ5XAwAAAAAAAAB5F3ui32OS1++QZbOTFehmk1weCmYbFwAAAAAAAAC4BSvR7yGWyCjnDXSJBjoAAAAAAAAAOEET/R6S/PUWp3EeJAoAAAAAAAAAzrGdyz3CEhklXY11iLuENKWBDgAAAAAAAABpYCX6PSJ5+XeOQV8fGugAAAAAAAAAkA6a6PeA5PU7pOs3HeJuXVrnfDEAAAAAAAAAkI/QRL8HWPb86Bj09ZFL7Wo5XwwAAAAAAAAA5CM00Qs4S2SUdDPeIc4qdAAAAAAAAAC4PZroBZglMkrJn37tOODpzip0AAAAAAAAAMgAmugFWMqmPU7jLg82yOFKAAAAAAAAACB/oolegBnnLzvEzPUC5daxZS5UAwAAAAAAAAD5D030AirlpxNScop90N1V7v275Uo9AAAAAAAAAJAfueZ2AchaSV9vlvXIcen6TYcxk79fLlQEAAAAAAAAAPkXTfQCImndDlm3H5BSUtLMcW3bNAcrAgAAAAAAAID8jyZ6AZC0cJWsP55IN8dcL1AutavlUEUAAAAAAAAAUDDQRM/nLJFRaTbQTQFlpWSLzDUr8zBRAAAAAAAAALgDNNHzMUtklJKXrHc6Zq4XyENEAQAAAAAAAOAu0UTPp5LW7ZB1yw9Ox1xCmrLyHAAAAAAAAACyAE30fMISGSXrybMyVa0oxcen2UA31wukgQ4AAAAAAAAAWYQmeh6X9NV3sh46JsUn/BPYcTDNXLZwAQAAAAAAAICsRRM9D0tev0PW749kKJctXAAAAAAAAAAg69FEz4NSjp5QysbvpehLTsfNDzWRdes+23sa6AAAAAAAAACQPWii5zHJ63fIstn5fudyc5XbfzrLpXY1WQLKyXrqD5mrVpBL7Wo5WyQAAAAAAAAA3CNooucBqQ8Ntf4dIyPyZJp5Lq0a2RrmLrWr0TwHAAAAAAAAgGxmzu0CJGn27NkKCAiQp6engoODtW/fvnTzly9frho1asjT01N16tTR+vXrc6jSrJe8foeSP/1alh0H026gl/RmyxYAAAAAAAAAyAW53kRftmyZRowYofHjx+vQoUOqV6+eQkNDdfHiRaf5u3fvVu/evTVo0CAdPnxY3bp1U7du3RQZGZnDld85S2SUkldtUeInK9PeuuX/M9cLlOe4ITTQAQAAAAAAACAXmAzDMHKzgODgYDVu3FizZs2SJFmtVlWoUEEvvviiXn31VYf8Xr166ebNm1q7dq0t1rRpUwUFBWnevHm3vV5sbKy8vb0VExMjLy+vrPsgt2HcjFfKrkOyHP5Vuvj37Q8oVlguwXVpngMAAOQTufV9JgAAAIDslat7oiclJengwYMaO3asLWY2mxUSEqI9e/Y4PWbPnj0aMWKEXSw0NFSrVq1ymp+YmKjExETb+9jY2Lsv/A4kL1oj64kzt80z16oql+C67HcOAAAAAAAAAHlArjbRL1++LIvFIn9/f7u4v7+/jh075vSY8+fPO80/f/680/zw8HBNnDgxawq+C4Y1/QX/poql5RrSjOY5AAAAAAAAAOQhudpEzwljx461W7keGxurChUq5HgdLi0aKuXkWclskqyGzLWqylTGT0pOkblqBZrnAAAAAAAAAJAH5WoT3dfXVy4uLrpw4YJd/MKFCypdurTTY0qXLp2pfA8PD3l4eGRNwXfBtU41mZ56VNZTf9A0BwAAAAAAAIB8wpybF3d3d1fDhg21ZcsWW8xqtWrLli1q1qyZ02OaNWtmly9JmzZtSjM/L3GpXU1uXR+igQ4AAAAAAAAA+USub+cyYsQI9e/fX40aNVKTJk00Y8YM3bx5UwMHDpQk9evXT+XKlVN4eLgk6aWXXlKrVq00bdo0derUSUuXLtWBAwf00Ucf5ebHAAAAAAAAAAAUQLneRO/Vq5cuXbqksLAwnT9/XkFBQdqwYYPt4aFnz56V2fx/C+YfeOABLVmyRK+//rpee+01VatWTatWrVLt2rVz6yMAAAAAAAAAAAook2EYRm4XkZNiY2Pl7e2tmJgYeXl55XY5AAAAKCD4PhMAAAAomHJ1T3QAAAAAAAAAAPIymugAAAAAAAAAAKSBJjoAAAAAAAAAAGmgiQ4AAAAAAAAAQBpoogMAAAAAAAAAkAaa6AAAAAAAAAAApIEmOgAAAAAAAAAAaaCJDgAAAAAAAABAGmiiAwAAAAAAAACQBproAAAAAAAAAACkwTW3C8hphmFIkmJjY3O5EgAAABQkqd9fpn6/CQAAAKBguOea6NevX5ckVahQIZcrAQAAQEF0/fp1eXt753YZAAAAALKIybjHlspYrVadO3dOxYoVk8lkyu1yCoTY2FhVqFBBf/zxh7y8vHK7nAKJOc4ZzHPOYJ5zBvOcM5jn7Jef5tgwDF2/fl1ly5aV2cyuiQAAAEBBcc+tRDebzSpfvnxul1EgeXl55fkfbvM75jhnMM85g3nOGcxzzmCes19+mWNWoAMAAAAFD0tkAAAAAAAAAABIA010AAAAAAAAAADSQBMdd83Dw0Pjx4+Xh4dHbpdSYDHHOYN5zhnMc85gnnMG85z9mGMAAAAAue2ee7AoAAAAAAAAAAAZxUp0AAAAAAAAAADSQBMdAAAAAAAAAIA00EQHAAAAAAAAACANNNGh8PBwNW7cWMWKFVOpUqXUrVs3HT9+3C6ndevWMplMdq9nn33WLufs2bPq1KmTChcurFKlSmnUqFFKSUmxy4mIiFCDBg3k4eGh++67TwsWLMjuj5dnTJgwwWEOa9SoYRtPSEjQCy+8oJIlS6po0aJ6/PHHdeHCBbtzMMe3FxAQ4DDPJpNJL7zwgiTu5Tu1Y8cOde7cWWXLlpXJZNKqVavsxg3DUFhYmMqUKaNChQopJCREUVFRdjl///23+vTpIy8vLxUvXlyDBg3SjRs37HKOHj2qFi1ayNPTUxUqVNA777zjUMvy5ctVo0YNeXp6qk6dOlq/fn2Wf97ckN4cJycna8yYMapTp46KFCmismXLql+/fjp37pzdOZzd/1OmTLHLuZfnWLr9vTxgwACHOWzfvr1dDvfy7d1unp39O20ymTR16lRbDvczAAAAgLyCJjq0fft2vfDCC/rhhx+0adMmJScnq127drp586Zd3uDBgxUdHW173fqDqsViUadOnZSUlKTdu3dr4cKFWrBggcLCwmw5p0+fVqdOndSmTRsdOXJEL7/8sp5++mlt3Lgxxz5rbqtVq5bdHO7atcs2Nnz4cH3zzTdavny5tm/frnPnzumxxx6zjTPHGbN//367Od60aZMkqUePHrYc7uXMu3nzpurVq6fZs2c7HX/nnXf0/vvva968edq7d6+KFCmi0NBQJSQk2HL69Omjn3/+WZs2bdLatWu1Y8cOPfPMM7bx2NhYtWvXTpUqVdLBgwc1depUTZgwQR999JEtZ/fu3erdu7cGDRqkw4cPq1u3burWrZsiIyOz78PnkPTmOC4uTocOHdIbb7yhQ4cOaeXKlTp+/Li6dOnikPvmm2/a3d8vvviibexen2Pp9veyJLVv395uDr/44gu7ce7l27vdPN86v9HR0fr0009lMpn0+OOP2+VxPwMAAADIEwzgXy5evGhIMrZv326LtWrVynjppZfSPGb9+vWG2Ww2zp8/b4vNnTvX8PLyMhITEw3DMIzRo0cbtWrVsjuuV69eRmhoaNZ+gDxq/PjxRr169ZyOXbt2zXBzczOWL19ui/3666+GJGPPnj2GYTDHd+qll14yqlatalitVsMwuJezgiTj66+/tr23Wq1G6dKljalTp9pi165dMzw8PIwvvvjCMAzD+OWXXwxJxv79+2053377rWEymYy//vrLMAzDmDNnjuHj42ObZ8MwjDFjxhjVq1e3ve/Zs6fRqVMnu3qCg4ONIUOGZOlnzG3/nmNn9u3bZ0gyzpw5Y4tVqlTJeO+999I8hjm252ye+/fvb3Tt2jXNY7iXMy8j93PXrl2Nhx56yC7G/QwAAAAgr2AlOhzExMRIkkqUKGEXX7x4sXx9fVW7dm2NHTtWcXFxtrE9e/aoTp068vf3t8VCQ0MVGxurn3/+2ZYTEhJid87Q0FDt2bMnuz5KnhMVFaWyZcuqSpUq6tOnj86ePStJOnjwoJKTk+3mp0aNGqpYsaJtfpjjzEtKStLnn3+up556SiaTyRbnXs5ap0+f1vnz5+3mxNvbW8HBwXb3b/HixdWoUSNbTkhIiMxms/bu3WvLadmypdzd3W05oaGhOn78uK5evWrLYe7/ERMTI5PJpOLFi9vFp0yZopIlS6p+/fqaOnWq3VZEzHHGREREqFSpUqpevbqee+45XblyxTbGvZz1Lly4oHXr1mnQoEEOY9zPAAAAAPIC19wuAHmL1WrVyy+/rObNm6t27dq2+JNPPqlKlSqpbNmyOnr0qMaMGaPjx49r5cqVkqTz58/bNR0l2d6fP38+3ZzY2FjFx8erUKFC2fnRcl1wcLAWLFig6tWrKzo6WhMnTlSLFi0UGRmp8+fPy93d3aEZ5u/vf9v5Sx1LL+demeN/W7Vqla5du6YBAwbYYtzLWS91XpzNya1zVqpUKbtxV1dXlShRwi6ncuXKDudIHfPx8Ulz7lPPca9ISEjQmDFj1Lt3b3l5edniw4YNU4MGDVSiRAnt3r1bY8eOVXR0tKZPny6JOc6I9u3b67HHHlPlypV16tQpvfbaa+rQoYP27NkjFxcX7uVssHDhQhUrVsxuCzOJ+xkAAABA3kETHXZeeOEFRUZG2u3VLclur9c6deqoTJkyevjhh3Xq1ClVrVo1p8vMlzp06GD7um7dugoODlalSpX05Zdf3nNN15zyySefqEOHDipbtqwtxr2M/C45OVk9e/aUYRiaO3eu3diIESNsX9etW1fu7u4aMmSIwsPD5eHhkdOl5ktPPPGE7es6deqobt26qlq1qiIiIvTwww/nYmUF16effqo+ffrI09PTLs79DAAAACCvYDsX2AwdOlRr167Vtm3bVL58+XRzg4ODJUknT56UJJUuXVoXLlywy0l9X7p06XRzvLy87skmcvHixRUYGKiTJ0+qdOnSSkpK0rVr1+xyLly4cNv5Sx1LL+denOMzZ85o8+bNevrpp9PN416+e6nz4mxObp2zixcv2o2npKTo77//zpJ7PHW8oEttoJ85c0abNm2yW4XuTHBwsFJSUvT7779LYo7vRJUqVeTr62v3bwT3ctbZuXOnjh8/ftt/qyXuZwAAAAC5hyY6ZBiGhg4dqq+//lpbt251+NNoZ44cOSJJKlOmjCSpWbNm+umnn+waC6kNnvvvv9+Ws2XLFrvzbNq0Sc2aNcuiT5K/3LhxQ6dOnVKZMmXUsGFDubm52c3P8ePHdfbsWdv8MMeZ89lnn6lUqVLq1KlTunncy3evcuXKKl26tN2cxMbGau/evXb377Vr13Tw4EFbztatW2W1Wm2/yGjWrJl27Nih5ORkW86mTZtUvXp1+fj42HLu1blPbaBHRUVp8+bNKlmy5G2POXLkiMxms237EeY48/78809duXLF7t8I7uWs88knn6hhw4aqV6/ebXO5nwEAAADkmtx+sily33PPPWd4e3sbERERRnR0tO0VFxdnGIZhnDx50njzzTeNAwcOGKdPnzZWr15tVKlSxWjZsqXtHCkpKUbt2rWNdu3aGUeOHDE2bNhg+Pn5GWPHjrXl/Pbbb0bhwoWNUaNGGb/++qsxe/Zsw8XFxdiwYUOOf+bc8MorrxgRERHG6dOnje+//94ICQkxfH19jYsXLxqGYRjPPvusUbFiRWPr1q3GgQMHjGbNmhnNmjWzHc8cZ5zFYjEqVqxojBkzxi7OvXznrl+/bhw+fNg4fPiwIcmYPn26cfjwYePMmTOGYRjGlClTjOLFixurV682jh49anTt2tWoXLmyER8fbztH+/btjfr16xt79+41du3aZVSrVs3o3bu3bfzatWuGv7+/8Z///MeIjIw0li5dahQuXNj48MMPbTnff/+94erqarz77rvGr7/+aowfP95wc3Mzfvrpp5ybjGyS3hwnJSUZXbp0McqXL28cOXLE7t/qxMREwzAMY/fu3cZ7771nHDlyxDh16pTx+eefG35+fka/fv1s17jX59gw0p/n69evGyNHjjT27NljnD592ti8ebPRoEEDo1q1akZCQoLtHNzLt3e7fzMMwzBiYmKMwoULG3PnznU4nvsZAAAAQF5CEx2GJKevzz77zDAMwzh79qzRsmVLo0SJEoaHh4dx3333GaNGjTJiYmLszvP7778bHTp0MAoVKmT4+voar7zyipGcnGyXs23bNiMoKMhwd3c3qlSpYrvGvaBXr15GmTJlDHd3d6NcuXJGr169jJMnT9rG4+Pjjeeff97w8fExChcubDz66KNGdHS03TmY44zZuHGjIck4fvy4XZx7+c5t27bN6b8T/fv3NwzDMKxWq/HGG28Y/v7+hoeHh/Hwww87zP+VK1eM3r17G0WLFjW8vLyMgQMHGtevX7fL+fHHH40HH3zQ8PDwMMqVK2dMmTLFoZYvv/zSCAwMNNzd3Y1atWoZ69aty7bPnZPSm+PTp0+n+W/1tm3bDMMwjIMHDxrBwcGGt7e34enpadSsWdOYPHmyXfPXMO7tOTaM9Oc5Li7OaNeuneHn52e4ubkZlSpVMgYPHmycP3/e7hzcy7d3u38zDMMwPvzwQ6NQoULGtWvXHI7nfgYAAACQl5gMwzCydak7AAAAAAAAAAD5FHuiAwAAAAAAAACQBproAAAAAAAAAACkgSY6AAAAAAAAAABpoIkOAAAAAAAAAEAaaKIDAAAAAAAAAJAGmugAAAAAAAAAAKSBJjoAAAAAAAAAAGmgiQ4AAAAAAAAAQBpoogMA8rTff/9dJpNJR44cye1SAAAAAADAPYgmOgDkMpPJlO5rwoQJd3zujDag80qjesCAAerWrVuu1gAAAAAAAHAr19wuAADuddHR0bavly1bprCwMB0/ftwWK1q0aG6UBQAAAAAAALESHQByXenSpW0vb29vmUwmu9jSpUtVs2ZNeXp6qkaNGpozZ47t2Keeekp169ZVYmKiJCkpKUn169dXv379JEmVK1eWJNWvX18mk0mtW7e+oxqtVqvCw8NVuXJlFSpUSPXq1dOKFSts4xERETKZTNqyZYsaNWqkwoUL64EHHrD7ZYAkvfXWWypVqpSKFSump59+Wq+++qqCgoIkSRMmTNDChQu1evVq2yr8iIgI27G//fab2rRpo8KFC6tevXras2fPHX0WAAAAAACAzKCJDgB52OLFixUWFqa3335bv/76qyZPnqw33nhDCxculCS9//77unnzpl599VVJ0rhx43Tt2jXNmjVLkrRv3z5J0ubNmxUdHa2VK1feUR3h4eH63//+p3nz5unnn3/W8OHD1bdvX23fvt0ub9y4cZo2bZoOHDggV1dXPfXUU3af5e2339Z///tfHTx4UBUrVtTcuXNt4yNHjlTPnj3Vvn17RUdHKzo6Wg888IDduUeOHKkjR44oMDBQvXv3VkpKyh19HgAAAAAAgIxiOxcAyMPGjx+vadOm6bHHHpP0z8ryX375RR9++KH69++vokWL6vPPP1erVq1UrFgxzZgxQ9u2bZOXl5ckyc/PT5JUsmRJlS5d+o5qSExM1OTJk7V582Y1a9ZMklSlShXt2rVLH374oVq1amXLffvtt23vX331VXXq1EkJCQny9PTUBx98oEGDBmngwIGSpLCwMH333Xe6ceOGpH+2rSlUqJASExOd1jpy5Eh16tRJkjRx4kTVqlVLJ0+eVI0aNe7ocwEAAAAAAGQEK9EBII+6efOmTp06pUGDBqlo0aK211tvvaVTp07Z8po1a6aRI0dq0qRJeuWVV/Tggw9maR0nT55UXFyc2rZta1fH//73P7s6JKlu3bq2r8uUKSNJunjxoiTp+PHjatKkiV3+v9+nJ71zAwAAAAAAZBdWogNAHpW6Qvvjjz9WcHCw3ZiLi4vta6vVqu+//14uLi46efJkttWxbt06lStXzm7Mw8PD7r2bm5vta5PJZKsvK2TnuQEAAAAAANJCEx0A8ih/f3+VLVtWv/32m/r06ZNm3tSpU3Xs2DFt375doaGh+uyzz2xbpri7u0uSLBbLHddx//33y8PDQ2fPnrXbuiWzqlevrv3799seeipJ+/fvt8txd3e/q1oBAAAAAACyGk10AMjDJk6cqGHDhsnb21vt27dXYmKiDhw4oKtXr2rEiBE6fPiwwsLCtGLFCjVv3lzTp0/XSy+9pFatWqlKlSoqVaqUChUqpA0bNqh8+fLy9PSUt7d3mtc7fvy4Q6xWrVoaOXKkhg8fLqvVqgcffFAxMTH6/vvv5eXlpf79+2fos7z44osaPHiwGjVqpAceeEDLli3T0aNHVaVKFVtOQECANm7cqOPHj6tkyZLp1goAAAAAAJATaKIDQB729NNPq3Dhwpo6dapGjRqlIkWKqE6dOnr55ZeVkJCgvn37asCAAercubMk6ZlnntG6dev0n//8Rzt27JCrq6vef/99vfnmmwoLC1OLFi0UERGR5vWeeOIJh9gff/yhSZMmyc/PT+Hh4frtt99UvHhxNWjQQK+99lqGP0ufPn3022+/aeTIkUpISFDPnj01YMAA7du3z5YzePBgRUREqFGjRrpx44a2bdumgICADF8DAAAAAAAgq5kMwzByuwgAwL2pbdu2Kl26tBYtWpTbpQAAAAAAADjFSnQAQI6Ii4vTvHnzFBoaKhcXF33xxRfavHmzNm3alNulAQAAAAAApImV6ACAHBEfH6/OnTvr8OHDSkhIUPXq1fX666/rsccey+3SAAAAAAAA0kQTHQAAAAAAAACANJhzuwAAAAAAAAAAAPIqmugAAAAAAAAAAKSBJjoAAAAAAAAAAGmgiQ4AAAAAAAAAQBpoogMAAAAAAAAAkAaa6AAAAAAAAAAApIEmOgAAAAAAAAAAaaCJDgAAAAAAAABAGmiiAwAAAAAAAACQhv8Hhnc21tncemMAAAAASUVORK5CYII=", "text/plain": ["<Figure size 1500x1000 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Dataset Statistics:\n", "count: 758\n", "min_length: 991\n", "max_length: 17834\n", "mean_length: 5586.877308707124\n", "median_length: 5038.0\n", "std_length: 3057.076227833264\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1500x1000 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Dataset Statistics:\n", "count: 811\n", "min_length: 1146\n", "max_length: 25152\n", "mean_length: 7633.040690505549\n", "median_length: 6529.0\n", "std_length: 4745.430192930546\n"]}], "source": ["lengths, stats = analyze_dataset_direct(\"GAIR/LIMO\", \"solution\", tokenizer)\n", "lengths, stats = analyze_dataset_direct(\"junnei/ko-limo\", \"solution\", tokenizer)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d99a9e3c01284b21b8b244b755e3396a", "version_major": 2, "version_minor": 0}, "text/plain": ["Filter:   0%|          | 0/817 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "394adfd54b5b463b92000d90a53ca82e", "version_major": 2, "version_minor": 0}, "text/plain": ["Filter:   0%|          | 0/817 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "20a36f7c2dfd41efa2ece075a33a86cd", "version_major": 2, "version_minor": 0}, "text/plain": ["Filter:   0%|          | 0/817 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "55b933e2cd1e474bacb197f8ebc74767", "version_major": 2, "version_minor": 0}, "text/plain": ["Filter:   0%|          | 0/817 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6a86fc025ff54abf826286a97e430e32", "version_major": 2, "version_minor": 0}, "text/plain": ["Uploading the dataset shards:   0%|          | 0/1 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "564d86b07f284f03afa164d883afd408", "version_major": 2, "version_minor": 0}, "text/plain": ["Creating parquet from Arrow format:   0%|          | 0/1 [00:00<?, ?ba/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "11fceb5191da4f4ba753423447e0a897", "version_major": 2, "version_minor": 0}, "text/plain": ["Uploading the dataset shards:   0%|          | 0/1 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7cc1558dc68e41c9a7762f29998da2e2", "version_major": 2, "version_minor": 0}, "text/plain": ["Creating parquet from Arrow format:   0%|          | 0/1 [00:00<?, ?ba/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["No files have been modified since last commit. Skipping to prevent empty commit.\n"]}, {"data": {"text/plain": ["CommitInfo(commit_url='https://huggingface.co/datasets/junkim100/limo_crosslingual_ko_en/commit/1d78a9569bc93b2da168883c7d7e5e60efe2a79f', commit_message='Upload dataset', commit_description='', oid='1d78a9569bc93b2da168883c7d7e5e60efe2a79f', pr_url=None, repo_url=RepoUrl('https://huggingface.co/datasets/junkim100/limo_crosslingual_ko_en', endpoint='https://huggingface.co', repo_type='dataset', repo_id='junkim100/limo_crosslingual_ko_en'), pr_revision=None, pr_num=None)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["from datasets import Dataset\n", "from datasets import load_dataset\n", "\n", "# I'm going to create a new dataset where question column from junnei/ko-limo is kept as question, question from GAIR/LIMO as translation, solution from GAIR/LIMO as solution, and answer from GAIR/LIMO as answer.\n", "kolimo = load_dataset(\"junnei/ko-limo\")\n", "limo = load_dataset(\"GAIR/LIMO\")\n", "\n", "new_data = {\n", "    'question': kolimo['train']['question'],\n", "    'translation': limo['train']['question'],\n", "    'solution': limo['train']['solution'],\n", "    'answer': limo['train']['answer']\n", "}\n", "\n", "new_dataset = Dataset.from_dict(new_data)\n", "\n", "# Remove data with length larger than 40960 and less than 1\n", "new_dataset = new_dataset.filter(lambda x: len(x['question']) < 40960)\n", "new_dataset = new_dataset.filter(lambda x: len(x['question']) > 0)\n", "new_dataset = new_dataset.filter(lambda x: len(x['translation']) < 40960)\n", "new_dataset = new_dataset.filter(lambda x: len(x['translation']) > 0)\n", "\n", "# split train and test\n", "new_dataset = new_dataset.train_test_split(test_size=0.2, seed=42)\n", "\n", "new_dataset.push_to_hub(\"junkim100/limo_crosslingual_ko_en\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>question</th>\n", "      <th>translation</th>\n", "      <th>solution</th>\n", "      <th>answer</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>숫자 1, 2, …, 9의 순열 $p = (a_1, a_2, \\ldots, a_9)...</td>\n", "      <td>For a permutation $p = (a_1,a_2,\\ldots,a_9)$ o...</td>\n", "      <td>Alright, let's try to tackle this problem step...</td>\n", "      <td>162</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>삼각형 $ABC$에서, 각도 $C$는 직각이고, $C$에서 수직선은 $D$에서 $\\...</td>\n", "      <td>In triangle $ABC,\\,$ angle $C$ is a right angl...</td>\n", "      <td>Okay, let's tackle this geometry problem. So, ...</td>\n", "      <td>450</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>어떤 정수 $m$에 대해, 다항식 $x^3 - 2011x + m$의 세 개의 정수 ...</td>\n", "      <td>For some integer $m$ , the polynomial $x^3 - 2...</td>\n", "      <td>Okay, so I have this problem here: For some in...</td>\n", "      <td>98</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>정사각형의 변의 길이는 2입니다. 집합 $S$는 길이가 2이고 끝점이 정사각형의 인...</td>\n", "      <td>A square has sides of length 2. Set $S$ is the...</td>\n", "      <td>Okay, so I need to solve this geometry problem...</td>\n", "      <td>86</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>정육면체의 세 변은 $\\overline{AB}, \\overline{BC},$ 그리고...</td>\n", "      <td>Three of the edges of a cube are $\\overline{AB...</td>\n", "      <td>Okay, so I have this geometry problem here. Le...</td>\n", "      <td>525</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>159</th>\n", "      <td>7×1 크기의 보드는 겹치지 않고 $m×1$ 크기의 타일로 완전히 덮여 있습니다. ...</td>\n", "      <td>A $7\\times 1$ board is completely covered by $...</td>\n", "      <td>Okay, let's try to figure out this tiling prob...</td>\n", "      <td>106</td>\n", "    </tr>\n", "    <tr>\n", "      <th>160</th>\n", "      <td>원 $\\omega_1$과 $\\omega_2$는 두 점 $P$와 $Q$에서 교차하고,...</td>\n", "      <td>Circles $\\omega_1$ and $\\omega_2$ intersect at...</td>\n", "      <td>Okay, so I need to solve this geometry problem...</td>\n", "      <td>33</td>\n", "    </tr>\n", "    <tr>\n", "      <th>161</th>\n", "      <td>복소수 $z$와 $w$는 $z^{13} = w,$ $w^{11} = z$를 만족하고...</td>\n", "      <td>The complex numbers $z$ and $w$ satisfy $z^{13...</td>\n", "      <td>Okay, so I have this problem here about comple...</td>\n", "      <td>71</td>\n", "    </tr>\n", "    <tr>\n", "      <th>162</th>\n", "      <td>삼각형 $ABC$의 내부에 반지름이 같은 네 개의 원 $\\omega,$ $\\omeg...</td>\n", "      <td>Four circles $\\omega,$ $\\omega_{A},$ $\\omega_{...</td>\n", "      <td>Okay, so I need to find the radius of circle ω...</td>\n", "      <td>389</td>\n", "    </tr>\n", "    <tr>\n", "      <th>163</th>\n", "      <td>2004의 배수 중에서 2004개의 양의 정수로 나눌 수 있는 정수의 배수는 몇 개...</td>\n", "      <td>How many positive integer divisors of $2004^{2...</td>\n", "      <td>Alright, so I’ve just come across this math pr...</td>\n", "      <td>54</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>164 rows × 4 columns</p>\n", "</div>"], "text/plain": ["                                              question  \\\n", "0    숫자 1, 2, …, 9의 순열 $p = (a_1, a_2, \\ldots, a_9)...   \n", "1    삼각형 $ABC$에서, 각도 $C$는 직각이고, $C$에서 수직선은 $D$에서 $\\...   \n", "2    어떤 정수 $m$에 대해, 다항식 $x^3 - 2011x + m$의 세 개의 정수 ...   \n", "3    정사각형의 변의 길이는 2입니다. 집합 $S$는 길이가 2이고 끝점이 정사각형의 인...   \n", "4    정육면체의 세 변은 $\\overline{AB}, \\overline{BC},$ 그리고...   \n", "..                                                 ...   \n", "159  7×1 크기의 보드는 겹치지 않고 $m×1$ 크기의 타일로 완전히 덮여 있습니다. ...   \n", "160  원 $\\omega_1$과 $\\omega_2$는 두 점 $P$와 $Q$에서 교차하고,...   \n", "161  복소수 $z$와 $w$는 $z^{13} = w,$ $w^{11} = z$를 만족하고...   \n", "162  삼각형 $ABC$의 내부에 반지름이 같은 네 개의 원 $\\omega,$ $\\omeg...   \n", "163  2004의 배수 중에서 2004개의 양의 정수로 나눌 수 있는 정수의 배수는 몇 개...   \n", "\n", "                                           translation  \\\n", "0    For a permutation $p = (a_1,a_2,\\ldots,a_9)$ o...   \n", "1    In triangle $ABC,\\,$ angle $C$ is a right angl...   \n", "2    For some integer $m$ , the polynomial $x^3 - 2...   \n", "3    A square has sides of length 2. Set $S$ is the...   \n", "4    Three of the edges of a cube are $\\overline{AB...   \n", "..                                                 ...   \n", "159  A $7\\times 1$ board is completely covered by $...   \n", "160  Circles $\\omega_1$ and $\\omega_2$ intersect at...   \n", "161  The complex numbers $z$ and $w$ satisfy $z^{13...   \n", "162  Four circles $\\omega,$ $\\omega_{A},$ $\\omega_{...   \n", "163  How many positive integer divisors of $2004^{2...   \n", "\n", "                                              solution answer  \n", "0    Alright, let's try to tackle this problem step...    162  \n", "1    Okay, let's tackle this geometry problem. So, ...    450  \n", "2    Okay, so I have this problem here: For some in...     98  \n", "3    Okay, so I need to solve this geometry problem...     86  \n", "4    Okay, so I have this geometry problem here. Le...    525  \n", "..                                                 ...    ...  \n", "159  Okay, let's try to figure out this tiling prob...    106  \n", "160  Okay, so I need to solve this geometry problem...     33  \n", "161  Okay, so I have this problem here about comple...     71  \n", "162  Okay, so I need to find the radius of circle ω...    389  \n", "163  Alright, so I’ve just come across this math pr...     54  \n", "\n", "[164 rows x 4 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["limo_crosslingual_ko_en = load_dataset(\"junkim100/limo_crosslingual_ko_en\")\n", "limo_crosslingual_ko_en['train'].to_pandas()\n", "limo_crosslingual_ko_en['test'].to_pandas()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["32159"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["# max length of data when question translation, solution, and answer are concatenated\n", "max([len(x['question']) + len(x['translation']) + len(x['solution']) + len(x['answer']) for x in limo_crosslingual_ko_en['train']])\n", "\n", "# max token length of data when question translation, solution, and answer are concatenated\n", "tokenizer = AutoTokenizer.from_pretrained(\"meta-llama/Llama-3.1-8B\")\n", "max([len(tokenizer(x['question'] + x['translation'] + x['solution'] + x['answer'])['input_ids']) for x in limo_crosslingual_ko_en['train']])"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}