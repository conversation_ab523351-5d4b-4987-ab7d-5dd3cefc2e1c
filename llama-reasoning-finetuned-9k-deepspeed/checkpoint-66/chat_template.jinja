{%- for message in messages %}
    {%- if message['role'] == 'system' %}
        {{- '<|start_header_id|>system<|end_header_id|>

' + message['content'] + '<|eot_id|>' }}
    {%- elif message['role'] == 'user' %}
        {{- '<|start_header_id|>user<|end_header_id|>

' + message['content'] + '<|eot_id|>' }}
    {%- elif message['role'] == 'assistant' %}
        {{- '<|start_header_id|>assistant<|end_header_id|>

' + message['content'] + '<|eot_id|>' }}
    {%- endif %}
{%- endfor %}
{%- if add_generation_prompt %}
    {%- set last_user_message = messages[-1]['content'] if messages and messages[-1]['role'] == 'user' else '' %}
    {%- set is_translation_request = (
        'translate' in last_user_message.lower() or
        'translation' in last_user_message.lower() or
        'italian' in last_user_message.lower() or
        'convert to italian' in last_user_message.lower() or
        'in italian' in last_user_message.lower() or
        'to italian' in last_user_message.lower()
    ) %}
    {{- '<|start_header_id|>assistant<|end_header_id|>

' }}
    {%- if is_translation_request %}
        {{- '<translation>' }}
    {%- endif %}
{%- endif %}