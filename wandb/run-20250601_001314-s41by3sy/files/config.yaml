_name_or_path:
    value: meta-llama/Meta-Llama-3.1-8B
_wandb:
    value:
        cli_version: 0.19.11
        m:
            - "1": train/global_step
              "6":
                - 3
              "7": []
        python_version: 3.10.0
        t:
            "1":
                - 1
                - 5
                - 11
                - 49
                - 51
                - 53
                - 55
                - 71
                - 98
                - 105
            "2":
                - 1
                - 5
                - 11
                - 49
                - 51
                - 53
                - 55
                - 71
                - 98
                - 105
            "3":
                - 7
                - 13
                - 15
                - 16
                - 19
                - 23
                - 55
                - 66
            "4": 3.10.0
            "5": 0.19.11
            "6": 4.52.4
            "8":
                - 5
                - 9
            "9":
                "1": transformers_trainer
            "12": 0.19.11
            "13": linux-x86_64
accelerator_config:
    value:
        dispatch_batches: null
        even_batches: true
        gradient_accumulation_kwargs: null
        non_blocking: false
        split_batches: false
        use_seedable_sampler: true
adafactor:
    value: false
adam_beta1:
    value: 0.9
adam_beta2:
    value: 0.999
adam_epsilon:
    value: 1e-08
add_cross_attention:
    value: false
architectures:
    value:
        - LlamaForCausalLM
attention_bias:
    value: false
attention_dropout:
    value: 0
auto_find_batch_size:
    value: false
average_tokens_across_devices:
    value: false
bad_words_ids:
    value: null
batch_eval_metrics:
    value: false
begin_suppress_tokens:
    value: null
bf16:
    value: true
bf16_full_eval:
    value: false
bos_token_id:
    value: 128000
chunk_size_feed_forward:
    value: 0
cross_attention_hidden_size:
    value: null
data_seed:
    value: null
dataloader_drop_last:
    value: false
dataloader_num_workers:
    value: 0
dataloader_persistent_workers:
    value: false
dataloader_pin_memory:
    value: false
dataloader_prefetch_factor:
    value: null
dataset_name:
    value: junkim100/Lima-X-Processed
ddp_backend:
    value: null
ddp_broadcast_buffers:
    value: null
ddp_bucket_cap_mb:
    value: null
ddp_find_unused_parameters:
    value: null
ddp_timeout:
    value: 1800
debug:
    value: []
decoder_start_token_id:
    value: null
deepspeed:
    value: deepspeed_config_zero3.json
deepspeed_stage:
    value: 3
disable_tqdm:
    value: false
diversity_penalty:
    value: 0
do_eval:
    value: true
do_predict:
    value: false
do_sample:
    value: false
do_train:
    value: false
early_stopping:
    value: false
encoder_no_repeat_ngram_size:
    value: 0
eos_token_id:
    value: 128001
eval_accumulation_steps:
    value: 1
eval_delay:
    value: 0
eval_do_concat_batches:
    value: true
eval_on_start:
    value: false
eval_steps:
    value: 100
eval_strategy:
    value: steps
eval_use_gather_object:
    value: false
exponential_decay_length_penalty:
    value: null
finetuning_task:
    value: null
forced_bos_token_id:
    value: null
forced_eos_token_id:
    value: null
fp16:
    value: false
fp16_backend:
    value: auto
fp16_full_eval:
    value: false
fp16_opt_level:
    value: O1
fsdp:
    value: []
fsdp_config:
    value:
        min_num_params: 0
        xla: false
        xla_fsdp_grad_ckpt: false
        xla_fsdp_v2: false
fsdp_min_num_params:
    value: 0
fsdp_transformer_layer_cls_to_wrap:
    value: null
full_determinism:
    value: false
gradient_accumulation_steps:
    value: 8
gradient_checkpointing:
    value: false
gradient_checkpointing_kwargs:
    value: null
greater_is_better:
    value: null
group_by_length:
    value: false
half_precision_backend:
    value: auto
head_dim:
    value: 128
hidden_act:
    value: silu
hidden_size:
    value: 4096
hub_always_push:
    value: false
hub_model_id:
    value: null
hub_private_repo:
    value: null
hub_strategy:
    value: every_save
hub_token:
    value: <HUB_TOKEN>
id2label:
    value:
        "0": LABEL_0
        "1": LABEL_1
ignore_data_skip:
    value: false
include_for_metrics:
    value: []
include_inputs_for_metrics:
    value: false
include_num_input_tokens_seen:
    value: false
include_tokens_per_second:
    value: false
initializer_range:
    value: 0.02
intermediate_size:
    value: 14336
is_decoder:
    value: false
is_encoder_decoder:
    value: false
jit_mode_eval:
    value: false
label_names:
    value: null
label_smoothing_factor:
    value: 0
label2id:
    value:
        LABEL_0: 0
        LABEL_1: 1
learning_rate:
    value: 0.0001
length_column_name:
    value: length
length_penalty:
    value: 1
load_best_model_at_end:
    value: false
local_rank:
    value: 0
log_level:
    value: passive
log_level_replica:
    value: warning
log_on_each_node:
    value: true
logging_dir:
    value: ./llama-reasoning-finetuned-32k-deepspeed/runs/Jun01_00-14-05_nlp-server-18
logging_first_step:
    value: true
logging_nan_inf_filter:
    value: true
logging_steps:
    value: 5
logging_strategy:
    value: steps
lr_scheduler_type:
    value: linear
max_grad_norm:
    value: 0.5
max_length:
    value: 20
max_position_embeddings:
    value: 131072
max_steps:
    value: -1
method:
    value: LoRA + DeepSpeed ZeRO-3
metric_for_best_model:
    value: null
min_length:
    value: 0
mlp_bias:
    value: false
model/num_parameters:
    value: 8072220672
model_name:
    value: meta-llama/Meta-Llama-3.1-8B
model_type:
    value: llama
mp_parameters:
    value: ""
neftune_noise_alpha:
    value: null
no_cuda:
    value: false
no_repeat_ngram_size:
    value: 0
num_attention_heads:
    value: 32
num_beam_groups:
    value: 1
num_beams:
    value: 1
num_hidden_layers:
    value: 32
num_key_value_heads:
    value: 8
num_return_sequences:
    value: 1
num_train_epochs:
    value: 2
optim:
    value: adamw_torch
optim_args:
    value: null
optim_target_modules:
    value: null
output_attentions:
    value: false
output_dir:
    value: ./llama-reasoning-finetuned-32k-deepspeed
output_hidden_states:
    value: false
output_scores:
    value: false
overwrite_output_dir:
    value: false
pad_token_id:
    value: null
past_index:
    value: -1
peft_config:
    value:
        default:
            auto_mapping: null
            base_model_name_or_path: meta-llama/Meta-Llama-3.1-8B
            bias: none
            corda_config: null
            eva_config: null
            exclude_modules: null
            fan_in_fan_out: false
            inference_mode: false
            init_lora_weights: true
            layer_replication: null
            layers_pattern: null
            layers_to_transform: null
            lora_alpha: 32
            lora_bias: false
            lora_dropout: 0.05
            megatron_config: null
            megatron_core: megatron.core
            modules_to_save: null
            peft_type: LORA
            r: 16
            revision: null
            runtime_config:
                ephemeral_gpu_offload: false
            target_modules:
                - v_proj
                - q_proj
                - k_proj
                - down_proj
                - o_proj
                - up_proj
                - gate_proj
            task_type: CAUSAL_LM
            trainable_token_indices: null
            use_dora: false
            use_rslora: false
per_device_eval_batch_size:
    value: 8
per_device_train_batch_size:
    value: 1
per_gpu_eval_batch_size:
    value: null
per_gpu_train_batch_size:
    value: null
prediction_loss_only:
    value: false
prefix:
    value: null
pretraining_tp:
    value: 1
problem_type:
    value: null
push_to_hub:
    value: false
push_to_hub_model_id:
    value: null
push_to_hub_organization:
    value: null
push_to_hub_token:
    value: <PUSH_TO_HUB_TOKEN>
ray_scope:
    value: last
reasoning_tokens:
    value: true
remove_invalid_values:
    value: false
remove_unused_columns:
    value: false
repetition_penalty:
    value: 1
report_to:
    value:
        - wandb
restore_callback_states_from_checkpoint:
    value: false
resume_from_checkpoint:
    value: null
return_dict:
    value: true
return_dict_in_generate:
    value: false
rms_norm_eps:
    value: 1e-05
rope_scaling:
    value:
        factor: 8
        high_freq_factor: 4
        low_freq_factor: 1
        original_max_position_embeddings: 8192
        rope_type: llama3
rope_theta:
    value: 500000
run_name:
    value: it-4gpu-deepspeed
save_on_each_node:
    value: false
save_only_model:
    value: false
save_safetensors:
    value: true
save_steps:
    value: 100
save_strategy:
    value: steps
save_total_limit:
    value: 2
seed:
    value: 42
sep_token_id:
    value: null
skip_memory_metrics:
    value: false
suppress_tokens:
    value: null
task_specific_params:
    value: null
temperature:
    value: 1
tf_legacy_loss:
    value: false
tf32:
    value: true
tie_encoder_decoder:
    value: false
tie_word_embeddings:
    value: false
tokenizer_class:
    value: null
top_k:
    value: 50
top_p:
    value: 1
torch_compile:
    value: false
torch_compile_backend:
    value: null
torch_compile_mode:
    value: null
torch_dtype:
    value: bfloat16
torch_empty_cache_steps:
    value: null
torchdynamo:
    value: null
torchscript:
    value: false
tpu_metrics_debug:
    value: false
tpu_num_cores:
    value: null
transformers_version:
    value: 4.52.4
typical_p:
    value: 1
use_bfloat16:
    value: false
use_cache:
    value: false
use_cpu:
    value: false
use_ipex:
    value: false
use_legacy_prediction_loop:
    value: false
use_liger_kernel:
    value: false
use_mps_device:
    value: false
vocab_size:
    value: 128258
warmup_ratio:
    value: 0.05
warmup_steps:
    value: 0
weight_decay:
    value: 0.01
world_size:
    value: 4
