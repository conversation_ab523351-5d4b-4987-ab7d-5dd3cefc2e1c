Starting training on GPU 0/4 with max_length=32159
Process 0: Target device is cuda:0
Process 0: Loading model on device cuda:0
Process 0: Loading model from meta-llama/Meta-Llama-3.1-8B
Loading checkpoint shards: 100%|███████████████████████████████████████████████████████████████████| 4/4 [00:11<00:00,  2.83s/it]
Process 0: Resizing token embeddings from 128256 to 128260
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
Process 0: Moving model to cuda:0
Process 0: Model successfully moved to cuda:0
Enhanced checkpointing: every 8 layers for max 32159 tokens
Process 0: Model loaded on cuda:0
Process 0: Passed model loading barrier
trainable params: 167,772,160 || all params: 8,198,066,176 || trainable%: 2.0465
Process 0: LoRA model is on cuda:0
Process 0: Setting up DDP with device_ids=[0], output_device=0
Process 0: DDP model is on cuda:0
Process 0: Passed DDP setup barrier
=== Distributed GPU Usage Monitor ===
GPU 0 (NVIDIA A100-SXM4-80GB):
  Allocated: 9.20GB / 84.97GB (10.8%)
  Reserved:  17.61GB / 84.97GB (20.7%)
GPU 1 (NVIDIA A100-SXM4-80GB):
  Allocated: 0.00GB / 84.97GB (0.0%)
  Reserved:  0.00GB / 84.97GB (0.0%)
GPU 2 (NVIDIA A100-SXM4-80GB):
  Allocated: 0.00GB / 84.97GB (0.0%)
  Reserved:  0.00GB / 84.97GB (0.0%)
GPU 3 (NVIDIA A100-SXM4-80GB):
  Allocated: 0.00GB / 84.97GB (0.0%)
  Reserved:  0.00GB / 84.97GB (0.0%)
==================================================
Process 0: Preparing dataset...
Dataset after filtering: 735 training samples
Test samples: 41
torch.distributed process group is initialized, but parallel_mode != ParallelMode.DISTRIBUTED. In order to use Torch DDP, launch your script with `python -m torch.distributed.launch
/data_x/junkim100/projects/translation_it/new.py:566: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `Trainer.__init__`. Use `processing_class` instead.
  trainer = Trainer(
=== GPU Memory Before Training ===
GPU 0: Allocated 9.20GB, Reserved 17.61GB
GPU 1: Allocated 0.00GB, Reserved 0.00GB
GPU 2: Allocated 0.00GB, Reserved 0.00GB
GPU 3: Allocated 0.00GB, Reserved 0.00GB
Process 0: Passed pre-training barrier
Starting enhanced training with proper GPU distribution...
Each process should use ~23% memory on its assigned GPU
  0%|                                                                                                     | 0/36 [00:00<?, ?it/s]`use_cache=True` is incompatible with gradient checkpointing. Setting `use_cache=False`.
/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/torch/_dynamo/eval_frame.py:838: UserWarning: torch.utils.checkpoint: the use_reentrant parameter should be passed explicitly. In version 2.5 we will raise an exception if use_reentrant is not passed. use_reentrant=False is recommended, but if you need to preserve the current default behavior, you can pass use_reentrant=True. Refer to docs for more details on the differences between the two variants.
  return fn(*args, **kwargs)
Process 0: Error during training: CUDA out of memory. Tried to allocate 3.78 GiB. GPU 0 has a total capacity of 79.14 GiB of which 3.67 GiB is free. Process 3326718 has 414.00 MiB memory in use. Process 3326877 has 8.46 GiB memory in use. Process 3326876 has 8.46 GiB memory in use. Process 3326878 has 8.46 GiB memory in use. Including non-PyTorch memory, this process has 49.64 GiB memory in use. Of the allocated memory 48.87 GiB is allocated by PyTorch, and 32.62 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
Process 0: Exception type: OutOfMemoryError
Traceback (most recent call last):
  File "/data_x/junkim100/projects/translation_it/new.py", line 597, in train_model
    trainer.train()
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/transformers/trainer.py", line 2240, in train
    return inner_training_loop(
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/transformers/trainer.py", line 2555, in _inner_training_loop
    tr_loss_step = self.training_step(model, inputs, num_items_in_batch)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/transformers/trainer.py", line 3745, in training_step
    loss = self.compute_loss(model, inputs, num_items_in_batch=num_items_in_batch)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/transformers/trainer.py", line 3810, in compute_loss
    outputs = model(**inputs)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/accelerate/utils/operations.py", line 818, in forward
    return model_forward(*args, **kwargs)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/accelerate/utils/operations.py", line 806, in __call__
    return convert_to_fp32(self.model_forward(*args, **kwargs))
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/torch/amp/autocast_mode.py", line 44, in decorate_autocast
    return func(*args, **kwargs)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/torch/nn/parallel/distributed.py", line 1637, in forward
    else self._run_ddp_forward(*inputs, **kwargs)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/torch/nn/parallel/distributed.py", line 1464, in _run_ddp_forward
    return self.module(*inputs, **kwargs)  # type: ignore[index]
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/peft/peft_model.py", line 1757, in forward
    return self.base_model(
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/peft/tuners/tuners_utils.py", line 193, in forward
    return self.model.forward(*args, **kwargs)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/transformers/utils/generic.py", line 969, in wrapper
    output = func(self, *args, **kwargs)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/transformers/models/llama/modeling_llama.py", line 688, in forward
    outputs: BaseModelOutputWithPast = self.model(
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/transformers/utils/generic.py", line 969, in wrapper
    output = func(self, *args, **kwargs)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/transformers/models/llama/modeling_llama.py", line 453, in forward
    layer_outputs = decoder_layer(
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/transformers/modeling_layers.py", line 47, in __call__
    return self._gradient_checkpointing_func(partial(super().__call__, **kwargs), *args)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/torch/_compile.py", line 51, in inner
    return disable_fn(*args, **kwargs)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/torch/_dynamo/eval_frame.py", line 838, in _fn
    return fn(*args, **kwargs)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/torch/utils/checkpoint.py", line 488, in checkpoint
    return CheckpointFunction.apply(function, preserve, *args)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/torch/autograd/function.py", line 575, in apply
    return super().apply(*args, **kwargs)  # type: ignore[misc]
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/torch/utils/checkpoint.py", line 263, in forward
    outputs = run_function(*args)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/transformers/models/llama/modeling_llama.py", line 324, in forward
    hidden_states = self.mlp(hidden_states)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/transformers/models/llama/modeling_llama.py", line 162, in forward
    down_proj = self.down_proj(self.act_fn(self.gate_proj(x)) * self.up_proj(x))
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/peft/tuners/lora/bnb.py", line 534, in forward
    result = result + output
torch.OutOfMemoryError: CUDA out of memory. Tried to allocate 3.78 GiB. GPU 0 has a total capacity of 79.14 GiB of which 3.67 GiB is free. Process 3326718 has 414.00 MiB memory in use. Process 3326877 has 8.46 GiB memory in use. Process 3326876 has 8.46 GiB memory in use. Process 3326878 has 8.46 GiB memory in use. Including non-PyTorch memory, this process has 49.64 GiB memory in use. Of the allocated memory 48.87 GiB is allocated by PyTorch, and 32.62 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
