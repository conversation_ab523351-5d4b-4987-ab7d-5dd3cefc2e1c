2025-06-01 01:03:51,366 INFO    MainThread:3531816 [wandb_setup.py:_flush():70] Current SDK version is 0.19.11
2025-06-01 01:03:51,366 INFO    MainThread:3531816 [wandb_setup.py:_flush():70] Configure stats pid to 3531816
2025-06-01 01:03:51,366 INFO    MainThread:3531816 [wandb_setup.py:_flush():70] Loading settings from /mnt/raid6/junkim100/.config/wandb/settings
2025-06-01 01:03:51,366 INFO    MainThread:3531816 [wandb_setup.py:_flush():70] Loading settings from /data_x/junkim100/projects/translation_it/wandb/settings
2025-06-01 01:03:51,366 INFO    MainThread:3531816 [wandb_setup.py:_flush():70] Loading settings from environment variables
2025-06-01 01:03:51,366 INFO    MainThread:3531816 [wandb_init.py:setup_run_log_directory():724] Logging user logs to /data_x/junkim100/projects/translation_it/wandb/run-20250601_010351-s5gqxn2j/logs/debug.log
2025-06-01 01:03:51,367 INFO    MainThread:3531816 [wandb_init.py:setup_run_log_directory():725] Logging internal logs to /data_x/junkim100/projects/translation_it/wandb/run-20250601_010351-s5gqxn2j/logs/debug-internal.log
2025-06-01 01:03:51,367 INFO    MainThread:3531816 [wandb_init.py:init():852] calling init triggers
2025-06-01 01:03:51,367 INFO    MainThread:3531816 [wandb_init.py:init():857] wandb.init called with sweep_config: {}
config: {'model_name': 'meta-llama/Meta-Llama-3.1-8B', 'dataset_name': 'junkim100/Lima-X-Processed', 'world_size': 4, 'max_length': 9064, 'method': 'LoRA + DeepSpeed ZeRO-3', 'reasoning_tokens': True, 'deepspeed_stage': 3, '_wandb': {}}
2025-06-01 01:03:51,367 INFO    MainThread:3531816 [wandb_init.py:init():893] starting backend
2025-06-01 01:03:51,367 INFO    MainThread:3531816 [wandb_init.py:init():897] sending inform_init request
2025-06-01 01:03:51,368 INFO    MainThread:3531816 [backend.py:_multiprocessing_setup():101] multiprocessing start_methods=fork,spawn,forkserver, using: spawn
2025-06-01 01:03:51,368 INFO    MainThread:3531816 [wandb_init.py:init():907] backend started and connected
2025-06-01 01:03:51,369 INFO    MainThread:3531816 [wandb_init.py:init():1005] updated telemetry
2025-06-01 01:03:51,369 INFO    MainThread:3531816 [wandb_init.py:init():1029] communicating run to backend with 90.0 second timeout
2025-06-01 01:03:52,025 INFO    MainThread:3531816 [wandb_init.py:init():1104] starting run threads in backend
