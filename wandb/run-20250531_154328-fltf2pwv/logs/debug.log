2025-05-31 15:43:29,010 INFO    MainThread:3332271 [wandb_setup.py:_flush():70] Current SDK version is 0.19.11
2025-05-31 15:43:29,010 INFO    MainThread:3332271 [wandb_setup.py:_flush():70] Configure stats pid to 3332271
2025-05-31 15:43:29,010 INFO    MainThread:3332271 [wandb_setup.py:_flush():70] Loading settings from /mnt/raid6/junkim100/.config/wandb/settings
2025-05-31 15:43:29,010 INFO    MainThread:3332271 [wandb_setup.py:_flush():70] Loading settings from /data_x/junkim100/projects/translation_it/wandb/settings
2025-05-31 15:43:29,010 INFO    MainThread:3332271 [wandb_setup.py:_flush():70] Loading settings from environment variables
2025-05-31 15:43:29,010 INFO    MainThread:3332271 [wandb_init.py:setup_run_log_directory():724] Logging user logs to /data_x/junkim100/projects/translation_it/wandb/run-20250531_154328-fltf2pwv/logs/debug.log
2025-05-31 15:43:29,010 INFO    MainThread:3332271 [wandb_init.py:setup_run_log_directory():725] Logging internal logs to /data_x/junkim100/projects/translation_it/wandb/run-20250531_154328-fltf2pwv/logs/debug-internal.log
2025-05-31 15:43:29,010 INFO    MainThread:3332271 [wandb_init.py:init():852] calling init triggers
2025-05-31 15:43:29,010 INFO    MainThread:3332271 [wandb_init.py:init():857] wandb.init called with sweep_config: {}
config: {'model_name': 'meta-llama/Meta-Llama-3.1-8B', 'dataset_name': 'junkim100/limo_crosslingual_ko_en', 'world_size': 4, 'max_length': 32159, 'method': 'LoRA', 'reasoning_tokens': True, 'optimization': 'long-sequence-optimized', '_wandb': {}}
2025-05-31 15:43:29,010 INFO    MainThread:3332271 [wandb_init.py:init():893] starting backend
2025-05-31 15:43:29,010 INFO    MainThread:3332271 [wandb_init.py:init():897] sending inform_init request
2025-05-31 15:43:29,016 INFO    MainThread:3332271 [backend.py:_multiprocessing_setup():101] multiprocessing start_methods=fork,spawn,forkserver, using: spawn
2025-05-31 15:43:29,016 INFO    MainThread:3332271 [wandb_init.py:init():907] backend started and connected
2025-05-31 15:43:29,019 INFO    MainThread:3332271 [wandb_init.py:init():1005] updated telemetry
2025-05-31 15:43:29,020 INFO    MainThread:3332271 [wandb_init.py:init():1029] communicating run to backend with 90.0 second timeout
2025-05-31 15:43:29,713 INFO    MainThread:3332271 [wandb_init.py:init():1104] starting run threads in backend
2025-05-31 15:43:29,846 INFO    MainThread:3332271 [wandb_run.py:_console_start():2573] atexit reg
2025-05-31 15:43:29,847 INFO    MainThread:3332271 [wandb_run.py:_redirect():2421] redirect: wrap_raw
2025-05-31 15:43:29,847 INFO    MainThread:3332271 [wandb_run.py:_redirect():2490] Wrapping output streams.
2025-05-31 15:43:29,847 INFO    MainThread:3332271 [wandb_run.py:_redirect():2513] Redirects installed.
2025-05-31 15:43:29,848 INFO    MainThread:3332271 [wandb_init.py:init():1150] run started, returning control to user process
2025-05-31 15:44:21,857 INFO    MsgRouterThr:3332271 [mailbox.py:close():129] [no run ID] Closing mailbox, abandoning 1 handles.
