2025-05-31 01:07:09,406 INFO    MainThread:3196675 [wandb_setup.py:_flush():70] Current SDK version is 0.19.11
2025-05-31 01:07:09,406 INFO    MainThread:3196675 [wandb_setup.py:_flush():70] Configure stats pid to 3196675
2025-05-31 01:07:09,406 INFO    MainThread:3196675 [wandb_setup.py:_flush():70] Loading settings from /mnt/raid6/junkim100/.config/wandb/settings
2025-05-31 01:07:09,406 INFO    MainThread:3196675 [wandb_setup.py:_flush():70] Loading settings from /data_x/junkim100/projects/translation_it/wandb/settings
2025-05-31 01:07:09,406 INFO    MainThread:3196675 [wandb_setup.py:_flush():70] Loading settings from environment variables
2025-05-31 01:07:09,407 INFO    MainThread:3196675 [wandb_init.py:setup_run_log_directory():724] Logging user logs to /data_x/junkim100/projects/translation_it/wandb/run-20250531_010709-ylkphuti/logs/debug.log
2025-05-31 01:07:09,407 INFO    MainThread:3196675 [wandb_init.py:setup_run_log_directory():725] Logging internal logs to /data_x/junkim100/projects/translation_it/wandb/run-20250531_010709-ylkphuti/logs/debug-internal.log
2025-05-31 01:07:09,407 INFO    MainThread:3196675 [wandb_init.py:init():852] calling init triggers
2025-05-31 01:07:09,407 INFO    MainThread:3196675 [wandb_init.py:init():857] wandb.init called with sweep_config: {}
config: {'model_name': 'meta-llama/Meta-Llama-3.1-8B', 'dataset_name': 'junkim100/limo_crosslingual_ko_en', 'world_size': 3, 'method': 'LoRA', 'reasoning_tokens': True, 'max_length': 32159, '_wandb': {}}
2025-05-31 01:07:09,407 INFO    MainThread:3196675 [wandb_init.py:init():893] starting backend
2025-05-31 01:07:09,407 INFO    MainThread:3196675 [wandb_init.py:init():897] sending inform_init request
2025-05-31 01:07:09,412 INFO    MainThread:3196675 [backend.py:_multiprocessing_setup():101] multiprocessing start_methods=fork,spawn,forkserver, using: spawn
2025-05-31 01:07:09,412 INFO    MainThread:3196675 [wandb_init.py:init():907] backend started and connected
2025-05-31 01:07:09,414 INFO    MainThread:3196675 [wandb_init.py:init():1005] updated telemetry
2025-05-31 01:07:09,414 INFO    MainThread:3196675 [wandb_init.py:init():1029] communicating run to backend with 90.0 second timeout
2025-05-31 01:07:10,247 INFO    MainThread:3196675 [wandb_init.py:init():1104] starting run threads in backend
2025-05-31 01:07:10,380 INFO    MainThread:3196675 [wandb_run.py:_console_start():2573] atexit reg
2025-05-31 01:07:10,381 INFO    MainThread:3196675 [wandb_run.py:_redirect():2421] redirect: wrap_raw
2025-05-31 01:07:10,381 INFO    MainThread:3196675 [wandb_run.py:_redirect():2490] Wrapping output streams.
2025-05-31 01:07:10,381 INFO    MainThread:3196675 [wandb_run.py:_redirect():2513] Redirects installed.
2025-05-31 01:07:10,383 INFO    MainThread:3196675 [wandb_init.py:init():1150] run started, returning control to user process
2025-05-31 01:07:38,192 INFO    MainThread:3196675 [wandb_run.py:_config_callback():1436] config_cb None None {'output_dir': './llama-reasoning-finetuned-enhanced', 'overwrite_output_dir': False, 'do_train': False, 'do_eval': True, 'do_predict': False, 'eval_strategy': 'steps', 'prediction_loss_only': False, 'per_device_train_batch_size': 1, 'per_device_eval_batch_size': 8, 'per_gpu_train_batch_size': None, 'per_gpu_eval_batch_size': None, 'gradient_accumulation_steps': 16, 'eval_accumulation_steps': None, 'eval_delay': 0, 'torch_empty_cache_steps': None, 'learning_rate': 0.0001, 'weight_decay': 0.0, 'adam_beta1': 0.9, 'adam_beta2': 0.999, 'adam_epsilon': 1e-08, 'max_grad_norm': 1.0, 'num_train_epochs': 3, 'max_steps': -1, 'lr_scheduler_type': 'linear', 'lr_scheduler_kwargs': {}, 'warmup_ratio': 0.1, 'warmup_steps': 0, 'log_level': 'passive', 'log_level_replica': 'warning', 'log_on_each_node': True, 'logging_dir': './llama-reasoning-finetuned-enhanced/runs/May31_01-07-37_nlp-server-18', 'logging_strategy': 'steps', 'logging_first_step': False, 'logging_steps': 10, 'logging_nan_inf_filter': True, 'save_strategy': 'steps', 'save_steps': 500, 'save_total_limit': 3, 'save_safetensors': True, 'save_on_each_node': False, 'save_only_model': False, 'restore_callback_states_from_checkpoint': False, 'no_cuda': False, 'use_cpu': False, 'use_mps_device': False, 'seed': 42, 'data_seed': None, 'jit_mode_eval': False, 'use_ipex': False, 'bf16': True, 'fp16': False, 'fp16_opt_level': 'O1', 'half_precision_backend': 'auto', 'bf16_full_eval': False, 'fp16_full_eval': False, 'tf32': None, 'local_rank': 0, 'ddp_backend': None, 'tpu_num_cores': None, 'tpu_metrics_debug': False, 'debug': [], 'dataloader_drop_last': True, 'eval_steps': 500, 'dataloader_num_workers': 0, 'dataloader_prefetch_factor': None, 'past_index': -1, 'run_name': 'enhanced-32159', 'disable_tqdm': False, 'remove_unused_columns': False, 'label_names': None, 'load_best_model_at_end': True, 'metric_for_best_model': 'eval_loss', 'greater_is_better': False, 'ignore_data_skip': False, 'fsdp': [], 'fsdp_min_num_params': 0, 'fsdp_config': {'min_num_params': 0, 'xla': False, 'xla_fsdp_v2': False, 'xla_fsdp_grad_ckpt': False}, 'fsdp_transformer_layer_cls_to_wrap': None, 'accelerator_config': {'split_batches': False, 'dispatch_batches': None, 'even_batches': True, 'use_seedable_sampler': True, 'non_blocking': False, 'gradient_accumulation_kwargs': None}, 'deepspeed': None, 'label_smoothing_factor': 0.0, 'optim': 'adamw_torch', 'optim_args': None, 'adafactor': False, 'group_by_length': False, 'length_column_name': 'length', 'report_to': ['wandb'], 'ddp_find_unused_parameters': True, 'ddp_bucket_cap_mb': None, 'ddp_broadcast_buffers': None, 'dataloader_pin_memory': False, 'dataloader_persistent_workers': False, 'skip_memory_metrics': True, 'use_legacy_prediction_loop': False, 'push_to_hub': False, 'resume_from_checkpoint': None, 'hub_model_id': None, 'hub_strategy': 'every_save', 'hub_token': '<HUB_TOKEN>', 'hub_private_repo': None, 'hub_always_push': False, 'gradient_checkpointing': False, 'gradient_checkpointing_kwargs': None, 'include_inputs_for_metrics': False, 'include_for_metrics': [], 'eval_do_concat_batches': True, 'fp16_backend': 'auto', 'push_to_hub_model_id': None, 'push_to_hub_organization': None, 'push_to_hub_token': '<PUSH_TO_HUB_TOKEN>', 'mp_parameters': '', 'auto_find_batch_size': False, 'full_determinism': False, 'torchdynamo': None, 'ray_scope': 'last', 'ddp_timeout': 1800, 'torch_compile': False, 'torch_compile_backend': None, 'torch_compile_mode': None, 'include_tokens_per_second': False, 'include_num_input_tokens_seen': False, 'neftune_noise_alpha': None, 'optim_target_modules': None, 'batch_eval_metrics': False, 'eval_on_start': False, 'use_liger_kernel': False, 'eval_use_gather_object': False, 'average_tokens_across_devices': False}
2025-05-31 01:07:55,414 INFO    MsgRouterThr:3196675 [mailbox.py:close():129] [no run ID] Closing mailbox, abandoning 2 handles.
