Starting training on GPU 0/3 with max_length=32159
Loading checkpoint shards: 100%|███████████████████████████████████████████████████████████████████| 4/4 [00:15<00:00,  3.83s/it]
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
Enhanced checkpointing: every 8 layers for max 32159 tokens
trainable params: 167,772,160 || all params: 8,198,066,176 || trainable%: 2.0465
Map: 100%|███████████████████████████████████████████████████████████████████████████████| 41/41 [00:00<00:00, 278.02 examples/s]
Filter: 100%|████████████████████████████████████████████████████████████████████████████| 41/41 [00:00<00:00, 197.63 examples/s]
Dataset after filtering: 735 training samples
Test samples: 41
=== Dataset Debug Info ===

Sample 0:
  input_ids type: <class 'list'>
  input_ids length: 11013
  labels type: <class 'list'>
  labels length: 11013
  input_ids all ints: True
  labels all ints: True
  decoded start: <|start_header_id|>user<|end_header_id|>

특별한 카드 한 벌에는 $49$장의 카드가 들어 있는데, 각 카드에는 $1$부터 $7$까지의 숫자가 적혀...

Sample 1:
  input_ids type: <class 'list'>
  input_ids length: 4174
  labels type: <class 'list'>
  labels length: 4174
  input_ids all ints: True
  labels all ints: True
  decoded start: <|start_header_id|>user<|end_header_id|>

2019^8 + 1의 최소 홀수 제곱근을 구하세요.<|eot_id|><|start_header_id|>a...

Sample 2:
  input_ids type: <class 'list'>
  input_ids length: 13892
  labels type: <class 'list'>
  labels length: 13892
  input_ids all ints: True
  labels all ints: True
  decoded start: <|start_header_id|>user<|end_header_id|>

삼각형 $ABC$는 $AC = 7,$ $BC = 24,$ 그리고 $C$에서 직각인 직각삼각형입니다. 점 ...
torch.distributed process group is initialized, but parallel_mode != ParallelMode.DISTRIBUTED. In order to use Torch DDP, launch your script with `python -m torch.distributed.launch
/data_x/junkim100/projects/translation_it/new.py:493: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `Trainer.__init__`. Use `processing_class` instead.
  trainer = Trainer(
Starting enhanced training with long sequence support...
Max sequence length: 32159
Batch size: 1
Gradient accumulation: 16
Effective batch size: 48
  0%|                                                                                                     | 0/48 [00:00<?, ?it/s]`use_cache=True` is incompatible with gradient checkpointing. Setting `use_cache=False`.
/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/torch/_dynamo/eval_frame.py:838: UserWarning: torch.utils.checkpoint: the use_reentrant parameter should be passed explicitly. In version 2.5 we will raise an exception if use_reentrant is not passed. use_reentrant=False is recommended, but if you need to preserve the current default behavior, you can pass use_reentrant=True. Refer to docs for more details on the differences between the two variants.
  return fn(*args, **kwargs)
  0%|                                                                                                     | 0/48 [00:16<?, ?it/s]
