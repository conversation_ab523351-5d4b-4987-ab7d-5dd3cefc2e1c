Process 0: Loading model and tokenizer for max_length=9064
Process 0: DeepSpeed mode - quantization disabled for compatibility
Process 0: Tokenizer loaded with vocab size: 128258
Process 0: Loading model from meta-llama/Meta-Llama-3.1-8B
Loading checkpoint shards: 100%|██████████████████████████████████████████████████████████████████| 4/4 [00:00<00:00, 131.88it/s]
Process 0: Resizing token embeddings from 128256 to 128258
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
Process 0: Model loaded on CPU, DeepSpeed will handle device placement
Process 0: Passed model loading barrier

=== GPU Memory Usage After Model Loading ===
GPU 0: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 1: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 2: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 3: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
==================================================
Process 0: Setting up LoRA for max_length=9064
Process 0: Using LoRA target modules: ['q_proj', 'k_proj', 'v_proj', 'o_proj', 'gate_proj', 'up_proj', 'down_proj']
Process 0: LoRA setup complete, DeepSpeed will handle gradient checkpointing
trainable params: 10,485,760 || all params: 8,040,763,392 || trainable%: 0.1304
Process 0: LoRA model prepared for DeepSpeed initialization
Process 0: Passed LoRA setup barrier
Process 0: Preparing dataset junkim100/Lima-X-Processed with max_length=9064
Process 0: Dataset loaded - Train: 1030, Val: 52
Dataset filtering results:
  Train: 1030 -> 1030 (100.0% retained)
  Val: 52 -> 52 (100.0% retained)
  Sequence lengths - Min: 76, Max: 9064, Mean: 1516.7
Process 0: Dataset prepared and synchronized
[2025-06-01 01:11:10,354] [INFO] [comm.py:669:init_distributed] cdb=None
🔧 ChunkedSequenceDataCollator initialized:
   Max sequence length: 9064
   Chunk size: 4096
   Overlap: 512
   Effective chunks per 9k sequence: 3

=== GPU Memory Usage Before Trainer Init ===
GPU 0: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 1: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 2: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 3: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
==================================================
/data_x/junkim100/projects/translation_it/train.py:648: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `Trainer.__init__`. Use `processing_class` instead.
  trainer = Trainer(
No label_names provided for model class `PeftModelForCausalLM`. Since `PeftModel` hides base models input arguments, if label_names is not given, label_names can't be set automatically within `Trainer`. Note that empty label_names list will be used instead.

=== GPU Memory Usage After DeepSpeed Init ===
GPU 0: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 1: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 2: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 3: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
==================================================
🚀 Starting DeepSpeed training with sequence chunking on 4 GPUs...
   - Max sequence length: 9064 (chunked into 4k pieces)
   - Chunk size: 4096 tokens with 512 token overlap
   - DeepSpeed ZeRO Stage: 3 (Maximum Memory Efficiency)
   - Batch size per device: 1
   - Gradient accumulation steps: 8
   - Base effective batch size: 32
   - Note: Actual batch size will be ~3x higher due to chunking
Installed CUDA version 12.4 does not match the version torch was compiled with 12.6 but since the APIs are compatible, accepting this combination
Using /mnt/raid6/junkim100/.cache/torch_extensions/py310_cu126 as PyTorch extensions root...
Loading extension module cpu_adam...
Time to load cpu_adam op: 2.4553565979003906 seconds
Parameter Offload: Total persistent parameters: 5246976 in 417 params
[2025-06-01 01:11:35,886] [WARNING] [lr_schedules.py:683:get_lr] Attempting to get learning rate from scheduler before it has started
100%|████████████████████████████████████████████████████████████████████████████████████████████| 66/66 [27:24<00:00, 18.13s/it]/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/peft/utils/save_and_load.py:250: UserWarning: Setting `save_embedding_layers` to `True` as the embedding layer has been resized during finetuning.
{'loss': 4.3155, 'grad_norm': 6.966835021972656, 'learning_rate': 0.0, 'epoch': 0.03}
{'loss': 4.2721, 'grad_norm': 7.726235866546631, 'learning_rate': 0.0001, 'epoch': 0.16}
{'loss': 3.3049, 'grad_norm': 4.191048622131348, 'learning_rate': 0.0001, 'epoch': 0.31}
{'loss': 2.1973, 'grad_norm': 1.9786843061447144, 'learning_rate': 0.0001, 'epoch': 0.47}
{'loss': 1.7983, 'grad_norm': 1.2931272983551025, 'learning_rate': 0.0001, 'epoch': 0.62}
{'loss': 1.6576, 'grad_norm': 0.9273611903190613, 'learning_rate': 0.0001, 'epoch': 0.78}
[2025-06-01 01:22:43,633] [WARNING] [stage3.py:2148:step] 1 pytorch allocator cache flushes since last step. this happens when there is high memory pressure and is detrimental to performance. if this is happening frequently consider adjusting settings to reduce memory consumption. If you are unable to make the cache flushes go away consider adding get_accelerator().empty_cache() calls in your training loop to ensure that all ranks flush their caches at the same time
{'loss': 1.5087, 'grad_norm': 0.5237050652503967, 'learning_rate': 0.0001, 'epoch': 0.93}
{'loss': 1.4194, 'grad_norm': 0.601636528968811, 'learning_rate': 0.0001, 'epoch': 1.06}
{'loss': 1.3703, 'grad_norm': 0.4655713140964508, 'learning_rate': 0.0001, 'epoch': 1.22}
{'loss': 1.298, 'grad_norm': 0.4297325909137726, 'learning_rate': 0.0001, 'epoch': 1.37}
{'loss': 1.2369, 'grad_norm': 0.5946605205535889, 'learning_rate': 0.0001, 'epoch': 1.53}
{'loss': 1.2296, 'grad_norm': 0.38704314827919006, 'learning_rate': 0.0001, 'epoch': 1.68}
{'loss': 1.1536, 'grad_norm': 0.3129532039165497, 'learning_rate': 0.0001, 'epoch': 1.84}
{'loss': 1.1865, 'grad_norm': 0.4262920916080475, 'learning_rate': 0.0001, 'epoch': 1.99}
  warnings.warn(
100%|████████████████████████████████████████████████████████████████████████████████████████████| 66/66 [27:59<00:00, 25.45s/it]
{'train_runtime': 1678.4859, 'train_samples_per_second': 1.227, 'train_steps_per_second': 0.039, 'train_loss': 1.811251127358639, 'init_mem_cpu_alloc_delta': 0, 'init_mem_gpu_alloc_delta': 0, 'init_mem_cpu_peaked_delta': 0, 'init_mem_gpu_peaked_delta': 0, 'train_mem_cpu_alloc_delta': 2237652992, 'train_mem_gpu_alloc_delta': 61125632, 'train_mem_cpu_peaked_delta': 16121659392, 'train_mem_gpu_peaked_delta': 57475871232, 'before_init_mem_cpu': 6058213376, 'before_init_mem_gpu': 512, 'epoch': 2.0}

=== GPU Memory Usage After Training Complete ===
GPU 0: 0.06GB/79.14GB (0.1%) - Free: 79.08GB
GPU 1: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 2: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 3: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
==================================================
✅ DeepSpeed training completed in 0.47 hours!
