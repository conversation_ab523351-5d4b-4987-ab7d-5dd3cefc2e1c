Starting training on GPU 0/4 with max_length=32159
Loading checkpoint shards: 100%|███████████████████████████████████████████████████████████████████| 4/4 [00:12<00:00,  3.05s/it]
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
Enhanced checkpointing: every 8 layers for max 32159 tokens
Process 0: Moving model to cuda:0
Process 0: Model is now on cuda:0
trainable params: 167,772,160 || all params: 8,198,066,176 || trainable%: 2.0465
Process 0: LoRA model is on cuda:0
Process 0: DDP model is on cuda:0
=== Distributed GPU Usage Monitor ===
GPU 0 (NVIDIA A100-SXM4-80GB):
  Allocated: 9.20GB / 84.97GB (10.8%)
  Reserved:  27.28GB / 84.97GB (32.1%)
GPU 1 (NVIDIA A100-SXM4-80GB):
  Allocated: 0.00GB / 84.97GB (0.0%)
  Reserved:  0.00GB / 84.97GB (0.0%)
GPU 2 (NVIDIA A100-SXM4-80GB):
  Allocated: 0.00GB / 84.97GB (0.0%)
  Reserved:  0.00GB / 84.97GB (0.0%)
GPU 3 (NVIDIA A100-SXM4-80GB):
  Allocated: 0.00GB / 84.97GB (0.0%)
  Reserved:  0.00GB / 84.97GB (0.0%)
==================================================
Dataset after filtering: 735 training samples
Test samples: 41
torch.distributed process group is initialized, but parallel_mode != ParallelMode.DISTRIBUTED. In order to use Torch DDP, launch your script with `python -m torch.distributed.launch
/data_x/junkim100/projects/translation_it/new.py:504: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `Trainer.__init__`. Use `processing_class` instead.
  trainer = Trainer(
=== GPU Memory Before Training ===
GPU 0: Allocated 9.20GB, Reserved 27.28GB
GPU 1: Allocated 0.00GB, Reserved 0.00GB
GPU 2: Allocated 0.00GB, Reserved 0.00GB
GPU 3: Allocated 0.00GB, Reserved 0.00GB
Starting enhanced training with proper GPU distribution...
Each process should use ~23% memory on its assigned GPU
  0%|                                                                                                     | 0/36 [00:00<?, ?it/s]`use_cache=True` is incompatible with gradient checkpointing. Setting `use_cache=False`.
/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/torch/_dynamo/eval_frame.py:838: UserWarning: torch.utils.checkpoint: the use_reentrant parameter should be passed explicitly. In version 2.5 we will raise an exception if use_reentrant is not passed. use_reentrant=False is recommended, but if you need to preserve the current default behavior, you can pass use_reentrant=True. Refer to docs for more details on the differences between the two variants.
  return fn(*args, **kwargs)
