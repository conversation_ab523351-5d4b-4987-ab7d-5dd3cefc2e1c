_wandb:
    value:
        cli_version: 0.19.11
        m:
            - "1": train/global_step
              "6":
                - 3
              "7": []
        python_version: 3.10.0
        t:
            "1":
                - 1
                - 5
                - 11
                - 49
                - 51
                - 53
                - 55
                - 71
                - 98
                - 105
            "2":
                - 1
                - 5
                - 11
                - 49
                - 51
                - 53
                - 55
                - 71
                - 98
                - 105
            "3":
                - 7
                - 13
                - 15
                - 16
                - 23
                - 55
                - 66
            "4": 3.10.0
            "5": 0.19.11
            "6": 4.52.4
            "8":
                - 5
                - 9
            "9":
                "1": transformers_trainer
            "12": 0.19.11
            "13": linux-x86_64
accelerator_config:
    value:
        dispatch_batches: null
        even_batches: true
        gradient_accumulation_kwargs: null
        non_blocking: false
        split_batches: false
        use_seedable_sampler: true
adafactor:
    value: false
adam_beta1:
    value: 0.9
adam_beta2:
    value: 0.999
adam_epsilon:
    value: 1e-08
auto_find_batch_size:
    value: false
average_tokens_across_devices:
    value: false
batch_eval_metrics:
    value: false
bf16:
    value: true
bf16_full_eval:
    value: false
data_seed:
    value: null
dataloader_drop_last:
    value: true
dataloader_num_workers:
    value: 0
dataloader_persistent_workers:
    value: false
dataloader_pin_memory:
    value: false
dataloader_prefetch_factor:
    value: null
dataset_name:
    value: junkim100/limo_crosslingual_ko_en
ddp_backend:
    value: null
ddp_broadcast_buffers:
    value: null
ddp_bucket_cap_mb:
    value: null
ddp_find_unused_parameters:
    value: true
ddp_timeout:
    value: 1800
debug:
    value: []
deepspeed:
    value: null
disable_tqdm:
    value: false
do_eval:
    value: true
do_predict:
    value: false
do_train:
    value: false
eval_accumulation_steps:
    value: null
eval_delay:
    value: 0
eval_do_concat_batches:
    value: true
eval_on_start:
    value: false
eval_steps:
    value: 500
eval_strategy:
    value: steps
eval_use_gather_object:
    value: false
fp16:
    value: false
fp16_backend:
    value: auto
fp16_full_eval:
    value: false
fp16_opt_level:
    value: O1
fsdp:
    value: []
fsdp_config:
    value:
        min_num_params: 0
        xla: false
        xla_fsdp_grad_ckpt: false
        xla_fsdp_v2: false
fsdp_min_num_params:
    value: 0
fsdp_transformer_layer_cls_to_wrap:
    value: null
full_determinism:
    value: false
gradient_accumulation_steps:
    value: 16
gradient_checkpointing:
    value: false
gradient_checkpointing_kwargs:
    value: null
greater_is_better:
    value: false
group_by_length:
    value: false
half_precision_backend:
    value: auto
hub_always_push:
    value: false
hub_model_id:
    value: null
hub_private_repo:
    value: null
hub_strategy:
    value: every_save
hub_token:
    value: <HUB_TOKEN>
ignore_data_skip:
    value: false
include_for_metrics:
    value: []
include_inputs_for_metrics:
    value: false
include_num_input_tokens_seen:
    value: false
include_tokens_per_second:
    value: false
jit_mode_eval:
    value: false
label_names:
    value: null
label_smoothing_factor:
    value: 0
learning_rate:
    value: 0.0001
length_column_name:
    value: length
load_best_model_at_end:
    value: true
local_rank:
    value: 0
log_level:
    value: passive
log_level_replica:
    value: warning
log_on_each_node:
    value: true
logging_dir:
    value: ./llama-reasoning-finetuned-enhanced/runs/May31_12-26-39_nlp-server-18
logging_first_step:
    value: false
logging_nan_inf_filter:
    value: true
logging_steps:
    value: 10
logging_strategy:
    value: steps
lr_scheduler_type:
    value: linear
max_grad_norm:
    value: 1
max_length:
    value: 32159
max_steps:
    value: -1
method:
    value: LoRA
metric_for_best_model:
    value: eval_loss
model_name:
    value: meta-llama/Meta-Llama-3.1-8B
mp_parameters:
    value: ""
neftune_noise_alpha:
    value: null
no_cuda:
    value: false
num_train_epochs:
    value: 3
optim:
    value: adamw_torch
optim_args:
    value: null
optim_target_modules:
    value: null
output_dir:
    value: ./llama-reasoning-finetuned-enhanced
overwrite_output_dir:
    value: false
past_index:
    value: -1
per_device_eval_batch_size:
    value: 8
per_device_train_batch_size:
    value: 1
per_gpu_eval_batch_size:
    value: null
per_gpu_train_batch_size:
    value: null
prediction_loss_only:
    value: false
push_to_hub:
    value: false
push_to_hub_model_id:
    value: null
push_to_hub_organization:
    value: null
push_to_hub_token:
    value: <PUSH_TO_HUB_TOKEN>
ray_scope:
    value: last
reasoning_tokens:
    value: true
remove_unused_columns:
    value: false
report_to:
    value:
        - wandb
restore_callback_states_from_checkpoint:
    value: false
resume_from_checkpoint:
    value: null
run_name:
    value: enhanced-32159
save_on_each_node:
    value: false
save_only_model:
    value: false
save_safetensors:
    value: true
save_steps:
    value: 500
save_strategy:
    value: steps
save_total_limit:
    value: 3
seed:
    value: 42
skip_memory_metrics:
    value: true
tf32:
    value: null
torch_compile:
    value: false
torch_compile_backend:
    value: null
torch_compile_mode:
    value: null
torch_empty_cache_steps:
    value: null
torchdynamo:
    value: null
tpu_metrics_debug:
    value: false
tpu_num_cores:
    value: null
use_cpu:
    value: false
use_ipex:
    value: false
use_legacy_prediction_loop:
    value: false
use_liger_kernel:
    value: false
use_mps_device:
    value: false
warmup_ratio:
    value: 0.1
warmup_steps:
    value: 0
weight_decay:
    value: 0
world_size:
    value: 3
