Starting training on GPU 0/3 with max_length=8192 (no quantization)
Loading checkpoint shards: 100%|██████████████████████████████████████████████████████████████████| 4/4 [00:00<00:00, 135.15it/s]
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
Enhanced checkpointing: every 5 layers for max 8192 tokens
Setting up LoRA without quantization
trainable params: 167,772,160 || all params: 8,198,066,176 || trainable%: 2.0465
Map: 100%|█████████████████████████████████████████████████████████████████████████████| 735/735 [00:02<00:00, 354.61 examples/s]
Map: 100%|███████████████████████████████████████████████████████████████████████████████| 41/41 [00:00<00:00, 382.45 examples/s]
Map: 100%|███████████████████████████████████████████████████████████████████████████████| 41/41 [00:00<00:00, 369.01 examples/s]
Filter: 100%|██████████████████████████████████████████████████████████████████████████| 735/735 [00:03<00:00, 217.96 examples/s]
Filter: 100%|████████████████████████████████████████████████████████████████████████████| 41/41 [00:00<00:00, 229.74 examples/s]
Filter: 100%|████████████████████████████████████████████████████████████████████████████| 41/41 [00:00<00:00, 238.03 examples/s]
Dataset after filtering: 735 training samples
Test samples: 41
=== Dataset Debug Info ===

Sample 0:
  input_ids type: <class 'list'>
  input_ids length: 8192
  labels type: <class 'list'>
  labels length: 8192
  input_ids all ints: True
  labels all ints: True
  decoded start: <|start_header_id|>user<|end_header_id|>

특별한 카드 한 벌에는 $49$장의 카드가 들어 있는데, 각 카드에는 $1$부터 $7$까지의 숫자가 적혀...

Sample 1:
  input_ids type: <class 'list'>
  input_ids length: 4174
  labels type: <class 'list'>
  labels length: 4174
  input_ids all ints: True
  labels all ints: True
  decoded start: <|start_header_id|>user<|end_header_id|>

2019^8 + 1의 최소 홀수 제곱근을 구하세요.<|eot_id|><|start_header_id|>a...

Sample 2:
  input_ids type: <class 'list'>
  input_ids length: 8192
  labels type: <class 'list'>
  labels length: 8192
  input_ids all ints: True
  labels all ints: True
  decoded start: <|start_header_id|>user<|end_header_id|>

삼각형 $ABC$는 $AC = 7,$ $BC = 24,$ 그리고 $C$에서 직각인 직각삼각형입니다. 점 ...
torch.distributed process group is initialized, but parallel_mode != ParallelMode.DISTRIBUTED. In order to use Torch DDP, launch your script with `python -m torch.distributed.launch
/data_x/junkim100/projects/translation_it/new.py:548: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `Trainer.__init__`. Use `processing_class` instead.
  trainer = Trainer(
Starting training without quantization...
Max sequence length: 8192
Batch size: 1
Gradient accumulation: 16
Effective batch size: 48
Note: Higher memory usage without quantization
  0%|                                                                                                     | 0/48 [00:00<?, ?it/s]`use_cache=True` is incompatible with gradient checkpointing. Setting `use_cache=False`.
  0%|                                                                                                     | 0/48 [00:05<?, ?it/s]
