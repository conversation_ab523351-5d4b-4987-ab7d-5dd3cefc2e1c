Process 0: Loading model and tokenizer for max_length=32159
Process 0: Tokenizer loaded with vocab size: 128260
Process 0: Loading model from meta-llama/Meta-Llama-3.1-8B
Loading checkpoint shards: 100%|███████████████████████████████████████████████████████████████████| 4/4 [00:12<00:00,  3.09s/it]
Process 0: Resizing token embeddings from 128256 to 128260
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
Process 0: Moving model to cuda:0
Process 0: Model successfully moved to cuda:0
Process 0: Passed model loading barrier

=== GPU Memory Usage After Model Loading ===
GPU 0: 5.32GB/79.14GB (6.7%) - Free: 73.82GB
GPU 1: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 2: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 3: 0.01GB/79.14GB (0.0%) - Free: 79.13GB
==================================================
Process 0: Setting up LoRA for max_length=32159
Process 0: Gradient checkpointing enabled
trainable params: 83,886,080 || all params: 8,114,180,096 || trainable%: 1.0338
Process 0: LoRA model is on cuda:0
Process 0: Setting up DDP with device_ids=[0], output_device=0
Process 0: DDP model is on cuda:0
Process 0: Passed DDP setup barrier

=== GPU Memory Usage After DDP Setup ===
GPU 0: 7.92GB/79.14GB (10.0%) - Free: 71.22GB
GPU 1: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 2: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 3: 0.01GB/79.14GB (0.0%) - Free: 79.13GB
==================================================
Process 0: Preparing dataset junkim100/limo_crosslingual_ko_en with max_length=32159
Process 0: Dataset loaded - Train: 735, Test: 41
Dataset filtering results:
  Train: 735 -> 735 (100.0% retained)
  Test: 41 -> 41 (100.0% retained)
  Sequence lengths - Min: 1199, Max: 32159, Mean: 6638.7
Process 0: Dataset prepared and synchronized
torch.distributed process group is initialized, but parallel_mode != ParallelMode.DISTRIBUTED. In order to use Torch DDP, launch your script with `python -m torch.distributed.launch

=== GPU Memory Usage Before Trainer Init ===
GPU 0: 7.92GB/79.14GB (10.0%) - Free: 71.22GB
GPU 1: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 2: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 3: 0.01GB/79.14GB (0.0%) - Free: 79.13GB
==================================================
/data_x/junkim100/projects/translation_it/new.py:618: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `Trainer.__init__`. Use `processing_class` instead.
  trainer = Trainer(

=== GPU Memory Usage Before Training Start ===
GPU 0: 7.92GB/79.14GB (10.0%) - Free: 71.22GB
GPU 1: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 2: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 3: 0.01GB/79.14GB (0.0%) - Free: 79.13GB
==================================================
🚀 Starting optimized training for 32k sequences on 4 GPUs...
   - Max sequence length: 32159
   - Batch size per device: 1
   - Gradient accumulation steps: 8
   - Effective batch size: 32
  0%|                                                                                                     | 0/46 [00:00<?, ?it/s]
