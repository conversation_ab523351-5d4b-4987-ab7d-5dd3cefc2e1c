Loading checkpoint shards: 100%|██████████████████████████████████████████████████████████████████| 4/4 [00:00<00:00, 134.04it/s]
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
Enhanced checkpointing: every 5 layers for max 8192 tokens
Moving model to GPU 0
Model is on device: cuda:0
