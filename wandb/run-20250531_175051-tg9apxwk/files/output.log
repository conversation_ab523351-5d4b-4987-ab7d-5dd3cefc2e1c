Process 0: Loading model and tokenizer for max_length=32159
Process 0: DeepSpeed mode - quantization disabled for compatibility
Process 0: Tokenizer loaded with vocab size: 128260
Process 0: Loading model from meta-llama/Meta-Llama-3.1-8B
Loading checkpoint shards: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:00<00:00, 92.98it/s]
Process 0: Resizing token embeddings from 128256 to 128260
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
Process 0: Model loaded, DeepSpeed will handle device placement
Process 0: Passed model loading barrier

=== GPU Memory Usage After Model Loading ===
GPU 0: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 1: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 2: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 3: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
==================================================
Process 0: Setting up LoRA for max_length=32159
Process 0: Using LoRA target modules: ['q_proj', 'k_proj', 'v_proj', 'o_proj', 'gate_proj', 'up_proj', 'down_proj']
Process 0: LoRA setup complete, DeepSpeed will handle gradient checkpointing
trainable params: 167,772,160 || all params: 8,198,066,176 || trainable%: 2.0465
Process 0: LoRA model prepared for DeepSpeed initialization
Process 0: Passed LoRA setup barrier
Process 0: Preparing dataset junkim100/limo_crosslingual_ko_en with max_length=32159
Process 0: Dataset loaded - Train: 735, Test: 41
Dataset filtering results:
  Train: 735 -> 735 (100.0% retained)
  Test: 41 -> 41 (100.0% retained)
  Sequence lengths - Min: 1199, Max: 32159, Mean: 6638.7
Process 0: Dataset prepared and synchronized
[2025-05-31 17:51:45,285] [INFO] [comm.py:669:init_distributed] cdb=None

=== GPU Memory Usage Before Trainer Init ===
GPU 0: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 1: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 2: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 3: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
==================================================
/data_x/junkim100/projects/translation_it/train.py:706: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `Trainer.__init__`. Use `processing_class` instead.
  trainer = Trainer(
No label_names provided for model class `PeftModelForCausalLM`. Since `PeftModel` hides base models input arguments, if label_names is not given, label_names can't be set automatically within `Trainer`. Note that empty label_names list will be used instead.

=== GPU Memory Usage After DeepSpeed Init ===
GPU 0: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 1: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 2: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 3: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
==================================================
🚀 Starting DeepSpeed training for 32k sequences on 4 GPUs...
   - Max sequence length: 32159
   - DeepSpeed ZeRO Stage: 3 (Maximum Memory Efficiency)
   - Batch size per device: 1
   - Gradient accumulation steps: 8
   - Effective batch size: 32
Installed CUDA version 12.4 does not match the version torch was compiled with 12.6 but since the APIs are compatible, accepting this combination
Using /mnt/raid6/junkim100/.cache/torch_extensions/py310_cu126 as PyTorch extensions root...
Loading extension module cpu_adam...
Time to load cpu_adam op: 23.7118980884552 seconds
Parameter Offload: Total persistent parameters: 266240 in 65 params
[2025-05-31 17:52:47,351] [WARNING] [lr_schedules.py:683:get_lr] Attempting to get learning rate from scheduler before it has started
  0%|                                                                                                                                                                                                                                         | 0/46 [00:00<?, ?it/s]Traceback (most recent call last):
❌ Process 0: Error during training: CUDA out of memory. Tried to allocate 70.00 MiB. GPU 0 has a total capacity of 79.14 GiB of which 8.75 MiB is free. Including non-PyTorch memory, this process has 79.11 GiB memory in use. Of the allocated memory 74.75 GiB is allocated by PyTorch, and 3.55 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
  File "/data_x/junkim100/projects/translation_it/train.py", line 740, in train_model
    trainer.train()
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/transformers/trainer.py", line 2240, in train
    return inner_training_loop(
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/transformers/trainer.py", line 2555, in _inner_training_loop
    tr_loss_step = self.training_step(model, inputs, num_items_in_batch)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/transformers/trainer.py", line 3745, in training_step
    loss = self.compute_loss(model, inputs, num_items_in_batch=num_items_in_batch)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/transformers/trainer.py", line 3810, in compute_loss
    outputs = model(**inputs)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/deepspeed/utils/nvtx.py", line 20, in wrapped_fn
    ret_val = func(*args, **kwargs)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/deepspeed/runtime/engine.py", line 2054, in forward
    loss = self.module(*inputs, **kwargs)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1857, in _call_impl
    return inner()
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1805, in inner
    result = forward_call(*args, **kwargs)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/peft/peft_model.py", line 1757, in forward
    return self.base_model(
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1857, in _call_impl
    return inner()
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1805, in inner
    result = forward_call(*args, **kwargs)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/peft/tuners/tuners_utils.py", line 193, in forward
    return self.model.forward(*args, **kwargs)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/transformers/utils/generic.py", line 969, in wrapper
    output = func(self, *args, **kwargs)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/transformers/models/llama/modeling_llama.py", line 688, in forward
    outputs: BaseModelOutputWithPast = self.model(
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1857, in _call_impl
    return inner()
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1805, in inner
    result = forward_call(*args, **kwargs)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/transformers/utils/generic.py", line 969, in wrapper
    output = func(self, *args, **kwargs)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/transformers/models/llama/modeling_llama.py", line 453, in forward
    layer_outputs = decoder_layer(
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/transformers/modeling_layers.py", line 48, in __call__
    return super().__call__(*args, **kwargs)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1857, in _call_impl
    return inner()
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1805, in inner
    result = forward_call(*args, **kwargs)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/transformers/models/llama/modeling_llama.py", line 308, in forward
    hidden_states, self_attn_weights = self.self_attn(
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1857, in _call_impl
    return inner()
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1805, in inner
    result = forward_call(*args, **kwargs)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/transformers/models/llama/modeling_llama.py", line 277, in forward
    attn_output = self.o_proj(attn_output)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1857, in _call_impl
    return inner()
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1805, in inner
    result = forward_call(*args, **kwargs)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/peft/tuners/lora/layer.py", line 727, in forward
    result = result + lora_B(lora_A(dropout(x))) * scaling
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1857, in _call_impl
    return inner()
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1805, in inner
    result = forward_call(*args, **kwargs)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/torch/nn/modules/dropout.py", line 70, in forward
    return F.dropout(input, self.p, self.training, self.inplace)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/torch/nn/functional.py", line 1425, in dropout
    _VF.dropout_(input, p, training) if inplace else _VF.dropout(input, p, training)
torch.OutOfMemoryError: CUDA out of memory. Tried to allocate 70.00 MiB. GPU 0 has a total capacity of 79.14 GiB of which 8.75 MiB is free. Including non-PyTorch memory, this process has 79.11 GiB memory in use. Of the allocated memory 74.75 GiB is allocated by PyTorch, and 3.55 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
