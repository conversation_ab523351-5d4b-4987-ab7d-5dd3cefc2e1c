2025-05-31 17:50:51,514 INFO    MainThread:3395241 [wandb_setup.py:_flush():70] Current SDK version is 0.19.11
2025-05-31 17:50:51,514 INFO    MainThread:3395241 [wandb_setup.py:_flush():70] Configure stats pid to 3395241
2025-05-31 17:50:51,514 INFO    MainThread:3395241 [wandb_setup.py:_flush():70] Loading settings from /mnt/raid6/junkim100/.config/wandb/settings
2025-05-31 17:50:51,515 INFO    MainThread:3395241 [wandb_setup.py:_flush():70] Loading settings from /data_x/junkim100/projects/translation_it/wandb/settings
2025-05-31 17:50:51,515 INFO    MainThread:3395241 [wandb_setup.py:_flush():70] Loading settings from environment variables
2025-05-31 17:50:51,515 INFO    MainThread:3395241 [wandb_init.py:setup_run_log_directory():724] Logging user logs to /data_x/junkim100/projects/translation_it/wandb/run-20250531_175051-tg9apxwk/logs/debug.log
2025-05-31 17:50:51,515 INFO    MainThread:3395241 [wandb_init.py:setup_run_log_directory():725] Logging internal logs to /data_x/junkim100/projects/translation_it/wandb/run-20250531_175051-tg9apxwk/logs/debug-internal.log
2025-05-31 17:50:51,515 INFO    MainThread:3395241 [wandb_init.py:init():852] calling init triggers
2025-05-31 17:50:51,515 INFO    MainThread:3395241 [wandb_init.py:init():857] wandb.init called with sweep_config: {}
config: {'model_name': 'meta-llama/Meta-Llama-3.1-8B', 'dataset_name': 'junkim100/limo_crosslingual_ko_en', 'world_size': 4, 'max_length': 32159, 'method': 'LoRA + DeepSpeed ZeRO-3', 'reasoning_tokens': True, 'deepspeed_stage': 3, '_wandb': {}}
2025-05-31 17:50:51,515 INFO    MainThread:3395241 [wandb_init.py:init():893] starting backend
2025-05-31 17:50:51,515 INFO    MainThread:3395241 [wandb_init.py:init():897] sending inform_init request
2025-05-31 17:50:51,517 INFO    MainThread:3395241 [backend.py:_multiprocessing_setup():101] multiprocessing start_methods=fork,spawn,forkserver, using: spawn
2025-05-31 17:50:51,517 INFO    MainThread:3395241 [wandb_init.py:init():907] backend started and connected
2025-05-31 17:50:51,520 INFO    MainThread:3395241 [wandb_init.py:init():1005] updated telemetry
2025-05-31 17:50:51,520 INFO    MainThread:3395241 [wandb_init.py:init():1029] communicating run to backend with 90.0 second timeout
2025-05-31 17:50:52,323 INFO    MainThread:3395241 [wandb_init.py:init():1104] starting run threads in backend
2025-05-31 17:50:52,466 INFO    MainThread:3395241 [wandb_run.py:_console_start():2573] atexit reg
2025-05-31 17:50:52,466 INFO    MainThread:3395241 [wandb_run.py:_redirect():2421] redirect: wrap_raw
2025-05-31 17:50:52,466 INFO    MainThread:3395241 [wandb_run.py:_redirect():2490] Wrapping output streams.
2025-05-31 17:50:52,466 INFO    MainThread:3395241 [wandb_run.py:_redirect():2513] Redirects installed.
2025-05-31 17:50:52,467 INFO    MainThread:3395241 [wandb_init.py:init():1150] run started, returning control to user process
2025-05-31 17:52:47,368 INFO    MainThread:3395241 [wandb_run.py:_config_callback():1436] config_cb None None {'peft_config': {'default': {'task_type': 'CAUSAL_LM', 'peft_type': <PeftType.LORA: 'LORA'>, 'auto_mapping': None, 'base_model_name_or_path': 'meta-llama/Meta-Llama-3.1-8B', 'revision': None, 'inference_mode': False, 'r': 64, 'target_modules': {'q_proj', 'gate_proj', 'v_proj', 'up_proj', 'k_proj', 'down_proj', 'o_proj'}, 'exclude_modules': None, 'lora_alpha': 32, 'lora_dropout': 0.05, 'fan_in_fan_out': False, 'bias': 'none', 'use_rslora': False, 'modules_to_save': None, 'init_lora_weights': True, 'layers_to_transform': None, 'layers_pattern': None, 'rank_pattern': {}, 'alpha_pattern': {}, 'megatron_config': None, 'megatron_core': 'megatron.core', 'trainable_token_indices': None, 'loftq_config': {}, 'eva_config': None, 'corda_config': None, 'use_dora': False, 'layer_replication': None, 'runtime_config': {'ephemeral_gpu_offload': False}, 'lora_bias': False}}, 'vocab_size': 128260, 'max_position_embeddings': 131072, 'hidden_size': 4096, 'intermediate_size': 14336, 'num_hidden_layers': 32, 'num_attention_heads': 32, 'num_key_value_heads': 8, 'hidden_act': 'silu', 'initializer_range': 0.02, 'rms_norm_eps': 1e-05, 'pretraining_tp': 1, 'use_cache': False, 'rope_theta': 500000.0, 'rope_scaling': {'factor': 8.0, 'low_freq_factor': 1.0, 'high_freq_factor': 4.0, 'original_max_position_embeddings': 8192, 'rope_type': 'llama3'}, 'attention_bias': False, 'attention_dropout': 0.0, 'mlp_bias': False, 'head_dim': 128, 'return_dict': True, 'output_hidden_states': False, 'output_attentions': False, 'torchscript': False, 'torch_dtype': 'bfloat16', 'use_bfloat16': False, 'tf_legacy_loss': False, 'pruned_heads': {}, 'tie_word_embeddings': False, 'chunk_size_feed_forward': 0, 'is_encoder_decoder': False, 'is_decoder': False, 'cross_attention_hidden_size': None, 'add_cross_attention': False, 'tie_encoder_decoder': False, 'max_length': 20, 'min_length': 0, 'do_sample': False, 'early_stopping': False, 'num_beams': 1, 'num_beam_groups': 1, 'diversity_penalty': 0.0, 'temperature': 1.0, 'top_k': 50, 'top_p': 1.0, 'typical_p': 1.0, 'repetition_penalty': 1.0, 'length_penalty': 1.0, 'no_repeat_ngram_size': 0, 'encoder_no_repeat_ngram_size': 0, 'bad_words_ids': None, 'num_return_sequences': 1, 'output_scores': False, 'return_dict_in_generate': False, 'forced_bos_token_id': None, 'forced_eos_token_id': None, 'remove_invalid_values': False, 'exponential_decay_length_penalty': None, 'suppress_tokens': None, 'begin_suppress_tokens': None, 'architectures': ['LlamaForCausalLM'], 'finetuning_task': None, 'id2label': {0: 'LABEL_0', 1: 'LABEL_1'}, 'label2id': {'LABEL_0': 0, 'LABEL_1': 1}, 'tokenizer_class': None, 'prefix': None, 'bos_token_id': 128000, 'pad_token_id': None, 'eos_token_id': 128001, 'sep_token_id': None, 'decoder_start_token_id': None, 'task_specific_params': None, 'problem_type': None, '_name_or_path': 'meta-llama/Meta-Llama-3.1-8B', 'transformers_version': '4.52.4', 'model_type': 'llama', 'output_dir': './llama-reasoning-finetuned-32k-deepspeed', 'overwrite_output_dir': False, 'do_train': False, 'do_eval': True, 'do_predict': False, 'eval_strategy': 'steps', 'prediction_loss_only': False, 'per_device_train_batch_size': 1, 'per_device_eval_batch_size': 8, 'per_gpu_train_batch_size': None, 'per_gpu_eval_batch_size': None, 'gradient_accumulation_steps': 8, 'eval_accumulation_steps': 1, 'eval_delay': 0, 'torch_empty_cache_steps': None, 'learning_rate': 0.0001, 'weight_decay': 0.01, 'adam_beta1': 0.9, 'adam_beta2': 0.999, 'adam_epsilon': 1e-08, 'max_grad_norm': 0.5, 'num_train_epochs': 2, 'max_steps': -1, 'lr_scheduler_type': 'linear', 'lr_scheduler_kwargs': {}, 'warmup_ratio': 0.05, 'warmup_steps': 0, 'log_level': 'passive', 'log_level_replica': 'warning', 'log_on_each_node': True, 'logging_dir': './llama-reasoning-finetuned-32k-deepspeed/runs/May31_17-51-45_nlp-server-18', 'logging_strategy': 'steps', 'logging_first_step': True, 'logging_steps': 5, 'logging_nan_inf_filter': True, 'save_strategy': 'steps', 'save_steps': 100, 'save_total_limit': 2, 'save_safetensors': True, 'save_on_each_node': False, 'save_only_model': False, 'restore_callback_states_from_checkpoint': False, 'no_cuda': False, 'use_cpu': False, 'use_mps_device': False, 'seed': 42, 'data_seed': None, 'jit_mode_eval': False, 'use_ipex': False, 'bf16': True, 'fp16': False, 'fp16_opt_level': 'O1', 'half_precision_backend': 'auto', 'bf16_full_eval': False, 'fp16_full_eval': False, 'tf32': True, 'local_rank': 0, 'ddp_backend': None, 'tpu_num_cores': None, 'tpu_metrics_debug': False, 'debug': [], 'dataloader_drop_last': False, 'eval_steps': 100, 'dataloader_num_workers': 0, 'dataloader_prefetch_factor': None, 'past_index': -1, 'run_name': '32k-4gpu-deepspeed', 'disable_tqdm': False, 'remove_unused_columns': False, 'label_names': None, 'load_best_model_at_end': False, 'metric_for_best_model': None, 'greater_is_better': None, 'ignore_data_skip': False, 'fsdp': [], 'fsdp_min_num_params': 0, 'fsdp_config': {'min_num_params': 0, 'xla': False, 'xla_fsdp_v2': False, 'xla_fsdp_grad_ckpt': False}, 'fsdp_transformer_layer_cls_to_wrap': None, 'accelerator_config': {'split_batches': False, 'dispatch_batches': None, 'even_batches': True, 'use_seedable_sampler': True, 'non_blocking': False, 'gradient_accumulation_kwargs': None}, 'deepspeed': 'deepspeed_config_zero3.json', 'label_smoothing_factor': 0.0, 'optim': 'adamw_torch', 'optim_args': None, 'adafactor': False, 'group_by_length': False, 'length_column_name': 'length', 'report_to': ['wandb'], 'ddp_find_unused_parameters': None, 'ddp_bucket_cap_mb': None, 'ddp_broadcast_buffers': None, 'dataloader_pin_memory': False, 'dataloader_persistent_workers': False, 'skip_memory_metrics': False, 'use_legacy_prediction_loop': False, 'push_to_hub': False, 'resume_from_checkpoint': None, 'hub_model_id': None, 'hub_strategy': 'every_save', 'hub_token': '<HUB_TOKEN>', 'hub_private_repo': None, 'hub_always_push': False, 'gradient_checkpointing': False, 'gradient_checkpointing_kwargs': None, 'include_inputs_for_metrics': False, 'include_for_metrics': [], 'eval_do_concat_batches': True, 'fp16_backend': 'auto', 'push_to_hub_model_id': None, 'push_to_hub_organization': None, 'push_to_hub_token': '<PUSH_TO_HUB_TOKEN>', 'mp_parameters': '', 'auto_find_batch_size': False, 'full_determinism': False, 'torchdynamo': None, 'ray_scope': 'last', 'ddp_timeout': 1800, 'torch_compile': False, 'torch_compile_backend': None, 'torch_compile_mode': None, 'include_tokens_per_second': False, 'include_num_input_tokens_seen': False, 'neftune_noise_alpha': None, 'optim_target_modules': None, 'batch_eval_metrics': False, 'eval_on_start': False, 'use_liger_kernel': False, 'eval_use_gather_object': False, 'average_tokens_across_devices': False}
2025-05-31 17:52:47,372 INFO    MainThread:3395241 [wandb_config.py:__setitem__():154] [no run ID] config set model/num_parameters = 0 - <bound method Run._config_callback of <wandb.sdk.wandb_run.Run object at 0x7faf6ce961d0>>
2025-05-31 17:52:47,372 INFO    MainThread:3395241 [wandb_run.py:_config_callback():1436] config_cb model/num_parameters 0 None
