Starting training on GPU 0/4 with max_length=32159
Loading checkpoint shards: 100%|███████████████████████████████████████████████████████████████████| 4/4 [00:12<00:00,  3.06s/it]
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
Enhanced checkpointing: every 8 layers for max 32159 tokens
Process 0: Model moved to cuda:0
trainable params: 167,772,160 || all params: 8,198,066,176 || trainable%: 2.0465
=== GPU Status ===
Sat May 31 12:38:26 2025
+-----------------------------------------------------------------------------------------+
| NVIDIA-SMI 550.54.14              Driver Version: 550.54.14      CUDA Version: 12.4     |
|-----------------------------------------+------------------------+----------------------+
| GPU  Name                 Persistence-M | Bus-Id          Disp.A | Volatile Uncorr. ECC |
| Fan  Temp   Perf          Pwr:Usage/Cap |           Memory-Usage | GPU-Util  Compute M. |
|                                         |                        |               MIG M. |
|=========================================+========================+======================|
|   0  NVIDIA A100-SXM4-80GB          On  |   00000000:01:00.0 Off |                    0 |
| N/A   33C    P0             68W /  400W |   26521MiB /  81920MiB |    100%      Default |
|                                         |                        |             Disabled |
+-----------------------------------------+------------------------+----------------------+
|   1  NVIDIA A100-SXM4-80GB          On  |   00000000:21:00.0 Off |                    0 |
| N/A   37C    P0             58W /  400W |   26521MiB /  81920MiB |     38%      Default |
|                                         |                        |             Disabled |
+-----------------------------------------+------------------------+----------------------+
|   2  NVIDIA A100-SXM4-80GB          On  |   00000000:41:00.0 Off |                    0 |
| N/A   34C    P0             68W /  400W |   26521MiB /  81920MiB |    100%      Default |
|                                         |                        |             Disabled |
+-----------------------------------------+------------------------+----------------------+
|   3  NVIDIA A100-SXM4-80GB          On  |   00000000:61:00.0 Off |                    0 |
| N/A   35C    P0             73W /  400W |   26521MiB /  81920MiB |     23%      Default |
|                                         |                        |             Disabled |
+-----------------------------------------+------------------------+----------------------+
|   4  NVIDIA A100-SXM4-80GB          On  |   00000000:81:00.0 Off |                    0 |
| N/A   32C    P0             36W /  400W |       0MiB /  81920MiB |      0%      Default |
|                                         |                        |             Disabled |
+-----------------------------------------+------------------------+----------------------+
|   5  NVIDIA A100-SXM4-80GB          On  |   00000000:A1:00.0 Off |                    0 |
| N/A   31C    P0             35W /  400W |       0MiB /  81920MiB |      0%      Default |
|                                         |                        |             Disabled |
+-----------------------------------------+------------------------+----------------------+
|   6  NVIDIA A100-SXM4-80GB          On  |   00000000:C1:00.0 Off |                    0 |
| N/A   32C    P0             41W /  400W |   65735MiB /  81920MiB |      0%      Default |
|                                         |                        |             Disabled |
+-----------------------------------------+------------------------+----------------------+
|   7  NVIDIA A100-SXM4-80GB          On  |   00000000:E1:00.0 Off |                    0 |
| N/A   31C    P0             33W /  400W |       0MiB /  81920MiB |      0%      Default |
|                                         |                        |             Disabled |
+-----------------------------------------+------------------------+----------------------+

+-----------------------------------------------------------------------------------------+
| Processes:                                                                              |
|  GPU   GI   CI        PID   Type   Process name                              GPU Memory |
|        ID   ID                                                               Usage      |
|=========================================================================================|
|    0   N/A  N/A   3303948      C   ...im100/miniconda3/envs/it/bin/python      26512MiB |
|    1   N/A  N/A   3303949      C   ...im100/miniconda3/envs/it/bin/python      26512MiB |
|    2   N/A  N/A   3303950      C   ...im100/miniconda3/envs/it/bin/python      26512MiB |
|    3   N/A  N/A   3303951      C   ...im100/miniconda3/envs/it/bin/python      26512MiB |
|    6   N/A  N/A   3190630      C   KULLM-R Demo                                65726MiB |
+-----------------------------------------------------------------------------------------+

==================================================
Dataset after filtering: 735 training samples
Test samples: 41
=== Dataset Debug Info ===

Sample 0:
  input_ids type: <class 'list'>
  input_ids length: 11013
  labels type: <class 'list'>
  labels length: 11013
  input_ids all ints: True
  labels all ints: True
  decoded start: <|start_header_id|>user<|end_header_id|>

특별한 카드 한 벌에는 $49$장의 카드가 들어 있는데, 각 카드에는 $1$부터 $7$까지의 숫자가 적혀...

Sample 1:
  input_ids type: <class 'list'>
  input_ids length: 4174
  labels type: <class 'list'>
  labels length: 4174
  input_ids all ints: True
  labels all ints: True
  decoded start: <|start_header_id|>user<|end_header_id|>

2019^8 + 1의 최소 홀수 제곱근을 구하세요.<|eot_id|><|start_header_id|>a...

Sample 2:
  input_ids type: <class 'list'>
  input_ids length: 13892
  labels type: <class 'list'>
  labels length: 13892
  input_ids all ints: True
  labels all ints: True
  decoded start: <|start_header_id|>user<|end_header_id|>

삼각형 $ABC$는 $AC = 7,$ $BC = 24,$ 그리고 $C$에서 직각인 직각삼각형입니다. 점 ...
torch.distributed process group is initialized, but parallel_mode != ParallelMode.DISTRIBUTED. In order to use Torch DDP, launch your script with `python -m torch.distributed.launch
/data_x/junkim100/projects/translation_it/new.py:503: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `Trainer.__init__`. Use `processing_class` instead.
  trainer = Trainer(
Starting enhanced training with long sequence support...
Max sequence length: 32159
Batch size: 1
Gradient accumulation: 16
Effective batch size: 64
  0%|                                                                                                     | 0/36 [00:00<?, ?it/s]`use_cache=True` is incompatible with gradient checkpointing. Setting `use_cache=False`.
/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/torch/_dynamo/eval_frame.py:838: UserWarning: torch.utils.checkpoint: the use_reentrant parameter should be passed explicitly. In version 2.5 we will raise an exception if use_reentrant is not passed. use_reentrant=False is recommended, but if you need to preserve the current default behavior, you can pass use_reentrant=True. Refer to docs for more details on the differences between the two variants.
  return fn(*args, **kwargs)
  0%|                                                                                                     | 0/36 [00:28<?, ?it/s]
