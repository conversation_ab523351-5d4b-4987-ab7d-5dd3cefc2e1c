Process 0: Loading model and tokenizer for max_length=9064
Process 0: DeepSpeed mode - quantization disabled for compatibility
Process 0: Tokenizer loaded with vocab size: 128258
Process 0: Loading model from meta-llama/Meta-Llama-3.1-8B
Loading checkpoint shards: 100%|██████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:00<00:00, 135.33it/s]
Process 0: Resizing token embeddings from 128256 to 128258
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
Process 0: Model loaded on CPU, DeepSpeed will handle device placement
Process 0: Passed model loading barrier

=== GPU Memory Usage After Model Loading ===
GPU 0: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 1: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 2: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 3: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
==================================================
Process 0: Setting up LoRA for max_length=9064
Process 0: Using LoRA target modules: ['q_proj', 'k_proj', 'v_proj', 'o_proj', 'gate_proj', 'up_proj', 'down_proj']
Process 0: LoRA setup complete, DeepSpeed will handle gradient checkpointing
trainable params: 20,971,520 || all params: 8,051,249,152 || trainable%: 0.2605
Process 0: LoRA model prepared for DeepSpeed initialization
Process 0: Passed LoRA setup barrier
Process 0: Preparing dataset junkim100/Lima-X-Processed with max_length=9064
Process 0: Dataset loaded - Train: 1030, Val: 52
Formatting dataset: 100%|██████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 1030/1030 [00:00<00:00, 17481.92 examples/s]
Formatting dataset: 100%|██████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 52/52 [00:00<00:00, 11973.20 examples/s]
Tokenizing dataset: 100%|████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 1030/1030 [00:02<00:00, 417.83 examples/s]
Tokenizing dataset: 100%|████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 52/52 [00:00<00:00, 495.40 examples/s]
Filter: 100%|████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 1030/1030 [00:01<00:00, 758.38 examples/s]
Filter: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 52/52 [00:00<00:00, 1056.85 examples/s]
Dataset filtering results:
  Train: 1030 -> 1030 (100.0% retained)
  Val: 52 -> 52 (100.0% retained)
  Sequence lengths - Min: 76, Max: 9064, Mean: 1516.7
Process 0: Dataset prepared and synchronized
[2025-06-01 00:47:39,095] [INFO] [comm.py:669:init_distributed] cdb=None

=== GPU Memory Usage Before Trainer Init ===
GPU 0: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 1: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 2: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 3: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
==================================================
/data_x/junkim100/projects/translation_it/train.py:612: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `Trainer.__init__`. Use `processing_class` instead.
  trainer = Trainer(
No label_names provided for model class `PeftModelForCausalLM`. Since `PeftModel` hides base models input arguments, if label_names is not given, label_names can't be set automatically within `Trainer`. Note that empty label_names list will be used instead.

=== GPU Memory Usage After DeepSpeed Init ===
GPU 0: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 1: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 2: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 3: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
==================================================
🚀 Starting DeepSpeed training for 9k sequences on 4 GPUs...
   - Max sequence length: 9064
   - DeepSpeed ZeRO Stage: 3 (Maximum Memory Efficiency)
   - Batch size per device: 1
   - Gradient accumulation steps: 16
   - Effective batch size: 64
Installed CUDA version 12.4 does not match the version torch was compiled with 12.6 but since the APIs are compatible, accepting this combination
Using /mnt/raid6/junkim100/.cache/torch_extensions/py310_cu126 as PyTorch extensions root...
Loading extension module cpu_adam...
Time to load cpu_adam op: 2.4947149753570557 seconds
Parameter Offload: Total persistent parameters: 10227712 in 417 params
[2025-06-01 00:48:05,070] [WARNING] [lr_schedules.py:683:get_lr] Attempting to get learning rate from scheduler before it has started
  0%|                                                                                                                                                                 | 0/34 [00:00<?, ?it/s]
