setproctitle==1.3.3
omegaconf==2.3.0
graphql-relay==3.2.0
wcwidth==0.2.13
six==1.16.0
pyte==0.8.2
narwhals==1.31.0
decorator==5.1.1
colorama==0.4.6
thefuck==3.32
pure-eval==0.2.2
kaleido==0.2.1
traitlets==5.14.3
tornado==6.4
pyasn1_modules==0.4.1
python-dateutil==2.9.0.post0
Pygments==2.17.2
prompt-toolkit==3.0.43
platformdirs==4.2.1
parso==0.8.4
nest-asyncio==1.6.0
executing==2.0.1
exceptiongroup==1.2.1
debugpy==1.8.1
asttokens==2.4.1
stack-data==0.6.3
matplotlib-inline==0.1.7
jupyter_core==5.7.2
jedi==0.19.1
comm==0.2.2
jupyter_client==8.6.1
ipython==8.23.0
ipykernel==6.29.4
wrapt==1.16.0
Werkzeug==3.0.6
sqlparse==0.5.1
pyasn1==0.6.1
Markdown==3.7
Mako==1.3.6
itsdangerous==2.2.0
gunicorn==23.0.0
graphql-core==3.2.5
cachetools==5.5.0
blinker==1.8.2
rsa==4.9
Flask==3.0.3
docker==7.1.0
Deprecated==1.2.14
alembic==1.13.3
requests-oauthlib==2.0.0
graphene==3.4.1
google-auth==2.35.0
databricks-sdk==0.36.0
mlflow-skinny==2.17.1
mlflow==2.17.1
seaborn==0.13.2
google-auth-oauthlib==1.2.1
gspread==6.1.4
py==1.11.0
retry==0.9.2
oauth2client==4.1.3
uvicorn==0.34.0
mcp==1.6.0
markdown2==2.5.2
wkhtmltopdf==0.2
uuid==1.30
striprtf==0.0.26
pdfkit==1.0.0
fire==0.7.0
filetype==1.2.0
dirtyjson==1.0.8
zstandard==0.23.0
widgetsnbextension==4.0.13
tzdata==2025.1
pydantic_core==2.33.0
threadpoolctl==3.5.0
tenacity==9.0.0
pydantic==2.11.1
soupsieve==2.6
sniffio==1.3.1
safetensors==0.5.2
rpds-py==0.22.3
regex==2024.11.6
pycparser==2.22
propcache==0.2.1
pydantic-settings==2.8.1
orjson==3.10.15
ordered-set==4.1.0
starlette==0.46.1
plotly==6.0.1
python-dotenv==1.1.0
httpx-sse==0.4.0
annotated-types==0.7.0
mypy-extensions==1.0.0
typing-inspection==0.4.0
sse-starlette==2.2.1
kiwisolver==1.4.8
jupyterlab_widgets==3.0.13
jsonpickle==4.0.1
jsonpatch==1.33
joblib==1.4.2
jiter==0.8.2
importlib_resources==6.5.2
h11==0.14.0
frozenlist==1.5.0
fonttools==4.55.4
diskcache==5.6.3
cycler==0.12.1
click==8.1.8
charset-normalizer==3.4.1
certifi==2024.12.14
attrs==24.3.0
async-timeout==4.0.3
aiohappyeyeballs==2.4.4
yachalk==0.1.7
typing-inspect==0.9.0
SQLAlchemy==2.0.37
scipy==1.15.1
requests==2.32.3
referencing==0.36.1
python-louvain==0.16
pytesseract==0.3.13
pypdf==5.1.0
pandas==2.2.3
nltk==3.9.1
multidict==6.1.0
marshmallow==3.26.0
httpcore==1.0.7
contourpy==1.3.1
cffi==1.17.1
beautifulsoup4==4.12.3
anyio==4.8.0
aiosignal==1.3.2
yarl==1.18.3
tiktoken==0.8.0
scikit-learn==1.6.1
requests-toolbelt==1.0.0
matplotlib==3.10.0
httpx==0.28.1
dataclasses-json==0.6.7
cryptography==44.0.0
pyvis==0.3.2
powerlaw==1.5
pdfminer.six==20240706
openai==1.60.0
llama-cloud==0.1.10
langsmith==0.3.1
ipywidgets==8.1.5
community==1.0.0b1
aiohttp==3.11.11
llama-index-core==0.12.12
guidance-stitch==0.1.0
llama-parse==0.5.20
llama-index-readers-file==0.4.3
llama-index-llms-openai==0.3.14
llama-index-indices-managed-llama-cloud==0.6.4
llama-index-embeddings-openai==0.3.1
guidance==0.2.0
llama-index-readers-llama-parse==0.4.0
llama-index-multi-modal-llms-openai==0.4.2
llama-index-cli==0.4.0
llama-index-agent-openai==0.4.2
llama-index-program-openai==0.3.1
llama-index-question-gen-openai==0.3.0
llama-index==0.12.12
networkx==3.4.2
wheel==0.45.1
pip==25.1
typing_extensions==4.13.2
setuptools==80.9.0
sympy==1.14.0
nvidia-nvtx-cu12==12.6.77
nvidia-nvjitlink-cu12==12.6.85
nvidia-nccl-cu12==2.26.2
nvidia-curand-cu12==*********
nvidia-cufile-cu12==********
nvidia-cuda-runtime-cu12==12.6.77
nvidia-cuda-nvrtc-cu12==12.6.77
nvidia-cuda-cupti-cu12==12.6.80
nvidia-cublas-cu12==********
MarkupSafe==3.0.2
filelock==3.18.0
nvidia-cusparse-cu12==********
nvidia-cufft-cu12==********
nvidia-cudnn-cu12==********
nvidia-cusolver-cu12==********
torch==2.7.0
urllib3==2.4.0
tqdm==4.67.1
PyYAML==6.0.2
packaging==25.0
Jinja2==3.1.6
idna==3.10
hf-xet==1.1.2
huggingface-hub==0.32.3
tokenizers==0.21.1
transformers==4.52.4
psutil==7.0.0
accelerate==1.7.0
peft==0.15.2
pytz==2025.2
xxhash==3.5.0
smmap==5.0.2
sentry-sdk==2.29.1
pyarrow==20.0.0
protobuf==6.31.1
dill==0.3.8
fsspec==2025.5.1
docker-pycreds==0.4.0
multiprocess==0.70.16
gitdb==4.0.12
GitPython==3.1.44
wandb==0.19.11
datasets==3.6.0
py-cpuinfo==9.0.0
bitsandbytes==0.46.0
nvidia-cusparselt-cu12==0.6.3
mpmath==1.3.0
numpy==2.2.6
triton==3.3.0
nvidia-ml-py==12.575.51
hjson==3.1.0
ninja==********
msgpack==1.1.0
einops==0.8.1
deepspeed==0.16.9
