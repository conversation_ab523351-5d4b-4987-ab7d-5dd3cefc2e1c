Loading model and tokenizer...
Loading checkpoint shards: 100%|███████████████████████████████████████████████████████████████████| 4/4 [00:15<00:00,  3.80s/it]
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
Resized embeddings from 128260 to 128260
Setting up LoRA...
trainable params: 167,772,160 || all params: 8,198,066,176 || trainable%: 2.0465
Preparing dataset...
Loading dataset: junkim100/limo_crosslingual_ko_en
Tokenizing dataset: 100%|██████████████████████████████████████████████████████████████| 735/735 [00:02<00:00, 315.38 examples/s]
Filter: 100%|██████████████████████████████████████████████████████████████████████████| 735/735 [00:04<00:00, 178.18 examples/s]
Filter: 100%|████████████████████████████████████████████████████████████████████████████| 41/41 [00:00<00:00, 200.94 examples/s]
Filter: 100%|████████████████████████████████████████████████████████████████████████████| 41/41 [00:00<00:00, 197.17 examples/s]
Dataset prepared: 735 train, 41 test
Traceback (most recent call last):
  File "/data_x/junkim100/projects/translation_it/train_deepspeed.py", line 480, in <module>
    main()
  File "/data_x/junkim100/projects/translation_it/train_deepspeed.py", line 420, in main
    training_args = TrainingArguments(
TypeError: TrainingArguments.__init__() got an unexpected keyword argument 'evaluation_strategy'
Traceback (most recent call last):
  File "/data_x/junkim100/projects/translation_it/train_deepspeed.py", line 480, in <module>
    main()
  File "/data_x/junkim100/projects/translation_it/train_deepspeed.py", line 420, in main
    training_args = TrainingArguments(
TypeError: TrainingArguments.__init__() got an unexpected keyword argument 'evaluation_strategy'
