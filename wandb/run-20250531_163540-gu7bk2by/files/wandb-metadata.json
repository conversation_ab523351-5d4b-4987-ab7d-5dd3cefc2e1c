{"os": "Linux-5.15.0-134-generic-x86_64-with-glibc2.35", "python": "CPython 3.10.0", "startedAt": "2025-05-31T07:35:40.772854Z", "args": ["--local_rank=3"], "program": "/data_x/junkim100/projects/translation_it/train_deepspeed.py", "codePath": "train_deepspeed.py", "email": "<EMAIL>", "root": "/data_x/junkim100/projects/translation_it", "host": "nlp-server-18", "executable": "/mnt/raid6/junkim100/miniconda3/envs/it/bin/python3.10", "codePathLocal": "train_deepspeed.py", "cpu_count": 64, "cpu_count_logical": 128, "gpu": "NVIDIA A100-SXM4-80GB", "gpu_count": 8, "disk": {"/": {"total": "1966736678912", "used": "348115038208"}}, "memory": {"total": "1081929859072"}, "cpu": {"count": 64, "countLogical": 128}, "gpu_nvidia": [{"name": "NVIDIA A100-SXM4-80GB", "memoryTotal": "85899345920", "cudaCores": 6912, "architecture": "Ampere"}, {"name": "NVIDIA A100-SXM4-80GB", "memoryTotal": "85899345920", "cudaCores": 6912, "architecture": "Ampere"}, {"name": "NVIDIA A100-SXM4-80GB", "memoryTotal": "85899345920", "cudaCores": 6912, "architecture": "Ampere"}, {"name": "NVIDIA A100-SXM4-80GB", "memoryTotal": "85899345920", "cudaCores": 6912, "architecture": "Ampere"}, {"name": "NVIDIA A100-SXM4-80GB", "memoryTotal": "85899345920", "cudaCores": 6912, "architecture": "Ampere"}, {"name": "NVIDIA A100-SXM4-80GB", "memoryTotal": "85899345920", "cudaCores": 6912, "architecture": "Ampere"}, {"name": "NVIDIA A100-SXM4-80GB", "memoryTotal": "85899345920", "cudaCores": 6912, "architecture": "Ampere"}, {"name": "NVIDIA A100-SXM4-80GB", "memoryTotal": "85899345920", "cudaCores": 6912, "architecture": "Ampere"}], "cudaVersion": "12.4"}