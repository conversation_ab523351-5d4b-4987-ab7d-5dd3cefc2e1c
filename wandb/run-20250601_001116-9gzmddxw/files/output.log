Process 0: Loading model and tokenizer for max_length=8192
Process 0: DeepSpeed mode - quantization disabled for compatibility
Process 0: Tokenizer loaded with vocab size: 128258
Process 0: Loading model from meta-llama/Meta-Llama-3.1-8B
Loading checkpoint shards: 100%|██████████████████████████████████████████████████████████████████| 4/4 [00:00<00:00, 135.88it/s]
Process 0: Resizing token embeddings from 128256 to 128258
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
Process 0: Model loaded, DeepSpeed will handle device placement
Process 0: Passed model loading barrier

=== GPU Memory Usage After Model Loading ===
GPU 0: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 1: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 2: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 3: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
==================================================
Process 0: Setting up LoRA for max_length=8192
Process 0: Using LoRA target modules: ['q_proj', 'k_proj', 'v_proj', 'o_proj', 'gate_proj', 'up_proj', 'down_proj']
Process 0: LoRA setup complete, DeepSpeed will handle gradient checkpointing
trainable params: 41,943,040 || all params: 8,072,220,672 || trainable%: 0.5196
Process 0: LoRA model prepared for DeepSpeed initialization
Process 0: Passed LoRA setup barrier
Process 0: Preparing dataset junkim100/Lima-X-Processed with max_length=8192
❌ Process 0: Error during training: 'test'
Traceback (most recent call last):
  File "/data_x/junkim100/projects/translation_it/train.py", line 658, in train_model
    dataset = prepare_dataset(
  File "/data_x/junkim100/projects/translation_it/train.py", line 433, in prepare_dataset
    f"Process {rank}: Dataset loaded - Train: {len(dataset['train'])}, Test: {len(dataset['test'])}"
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/datasets/dataset_dict.py", line 81, in __getitem__
    return super().__getitem__(k)
KeyError: 'test'
